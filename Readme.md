# ECOM V2(ecom-auth-fontend) ![](https://cdnstatic.yougotagift.com/static/img/favicon.png)

YouGotaGift E-Commerce Website Login App with a new look which is more vibrant and content rich

## Documentation & Links

[Confluence Wiki](https://yougotagift.atlassian.net/wiki/spaces/YOUG/pages/1633943560/E-Comm+V2)
[JIRA](https://yougotagift.atlassian.net/jira/software/c/projects/EV/boards/71)
[Git](https://github.com/YouGotaGift/ecom_auth_frontend)

## Installation

Clone the main branch.

Install the packages.

```bash
npm install
```

Then run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Run application with local certificate

A locally generated certificate require to call some secure API's. Following steps will help to generate a
local certificate.

Install [mkcert](https://github.com/FiloSottile/mkcert) package

Generate certificate by using following command,

```bash
mkcert '*.yougotagift.com' '*.yougotagift.co' '*.qa.yougotagift.co' '*.sit.yougotagift.co' localhost 127.0.0.1 ::1
```

-   Create a folder named "ssl" at the root of the project
-   Rename the certificate to "certificate.pem"
-   Rename key file to "certificate-key.pem"
-   Move the certificates to ssl folder
-   Ensure that the ssl folder already added in the .gitignore file
-   Open server-local.js file from the root
-   Check certificates names are matched

These are the application level settings, one more step require to run the application in a local server,

-   Open terminal application, then run the following command

```bash
sudo nano /etc/hosts
```

-   Update the hostname from `localhost` to `ecom-auth.sit.yougotagift.co`
-   Save the changes and close the terminal
-   Then, run the following comman,

```bash
npm run start:local
```

-   Open the browser and take url [https://ecom-auth.sit.yougotagift.co:3000/](https://ecom-auth.sit.yougotagift.co:3000/)
-   Check the application is running with https protocol
