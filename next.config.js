/** @type {import('next').NextConfig} */
const { i18n } = require('./next-i18next.config');
const nextRewrites = require('./next.rewrites');
const { withSentryConfig } = require('@sentry/nextjs');
const isProd = process.env.NODE_ENV === 'production';
const ECOM_COGNITO_REDIRECT_URL =
    'https://ecom-auth-frontend-ev-ecomv2.sit.yougotagift.co';
const ECOM_COOKIE_DOMAIN = '.yougotagift.co';
const ECOM_DEFAULT_REDIRECT_URL =
    'https://ecom-frontend-ev-ecomv2.sit.yougotagift.co/shop';
const ECOM_DEFAULT_MWEB_REDIRECT_URL =
    'https://ecom-mweb-frontend-ev-ecomv2.sit.yougotagift.co/shop';

const ECOM_USERS_ENDPOINT =
    'https://ecom-users-eu-ecomv2.sit.yougotagift.co';
const ECOM_COGNITO_OAUTH_DOMAIN = 'login-v2.sit.yougotagift.co';
const RECAPTCHA_SITE_KEY =
    '6LcR5QcoAAAAAEDuN1ymGFjEazBQO5zwwuPqwF0U';
const GOOGLE_TAG_MANAGER_ID = 'GTM-MKF5ZQ6';
const HOTJARID = '3487555';
const FERNET_KEY = 'nAcP45pwWjGEURwWuVoAohvufHhn5P8QNxQ2e2LiJdA=';
const ANDROID_PACKAGE = 'com.yougotagift.YouGotaGiftApp.test';
const CLEVERTAPID = 'TEST-75R-466-566Z';
const FERNET_KEY_GUEST = 'ThoVesS-7EzOWZyIrnOHmK3aBWjY1XvICBA6nHAv8YM=';
const RATIFY_API_KEY = 'O8y1tp5sBbq5dw-4hbX2sWue0H224O6O0EHN60K_VOa4L';

const ECOM_USERS_API = {
    GET_TOKEN: '/users/api/v1/auth/user/get-tokens/',
    SET_TOKEN: '/users/api/v1/auth/user/set-tokens/',
    PROFILE_UPDATE_API: '/users/api/v1/auth/user/update/',
    REVOKE_TOKEN_API: '/users/api/v1/auth/user/revoke-tokens/',
    GET_PROFILE_API: '/users/api/v1/auth/user/details/',
    GENERATE_OTP: '/notifications/generate-otp/',
    VERIFY_OTP: '/notifications/verify-otp/',
    RESET_PASSWORD: '/notifications/reset-password/',
    VERIFY_EMAIL: '/users/api/v1/auth/user/generate-otp/',
    VERIFY_EMAIL_OTP: '/users/api/v1/auth/user/verify-otp/',
    SIGN_UP: '/users/api/v1/auth/user/sign-up/',
    LOGIN: '/users/api/v1/auth/user/login/',
    UPDATE_PREFILL: '/users/api/v1/app-prefill/mutate/',
    VALIDATE_EMAIL: '/users/api/v1/ratify/',
};

const ECOM_COGNITO_POOL_ID = 'us-east-2_LT3dcsfJt';
const ECOM_COGNITO_CLIENT_ID = '4halacugg93o8tbh4pqg0dc4t2';
const BUSINESS_LOGIN_URL = 'https://rewards.yougotagift.com'
const SOLUTON_HUB_URL = "https://yougotagift.com/solutions-hub"


// You can choose which headers to add to the list
const securityHeaders = [
    {
        key: 'X-DNS-Prefetch-Control',
        value: 'on',
    },
    // { // Removed this to load auth in android 8 version
    //     key: 'X-XSS-Protection',
    //     value: '1; mode=block',
    // },
    {
        key: 'X-Frame-Options',
        value: 'SAMEORIGIN',
    },
];

const moduleExports = {
    assetPrefix: isProd ? process.env.NEXT_PUBLIC_ECOM_ASSET_PREFIX : '',
    serverRuntimeConfig: {
        guestEncryptionKey:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? FERNET_KEY_GUEST
                : process.env.FERNET_KEY_GUEST,
        fernetKey:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? FERNET_KEY
                : process.env.NEXT_PUBLIC_FERNET_KEY,
        ratifyAPIKey:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? RATIFY_API_KEY
                : process.env.RATIFY_API_KEY
    },
    publicRuntimeConfig: {
        // Will be available on both server and client
        imageBaseUrl: isProd ? process.env.NEXT_PUBLIC_ECOM_ASSET_PREFIX : '',
        identityPoolId: process.env.NEXT_PUBLIC_ECOM_COGNITO_IDENTITY_POOL_ID,
        region: process.env.NEXT_PUBLIC_ECOM_COGNITO_REGION,
        userPoolId:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_COGNITO_POOL_ID
                : process.env.NEXT_PUBLIC_ECOM_COGNITO_USER_POOL_ID,
        userPoolWebClientId:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_COGNITO_CLIENT_ID
                : process.env.NEXT_PUBLIC_ECOM_COGNITO_USER_POOL_WEBCLIENT_ID,
        domain:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_COGNITO_OAUTH_DOMAIN
                : process.env.NEXT_PUBLIC_ECOM_COGNITO_OAUTH_DOMAIN,
        s3BucketName: process.env.NEXT_PUBLIC_ECOM_COGNITO_S3_BUCKET_NAME,
        redirectURL:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_COGNITO_REDIRECT_URL
                : process.env.NEXT_PUBLIC_ECOM_COGNITO_REDIRECT_URL,
        cookieDomain:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_COOKIE_DOMAIN
                : process.env.NEXT_PUBLIC_ECOM_COOKIE_DOMAIN,
        userTokenUrl:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.SET_TOKEN,
        defaultRedirectUrl:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_DEFAULT_REDIRECT_URL
                : process.env.NEXT_PUBLIC_ECOM_REDIRECT_URL,
        sendEmailOTPUrl:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.GENERATE_OTP,
        verifyOTPUrl:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.VERIFY_OTP,
        resetPasswordUrl:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.RESET_PASSWORD,
        defaultMwebRedirectUrl:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_DEFAULT_MWEB_REDIRECT_URL
                : process.env.NEXT_PUBLIC_ECOM_REDIRECT_URL,
        verifyEmailUrl:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.VERIFY_EMAIL,
        verifyEmailOTPUrl:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.VERIFY_EMAIL_OTP,
        getTokenUrl:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.GET_TOKEN,
        profileDetailsAPI:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.GET_PROFILE_API,
        sendSMSUrl:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.GENERATE_OTP,
        captchSiteKey:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? RECAPTCHA_SITE_KEY
                : process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
        userSignUpAPI:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.SIGN_UP,
        userLoginAPI:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.LOGIN,
        fernetKey:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? FERNET_KEY
                : process.env.NEXT_PUBLIC_FERNET_KEY,
        GTM_ID:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? GOOGLE_TAG_MANAGER_ID
                : process.env.NEXT_PUBLIC_GTM_ID,
        HOTJAR_ID:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? HOTJARID
                : process.env.NEXT_PUBLIC_HOTJAR_ID,
        CLEVERTAP_ACCOUNT_ID:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? CLEVERTAPID
                : process.env.NEXT_PUBLIC_CLEVERTAP_ID,
        updatePrefillAPI:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.UPDATE_PREFILL,
        androidPackage:
            process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ANDROID_PACKAGE
                : process.env.NEXT_PUBLIC_ANDROID_PACKAGE,
        validateEmailAPI:
            (process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
                ? ECOM_USERS_ENDPOINT
                : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT) +
            ECOM_USERS_API.VALIDATE_EMAIL,
        businessLoginUrl : process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
        ? BUSINESS_LOGIN_URL : process.env.NEXT_PUBLIC_BUSINESS_LOGIN_URL,
        solutionsHubURL : process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
        ? SOLUTON_HUB_URL : process.env.NEXT_PUBLIC_SOLUTIONS_HUB_URL,
    },
    async headers() {
        return [
            {
                // Apply these headers to all routes in your application.
                source: '/:path*',
                headers: securityHeaders,
            },
        ];
    },
    async redirects() {
        return [
            {
                source: '/',
                destination: '/login',
                permanent: false,
            },
        ];
    },
    images: {
        minimumCacheTTL: 300,
        domains: [
          "ygag-ecomweb-stores-qa-tf.s3.us-east-2.amazonaws.com",
          "ygag-ecomweb-stores-sandbox-tf.s3.us-east-2.amazonaws.com",
          "ygag-ecomweb-stores-qa-1-tf.s3.us-east-2.amazonaws.com",
          "ygag-ecomweb-stores-sandbox-1-tf.s3.ap-south-1.amazonaws.com",
          "cdn.stores.ecom.sandbox.yougotagift.com",
          "cdn.orders.ecom.sandbox.yougotagift.com",
          "cdn.stores.ecom.yougotagift.com",
          "ygg-ecomweb-stores-qa-2-tf.s3.me-central-1.amazonaws.com",
        ]
      },
    sentry: {
        widenClientFileUpload: true,
        hideSourceMaps: true,
    },
    i18n,
    trailingSlash: true,
    reactStrictMode: true,
    swcMinify: true,
    output: 'standalone', // *******DON'T REMOVE THIS. BUILD WILL FAIL.*************
};

const sentryWebpackPluginOptions = {
    // Additional config options for the Sentry Webpack plugin. Keep in mind that
    // the following options are set automatically, and overriding them is not
    // recommended:
    //   release, url, org, project, authToken, configFile, stripPrefix,
    //   urlPrefix, include, ignore
    url: process.env.SENTRY_URL,
    org: process.env.SENTRY_ORG,
    project: process.env.SENTRY_PROJECT,
    authToken: process.env.SENTRY_AUTH_TOKEN,
    silent: false, // Suppresses all logs
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options.
};

// Make sure adding Sentry options is the last code to run before exporting, to
// ensure that your source maps include changes from all other Webpack plugins
module.exports = withSentryConfig(moduleExports, sentryWebpackPluginOptions);
