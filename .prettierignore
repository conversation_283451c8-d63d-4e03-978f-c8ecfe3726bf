# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/deployment
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

#github
.github

# typescript
*.tsbuildinfo
next-env.d.ts

public