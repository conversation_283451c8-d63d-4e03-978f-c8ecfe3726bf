{"name": "ecom_auth_frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "start:local": "PORT=3000 node ./server-local.js", "lint": "next lint", "format": "npx prettier --write ."}, "dependencies": {"@apollo/client": "^3.7.17", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.112", "@mui/material": "^5.11.0", "@mui/x-date-pickers": "^5.0.12", "@reduxjs/toolkit": "^1.9.1", "@sentry/nextjs": "^7.6.0", "@types/lodash": "^4.14.191", "@types/node": "18.11.15", "@types/react": "18.0.26", "@types/react-dom": "18.0.9", "@types/uuid": "^9.0.0", "aws-amplify": "^5.0.7", "clevertap-web-sdk": "^1.1.2", "eslint": "8.29.0", "eslint-config-next": "13.0.6", "fernet": "^0.4.0", "fernet-nodejs": "^1.0.6", "generate-password": "^1.7.0", "graphql": "^16.7.1", "lodash": "^4.17.21", "moment": "^2.29.4", "next": "13.0.6", "next-i18next": "^13.0.2", "next-redux-cookie-wrapper": "^2.2.1", "next-redux-wrapper": "^7.0.5", "react": "18.2.0", "react-circle-flags": "^0.0.23", "react-dom": "18.2.0", "react-fast-marquee": "^1.6.5", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.41.0", "react-phone-number-input": "^3.4.12", "react-snowfall": "^1.2.1", "sass": "^1.57.0", "typescript": "4.9.4", "uuid": "^9.0.0"}, "devDependencies": {"@types/clevertap-web-sdk": "^1.1.0", "@types/fernet": "^0.4.0", "@types/react-google-recaptcha": "^2.1.5", "prettier": "2.8.3"}}