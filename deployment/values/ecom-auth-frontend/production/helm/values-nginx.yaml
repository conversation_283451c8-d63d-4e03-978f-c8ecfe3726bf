namespace: 'ecom-auth-frontend'

app_name: 'ecom-auth-frontend'
environment: 'production'

service:
  name: 'ecom-auth-frontend-nginx'
  default:
    port: 80
    protocol: 'TCP'
    targetPort: 80
  https:
    port: 443
    protocol: 'TCP'
    targetPort: 80

hpa:
  name: 'ecom-auth-frontend-nginx-hpa'
  minReplicas: 3
  maxReplicas: 20
  targetCPUUtilizationPercentage: 93

pdb:
  name: 'ecom-auth-frontend-nginx-pdb'
  minAvailable: 50%

deployment:
  name: 'ecom-auth-frontend-nginx-deployment'
  replicas: 3
  maxSurge: 100%
  maxUnavailable: 0%

  containers:
    default:
      name: 'nginx'
      imagePullPolicy: 'Always'
      image: '459037613883.dkr.ecr.ap-south-1.amazonaws.com/production/ygag/ecom-auth/frontend-nginx:[BUILD_TAG]'
      port: 80
      memory:
        requests: 32Mi
        limits: 64Mi
      cpu:
        requests: 10m
        limits: 20m
      health:
        path: '/nginx-health'
        port: 80
        scheme: 'HTTP'
      startupProbe:
        initialDelaySeconds: 3
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 5
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 4

  volumes:
    local:
      - name: 'ygag-ecom-auth-frontend-production-nginx-logs'
        mountPath: '/var/log/nginx/'
        hostPath: '/home/<USER>/ygag-logs/ygag-ecom-auth-frontend-production/nginx'

  nodeSelector:
    key: 'karpenter.sh/provisioner-name'
    value: 'default'

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: 'topology.kubernetes.io/zone'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'ecom-auth-frontend'
          tier: nginx
    - maxSkew: 1
      topologyKey: 'kubernetes.io/hostname'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'ecom-auth-frontend'
          tier: nginx

  priorityClassName: 'production-medium'
  terminationGracePeriodSeconds: 100
