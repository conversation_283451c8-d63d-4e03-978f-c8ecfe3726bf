namespace: 'ecom-auth-frontend-[JIRA_ID]'

app_name: 'ecom-auth-frontend-[JIRA_ID]'
environment: 'qa'

service:
  name: 'ecom-auth-frontend-[JIRA_ID]-app'
  default:
    port: 3000
    protocol: 'TCP'
    targetPort: 3000

enableHPA: false
hpa:
  name: 'ecom-auth-frontend-[JIRA_ID]-app-hpa'
  minReplicas: 2
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80

enableKedaCron: true
keda:
  name: 'ecom-auth-frontend-[JIRA_ID]-app-keda'
  minReplicas: 1
  desiredReplicas: 2
  maxReplicas: 2
  start: "0 7 * * 1-5"
  end: "0 22 * * 1-5"
  cpu: 80

pdb:
  name: 'ecom-auth-frontend-[JIRA_ID]-app-pdb'
  minAvailable: 0%

deployment:
  name: 'ecom-auth-frontend-[JIRA_ID]-app-deployment'
  replicas: 2
  maxSurge: 100%
  maxUnavailable: 50%
  serviceAccountName: 'ygag-ecom-auth-frontend-vault'

  containers:
    default:
      name: 'app'
      imagePullPolicy: 'IfNotPresent'
      image: '************.dkr.ecr.me-central-1.amazonaws.com/qa/ygg/ecom-auth/frontend-app:[BUILD_TAG]'
      command: '["sh", "-c" , "source /etc/profile.d/application-env.sh; node server.js"]'
      port: 3000
      memory:
        requests: 150Mi
        limits: 195Mi
      cpu:
        requests: 20m
      health:
        path: '/health/'
        port: 3000
        scheme: 'HTTP'
      startupProbe:
        initialDelaySeconds: 10
        periodSeconds: 5
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 7
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 4
      volumeMounts:
        - mountPath: '/ygag/logs/'
          name: 'ygag-ecom-auth-frontend-[JIRA_ID]-qa-app-logs'
        - name: 'ecom-auth-frontend-app-env-volume'
          mountPath: "/vault/secrets"

  volumes:
    - name: 'ygag-ecom-auth-frontend-[JIRA_ID]-qa-app-logs'
      hostPath:
        path: '/home/<USER>/ygag-logs/ygag-ecom-auth-frontend-[JIRA_ID]-qa/app'
    - name: 'ecom-auth-frontend-app-env-volume'
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: "ecom-auth-frontend-envs"

  nodeSelector:
    key: 'karpenter.sh/nodepool'
    value: 'default'

  topologySpreadConstraints:
    - maxSkew: 2
      topologyKey: 'topology.kubernetes.io/zone'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'ecom-auth-frontend-[JIRA_ID]'
          tier: app
    - maxSkew: 2
      topologyKey: 'kubernetes.io/hostname'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'ecom-auth-frontend-[JIRA_ID]'
          tier: app

  priorityClassName: 'qa-medium'
  terminationGracePeriodSeconds: 60
