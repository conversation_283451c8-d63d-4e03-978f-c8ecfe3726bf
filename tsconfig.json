{"compilerOptions": {"baseUrl": "src", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"@styles/*": ["styles/*"], "@layout/*": ["layout/*"], "@common/*": ["common/*"], "@features/*": ["features/*"], "@redux/*": ["redux/*"], "@utils/*": ["utils/*"], "@constants/*": ["constants/*"], "@graphql/*": ["graphql/*"], "@mock-data/*": ["__mockData__/*"], "@interfaces/*": ["interfaces/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}