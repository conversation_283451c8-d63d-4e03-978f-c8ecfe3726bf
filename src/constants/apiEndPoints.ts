// #. Base API url for QA and other environments
const ECOM_GRAPHQL_BASE_URL =
  "https://ecomweb-stores-cmwb-ecomv2.sit.yougotagift.co";
const ECOM_GUEST_LOGIN_URL = "https://ecom-orders-eo-ecomv2.sit.yougotagift.co";
const ECOM_USERS_URL = "https://ecom-users-eu-ecomv2.sit.yougotagift.co";
const AT_WORK_API_URL = "https://atwork-aw-ecomv2.sit.yougotagift.co";
const AT_WORK_BASE_URL = "https://atwork-frontend-aw-ecomv2.sit.yougotagift.co"







// #. API base url values are taking from env varaiable for sandbox and production. Not for QA.
export const defaultAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
    ? ECOM_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_ECOM_GRAPHQL_BASE_URL;

export const guestLoginAPIUrl = process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
? ECOM_GUEST_LOGIN_URL
: process.env.NEXT_PUBLIC_ECOM_GUEST_LOGIN_URL;

export const ecomUsersAPIUrl = process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
? ECOM_USERS_URL
: process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT;

export const atWorkAPIUrl = process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
? AT_WORK_API_URL
: process.env.NEXT_PUBLIC_AT_WORK_API_ENDPOINT;

export const atWorkBaseUrl = process.env.NEXT_PUBLIC_ENVIRONMENT === "qa"
? AT_WORK_BASE_URL
: process.env.NEXT_PUBLIC_AT_WORK_BASE_URL;