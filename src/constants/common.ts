// Platform Types
export enum PLATFORM_TYPE {
    WEB = 'WEB',
    TABLET = 'TABLET',
    MOBILE = 'MOBILE',
    MWEB = 'MWEB',
    APP = 'APP',
}

// Header Types
export enum HEADER_TYPE {
    MAJOR = 'MAJOR',
    MINOR = 'MINOR',
}

// Banner Types
export enum BANNER_TYPE {
    MAIN = 'MAIN',
    SIDE = 'SIDE',
    REWARDS = 'REWARDS',
    PROMOTIONAL = 'PROMOTIONAL',
}

// Slider Types
export enum SLIDER_TYPE {
    CATEGORY = 'CATEGORY_SLIDER',
    BRAND = 'BRAND_SLIDER',
}

// Widget Types
export enum WIDGET_TYPE {
    BLOG = 'BLOG',
    REWARDS_BANNER = 'REWARDS_BANNER',
    PROMOTIONAL_BANNER = 'PROMOTIONAL_BANNER',
    DOWNLOAD_APP = 'DOWNLOAD_APP',
}

// pages types
export enum PAGES {
    HOME = 'Home',
    ALL_BRANDS = 'All Brands',
    CART = 'Cart',
    BRAND = 'Brand',
}

// Page URL'S
export enum PAGEURLS {
    HOME = '/',
    ALL_BRANDS = '/all-brands',
    APP_DOWNLOAD = '/app-download',
    CART = '/cart',
    ABOUT_US = '/about-us',
    PRIVACY_POLICY = '/privacy-policy',
    TERMS = '/terms-of-use',
    BRAND = '/brand',
    UNSUPPORTED_COUNTRY = '/unsupported-country',
    PAYMENT_SUCCESS = '/payment/success',
    PAYMENT_FAIL = '/payment/error',
}

// Signin Signup screens
export enum SIGNIN_SIGNUP_SCREENS {
    SIGNIN_MOBILE = 'signin-mobile',
    OTP_VERIFICATION = 'otp-verification',
    ADD_INFO = 'add-info',
    ADD_INFO_WITH_EMAIL = 'add-info-with-email',
    SIGNIN_EMAIL = 'signin-email',
    CHANGE_PASSWORD = 'change-password',
    ENTER_DETAILS = 'enter-details',
    FORGOT_PASSWORD = 'forgot-password',
}

// Cognito oauth scopes
export const COGNITO_OAUTH_SCOPES = [
    'profile',
    'email',
    'openid',
    'https://www.googleapis.com/auth/user.gender.read',
    'https://www.googleapis.com/auth/user.phonenumbers.read',
    'aws.cognito.signin.user.admin',
];

// Cognito static configurations
export enum COGNITO_CONFIG {
    USER_PASSWORD_AUTH_TYPE = 'USER_PASSWORD_AUTH',
    CUSTOM_AUTH_TYPE = 'CUSTOM_AUTH',
    RESPONSE_TYPE = 'code',
    RESPONSE_SEPARATOR = '1*#*1',
}

export enum COGNITO_USER_POOL_FIELDS {
    NAME = 'name',
    EMAIL = 'email',
    PHONE = 'phone_number',
    PHONE_VERIFIED = 'phone_number_verified',
    SECURE_PAGE_ACCESS = 'custom:secure_page_access',
    FIRST_SIGN_IN = 'custom:first_sign_in',
    GENDER = 'gender',
    BIRTH_DATE = 'birthdate',
    PICTURE = 'picture',
    IDENTITIES = 'identities',
}

export enum COGNITO_USER_TYPE {
    COGNITO = 'cognito',
    GOOGLE = 'google',
    FACEBOOK = 'facebook',
    APPLE = 'signinwithapple',
    LEGACY = 'legacy',
}

export enum PROFILE_PAGES {
    EDIT_PROFILE = 'profile',
    CHANGE_PASSWORD = 'change-password',
}

export enum SIGN_IN_SLUGS {
    SIGN_IN = 'login',
    EMAIL_LOGIN = 'email-login',
    CHANGE_PASSWORD = 'change-password',
    ADD_INFO = 'add-info',
    VERIFY_OTP = 'verify-otp',
    ADD_MOBILE = 'add-mobile',
    MOBILE_LOGIN = 'mobile',
    SECURE_PAGE = 'secure-Page',
    VERIFY_EMAIL = 'verify-email',
    EMAIL_VERIFICATION = 'email-verification',
    BUSINESS_LOGIN = 'business-login',
    REWARDS_LOGIN = 'rewards-login',
    REWARDS_VERIFY_OTP = 'rewards-verify-otp',
}

export enum PASSWORD_STRENGTH {
    GOOD = 'good',
    BAD = 'bad',
}

export enum FEEDBACK_PAGES {
    FEEDBACK = 'feedback',
    SUBMIT = 'submit',
    EDIT = 'edit',
    SUCCESS = 'success',
}

export enum STATUS_CODE {
    STATUS_CODE_406 = 406,
}

export enum PAGE_FLOW {
    LEGACY_RESET_PASSWORD = 'legacy-reset-password',
    NONE = 'none',
    MOBILE_SIGN_UP = 'mobile-signup',
    EMAIL_SIGN_UP = 'email-signup',
    SOCIAL_SIGN_UP_WITH_EMAIL = 'social-signup-email',
    SOCIAL_SIGN_UP_WITHOUT_EMAIL = 'social-signup-without-email',
    LEGACY_SIGN_UP = 'legacy-signup',
    CUSTOM_SIGN_IN = 'custom-signin',
    SIGN_IN = 'signin',
    SIGN_UP = 'signup',
    GUEST = 'guest'
}

export enum AUTH_FLOW {
    NORMAL = "NormalAuthFlow",
    SOCIAL = "SocialAuthFlow"
}

export enum API_STATUS{
    USER_CREATED = 95200,
    FIELD_VERIFIED = 95203,
    OTP_GENERATED = 95201,
    OTP_GENERATED_WITH_CONTEXT_SWITCH = 95301,
    OTP_GENERATED_WITH_CONTEXT_SWITCH_EMAIL = 95309,
    USER_NOT_FOUND = 96404,
    LIMIT_EXCEEDED = 94429,
    ALREADY_EXISTS = 96408,
    CAPTCHA_FAILED = 96401,
    LOGIN_SUCCESSFULL = 96200,
    INVALID_OTP = 95204,
    LEGACY_USER_EXISTS = 96500,
    SMS_ENGINE_INVALID_CODE = 24004,
    ACCOUNT_ALREADY_EXISTS = 96408,
    USER_DOES_NOT_EXIST = 96404,
    REQUEST_BLOCKED = 94401,
    IS_WORK_EMAIL = 98403

}

export enum GRAPHQL_STATUS_CODE{
    INVALID_EMAIL = 612,
    INVALID_NAME = 611,
    EMAIL_EXISTS = 614,
    SERVER_ERROR = 500,
    UNUSUAL_ACTIVITY = 603
}

export enum BUSINESS_STATUS_CODES{
    REWARDS_USER = 48200,
    AT_WORK_USER = 96408,
    NOT_ANY_USER = 96404
}

export enum CustomSchema {
    YOUGOTAGIFT = 'yougotagift',
    INTENT = 'intent'
}

// #. User Types
export enum USER_TYPES {
    LOGGED_IN = 'Logged In',
    GUEST = 'Guest',
}

// Page URL'S
export enum PAGEURLS {
    SUSPICIOUS_ACTIVITY = "/suspicious-activity",
}

export enum EMAIL_EXISTS {
    EXISTS = 200,
    DOESNT_EXIST = 400,
    INVALID = 500
}

export enum LANGUAGES {
    EN = "en",
    AR = "ar"
}

export enum FLOW_TYPE {
    SIGN_IN = "SignIn",
    SIGN_UP = "SignUp",
}

export enum CHANNEL_TYPE {
    WHATSAPP = 'WHATSAPP',
}

export const GROUP_GIFT_APP_URL = 'https://app.groupgift.yougotagift.com/';
export const BUSINESS_APP_URL = 'https://rewards.yougotagift.com/';
export const HELP_CENTER_URL = 'https://support.yougotagift.com/hc/en-us';
export const HELP_CENTER_URL_AR = "https://support.yougotagift.com/hc/ar";
export const SNOW_ENABLED = "SNOW_ENABLED"
export const VOLUME_ENABLED = "VOLUME_ENABLED"
