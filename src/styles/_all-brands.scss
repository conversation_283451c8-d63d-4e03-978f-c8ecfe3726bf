.all-brands {
    position: relative;

    .top-bar {
        height: 88px;
        position: sticky;
        top: 88px;
        z-index: 99;
        border-bottom: 1px solid $silver;
        background-color: $white;

        @media (max-width: ( $md + 40)) {
            height: auto;
            padding: 24px 0;
        }

        .container {
            height: 100%;
            display: flex;
            justify-content: space-between;
            gap: 10px;

            @media (max-width: ( $md + 40)) {
                flex-direction: column;
                width: 100%;
                align-items: center;
            }

            .search {
                width: 100%;
                padding: 0;
                background: $white;

                .container > div {
                    border: 0 none;
                    background-color: $semi-dark-grey;
                }
            }
        }
    }

    .product-section {
        background-image: linear-gradient(
            to right,
            rgba(255, 255, 255, 0%) 50%,
            #f2f5f8 50%
        );
    }

    .highlight-title {
        color: $barney-purple;
    }

    .container.main-section {
        display: flex;

        .right-area {
            display: flex;
            flex-direction: column;
            padding: 41px 0 0 64px;
            width: 100%;
            background-color: $semi-dark-grey;

            @media (max-width: ( $md + 40)) {
                padding: 30px 0 0 30px;
            }

            @include rtl-styles {
                padding: 41px 64px 0 0;
            }
        }
    }

    .bottom-bar {
        border-top: 1px solid $very-light-grey2;
        margin-top: 3px;

        .dark-bg {
            border-top: 1px solid $light-grey;
            border-bottom: 1px solid $light-grey;
        }
    }

    .brand-filters {
        /* stylelint-disable */

        .MuiPaper-root {
            box-shadow: none;

            &.Mui-expanded {
                margin: 0;
            }

            h6 {
                margin: 10px 0;
            }
        }

        .MuiAccordion-root::before {
            display: none;
        }

        .MuiSvgIcon-root {
            font-size: 2.1rem;
            color: $black;
        }

        .MuiAccordionSummary-root {
            padding-left: 0;

            @include rtl-styles {
                padding-left: 16px;
                padding-right: 0;
            }
        }

        .MuiAccordionSummary-root.Mui-expanded {
            min-height: auto;
        }

        .MuiAccordionSummary-content {
            align-items: center;
        }

        .MuiAccordionSummary-content.Mui-expanded {
            margin: 12px 0 16px;
        }

        .MuiAccordionDetails-root {
            padding: 0;
        }

        /* stylelint-enable */
    }

    .upcoming-occasion + .brand-filters {
        @media (max-width: ( $md + 40)) {
            top: 258px;
        }
    }

    .catalog-list {
        @media (max-width: ( $md + 296)) {
            .gift-card {
                max-width: 320px;
                height: 320px;
            }

            .gift-card:hover {
                height: 340px;

                @include rtl-styles {
                    height: 350px;
                    margin-bottom: -60px;
                }
            }

            .gift-card.has-double-line:hover {
                height: 360px;
                margin-bottom: -70px;

                @include rtl-styles {
                    height: 370px;
                    margin-bottom: -80px;
                }
            }
        }

        @media (max-width: ( $md + 40)) {
            .gift-card {
                max-width: 272px;
                height: 290px;
            }

            .gift-card:hover {
                height: 315px;

                @include rtl-styles {
                    height: 310px;
                }
            }

            .gift-card.has-double-line:hover {
                height: 335px !important;
                margin-bottom: -45px;
            }
        }
    }

    .catalog-pagination {
        /* stylelint-disable */

        .MuiPagination-ul {
            justify-content: center;

            button {
                @include font-size(14);

                font-family: $default-font-family;
                font-optical-sizing: none;
                color: $warm-grey;
                background-color: rgba(92, 99, 105, 5%);
                border: 1px solid $very-light-grey;

                @include rtl-styles{
                    font-family: $arabic-font-family;
                }
            }

            .MuiPaginationItem-ellipsis {
                @include font-size(12);

                color: $warm-grey;
            }

            button.MuiPaginationItem-root {
                width: 48px;
                height: 48px;
                border-radius: 12px;
                // box-shadow: 0 30px 60px 0 rgba(92, 99, 105, 15%);
                // border: solid 1px rgba(92, 99, 105, 15%);
                border: solid 1px rgba(92, 99, 105, 0.15);
                box-shadow: 0 30px 60px 0 rgba(92, 99, 105, 0.15);
                background: rgba(92, 99, 105, 0.02);
            }

            button.Mui-selected {
                color: $black;
                font-weight: 500;
                background-color: $white;
            }

            svg.MuiPaginationItem-icon {
                width: 2rem;
                height: 2rem;
            }

            button.MuiPaginationItem-previousNext {
                @include rtl-rotate;
            }
        }

        /* stylelint-enable */
    }

    .extension-description {
        /* stylelint-disable */

        .MuiTabs-flexContainer {
            border-bottom: 3px solid #ebeded;
            float: right;

            .MuiButtonBase-root {
                font-family: $default-font-family;
                font-optical-sizing: none;
                color: $black;
                font-weight: 300;
                text-transform: none;

                @include rtl-styles{
                    font-family: $arabic-font-family;
                }

                @include font-size(14);
            }

            .Mui-selected {
                font-weight: 500;
            }
        }

        .MuiTabs-indicator {
            background-color: $barney-purple;
        }

        /* stylelint-enable */
    }
}

.filter-slider {
    .simplebar-track.simplebar-vertical {
        right: -6px !important;

        @include rtl-styles {
            right: unset !important;
            left: -6px !important;
        }
    }
}
