.payment-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 86px;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p {
        padding: 0;
        margin: 0;
    }

    .global-payment__images_container {
        display: flex;
        align-items: center;
        flex-direction: column;
        position: relative;
        height: 200px;
        width: 400px;
    }

    .global-payment__info {
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 5px;
    }

    .global-payment__extra_info {
        color: var(--warm-grey);
        font-size: 12px;
    }

    .global-payment__container {
        margin: 30px 0 10px;
        border-radius: 12px;
        border: dashed 1px var(--cool-grey);
        width: 832px;
        padding: 20px;

        @media (max-width: ( $lg + 40)) {
            max-width: 1024px;
        }

        @media (max-width: ( $md + 40)) {
            max-width: 608px;
        }
    }

    ul {
        margin: 20px 0;
        color: var(--dark-grey);
    }

    li {
        line-height: 18px;
    }

    li:not(:last-child) {
        margin-bottom: 20px;
    }

    .global-payment__span-bold {
        font-weight: 500;
    }

    .global-payment__span-purple {
        color: var(--barney-purple);
    }

    .global-payment__span-dark {
        color: var(--dark-grey);
    }

    .global-payment__help {
        font-size: 10px;
        color: var(--warm-grey);
        text-align: center;
        margin-bottom: 30px;

        @media (max-width: ( $md + 40)) {
            max-width: 608px;
            text-align: start;
            margin-bottom: 60px;
        }

        a {
            color: var(--barney-purple);
            cursor: pointer;
            text-decoration: underline;
        }
    }

    .global-payment__nav {
        display: flex;
        justify-content: center;
        gap: 20px;

        .global-payment__nav-btn {
            width: 350px;
            height: 50px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            text-transform: none;

            @media (max-width: ( $md + 40)) {
                max-width: 296px;
            }
        }

        .global-payment__primary_button {
            border: solid 1px var(--barney-purple);
            background-color: #fff;
            color: var(--barney-purple);
        }

        .global-payment__secondary_button {
            box-shadow: 0 30px 60px 0 rgba(92, 99, 105, 15%);
            background-color: var(--barney-purple);
            color: #fff;
        }
    }

    .global-payment__page-data-single,
    .global-payment__page-data-list {
        b {
            font-weight: 500;
        }

        span {
            color: var(--barney-purple);
        }
    }
}
