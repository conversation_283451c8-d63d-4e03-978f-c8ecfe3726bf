$barney-purple: #b408a4;
$strong-pink: #f07;
$very-light-pink: #ffefec;
$very-light-purple: #fae8f8;
$light-purple: #f9ebf8;
$light-pink: #ffe8fd;
$medium-light-pink: #facff5;
$error-text: #f00;
$error-border: #f07;
$red: #ff0028;
$light-red: #fee5e5;
$black: #000;
$cool-grey: #a6adb4;
$dark-grey: #202124;
$warm-grey: #808080;
$light-grey: #d8d8d8;
$pale-grey: #f6f8f9;
$hot-grey: #707070;
$cornflower-blue: #4071d9;
$white: #fff;
$golden-yellow: #fabb05;
$silver: #d9dfe4;
$very-light-grey: #ebeded;
$very-light-grey2: #ebebeb;
$very-light-grey3: #fafafa;
$medium-light-grey: #cbd0d3;
$semi-dark-grey: #f2f5f8;
$light-salmon: #ff9d98;
$dark-seafoam-green: #3dbd7d;
$border-color-sign-input: #e8ecf0;
$pale-grey-bg: #f2f5f8;
$carbon-black: #303030;
$black-header: #545454;
$dark-purple: #0e0f0c;
$semi-dark-purple: #b800c4;
$graphite-grey: #f0f0f0;
$red-secondary: #f8450b;
$grey-primary: #D9D9D9;
$dark-charcoal: #0e0f0c;
$grey-bg: #f5f5f5;
$error-red: #E74848;

// font weight
$thin: 100;
$small: 300;
$regular: 400;
$medium: 500;
$bold: 600;

// radius
$border-radius-min: 6px;
$border-radius-max: 22px;

// typo
$default-font-size: 14px;
$default-font-family: 'Bricolage Grotesque', sans-serif, 'Segoe UI', roboto, oxygen, ubuntu,
    cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
$arabic-font-family: 'Noto Kufi Arabic', sans-serif, 'Segoe UI', roboto, oxygen, ubuntu,
    cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif, 'Twemoji Country Flags';

$mona-sans-font-family : "Mona Sans", serif;

// gutter
$gutter-gap: 10px;

// mediaqueries
$xxl: 1400px;
$xl: 1320px;
$lg: 1280px;
$md: 1024px;
$sm: 767px;
$mob: 480px;