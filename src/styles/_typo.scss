@import url('https://fonts.googleapis.com/css2?family=Mona+Sans:wght@200..900&family=Cairo:wght@200;300;400;500;600;700;800;900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&&family=Noto+Kufi+Arabic:wght@100..900&display=swap');

@import 'variables';
@import 'mixins';

h1,
h2,
h3,
h4,
h5,
h6 {
    padding: 0;
    margin: 0;
}

h2 {
    @include font-size(30);

    font-weight: 500;
    line-height: 48px;
    margin: 30px 0;

    @media (max-width: ( $lg + 40)) {
        @include font-size(25);

        line-height: 38px;
    }

    @media (max-width: ( $md + 40)) {
        @include font-size(20);

        line-height: 30px;
    }
}

h3 {
    @include font-size(24);

    font-weight: 500;
    line-height: 30px;
    margin: 30px 0;
}

h4 {
    @include font-size(20);

    font-weight: 500;
    line-height: 25px;
    margin: 30px 0;
}

h5 {
    @include font-size(16);

    font-weight: 500;
    line-height: 23px;
    margin: 15px 0;
}

h6 {
    @include font-size(14);

    font-weight: 500;
    line-height: 20px;
    margin: 15px 0;
}
