@import 'icomoon';
@import 'variables';
@import 'mixins';
@import 'typo';
// @import "all-brands";
// @import "app-download";
// @import "brand";
// @import "payment";
// @import "cart";
// @import "feedback";
// @import "gift-open";

// css variables
:root {
    --barney-purple: #b408a4;
    --strong-pink: #f07;
    --very-light-pink: #ffefec;
    --very-light-purple: #fae8f8;
    --light-purple: #f9ebf8;
    --light-pink: #ffe8fd;
    --medium-light-pink: #facff5;
    --error-text: #f00;
    --error-border: #f07;
    --red: #ff0028;
    --black: #000;
    --cool-grey: #a6adb4;
    --dark-grey: #202124;
    --warm-grey: #808080;
    --light-grey: #d8d8d8;
    --pale-grey: #f6f8f9;
    --hot-grey: #707070;
    --cornflower-blue: #4071d9;
    --white: #fff;
    --golden-yellow: #fabb05;
    --silver: #d9dfe4;
    --very-light-grey: #ebeded;
    --very-light-grey2: #ebebeb;
    --very-light-grey3: #fafafa;
    --medium-light-grey: #cbd0d3;
    --semi-dark-grey: #f2f5f8;
    --light-salmon: #ff9d98;
    --dark-seafoam-green: #3dbd7d;
    --border-color-sign-input: #e8ecf0;
    --pale-grey-bg: #f2f5f8;
    --carbon-black: #303030;
    --black-header: #545454;
}

/* Chrome, Safari, Edge, Opera */
/* stylelint-disable */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type='number'] {
    -moz-appearance: textfield;
}
/* stylelint-enable */

// base
html,
body {
    padding: 0;
    margin: 0;
    font-family: $default-font-family;
    font-optical-sizing: none;
    font-weight: $regular;
    background-color: #fff;
    // background: linear-gradient(0deg, rgba(255, 255, 255, 0.20) 0%, rgba(255, 255, 255, 0.20) 100%), linear-gradient(90deg, #E8C7D7 -0.63%, #B1B8D3 100%);

    
    & [dir='rtl'] {
        font-family: $arabic-font-family;
    }
}

html {
    font-size: 62.5%;
}

body {
    @include font-size(14);

    color: $dark-grey;
}

input,
button,
select,
textarea:not(.brand-message-editor) {
    font-family: $default-font-family !important;
    font-optical-sizing: none;
    body[dir='rtl'] & {
        font-family: $arabic-font-family !important;
    }
}

[lang='en'] {
    font-family: $default-font-family !important;
    font-optical-sizing: none;
}

button[class^='icon-'],
button[class*=' icon-'] {
    /* use !important to prevent issues with browser extensions that change fonts */
    body[dir='rtl'] & {
        font-family: $icon-family;
    }
}

form {
    ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
}

a {
    color: inherit;
    text-decoration: none;

    &:hover {
        color: $barney-purple;
    }
}

* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
}

::placeholder {
    color: $very-light-grey;
}

.scroll-lock {
    overflow: hidden;
    width: 100vw;
    height: 100vh;
}

// container
main {
    min-height: 100vh;
    position: relative;

    &.home {
        background-color: $pale-grey-bg;
    }
}

.container {
    max-width: 1280px;
    width: 100%;
    margin: 0 auto;
    @media (max-width: ( $lg + 40)) {
        max-width: 1024px;
    }

    @media (max-width: ( $md + 40)) {
        max-width: 608px;
    }
}

.md-hide.md-hide {
    @media (max-width: ( $md + 40)) {
        display: none;
    }
}

.lg-hide.lg-hide {
    @media (min-width: ( $md + 41)) and (max-width: ( $lg + 40)) {
        display: none;
    }
}

.desk-hide.desk-hide {
    @media (min-width: ( $lg + 41)) {
        display: none;
    }
}

.rounded {
    border-radius: 12px;

    &-6 {
        border-radius: 6px;
    }

    &-22 {
        border-radius: 22px;
    }
}

.divider {
    border-top: solid 1px $warm-grey;
    margin: 20px 0;
    position: relative;

    .text {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        @include font-size(12);

        color: $black;
        padding: 0 25px;
        background-color: $white;
    }

    &.silver {
        border-top: solid 1px $silver;
    }

    &.silver-light {
        border-top: solid 1px rgba($silver, 0.5);
    }
}

.box {
    padding: 10px;

    &.purple {
        background: $light-purple;
        color: $black;
    }

    &.purple-light {
        background: rgba($light-purple, 0.5);
        color: $black;
    }

    &.grey {
        background-color: $pale-grey;
        color: $black;
    }
}

.paper-signin-dropdown {
    border-radius: 8px !important;
    padding: 20px;
    width: 90px;

    .Mui-selected{
        background-color: $grey-bg !important;
    }

    ul {
        padding: 0;
    }
}

.paper-signin-dropdowns {
    border-radius: 8px !important;
    border: 1px solid $grey-primary;
    box-shadow: none !important;
    margin-top: 21px;
    margin-left: 48px;

    @include rtl-styles{
        margin-left: auto;
        margin-right: 48px;
    }

    width: 260px;
    max-height: 472px !important;

    @media screen and (max-height : 850px) {
        max-height: calc(100vh - 65%) !important
    }


    ul {
        padding: 0;
    }


    // @media only screen and (max-width: $sm) {
    //     top: 207px !important;
    // }
}
// .paper-signin-dropdown--add-mobile {
//     @media only screen and (max-width: $sm) {
//         bottom: 15% !important;
//         left: 0px !important;
//     }
// }

// colors
.green {
    color: $dark-seafoam-green;

    .testimonial-rating & {
        background-color: rgba($dark-seafoam-green, 10%);
    }
}

.red {
    color: $error-text;

    .testimonial-rating & {
        background-color: rgba($error-text, 10%);
    }
}

// swiper styles over135

.swiper-quick-view {
    .swiper-pagination {
        bottom: 0 !important;

        .swiper-pagination-bullet.swiper-pagination-bullet-active {
            background-color: $barney-purple;
        }
    }
}

.swiper-item.swiper-item {
    padding-bottom: 20px;

    .swiper-pagination {
        bottom: -1px;
    }

    .swiper-pagination-bullet {
        height: 2px;
        width: 15px;
        border-radius: 0;

        &.swiper-pagination-bullet-active {
            background-color: $barney-purple;
        }
    }
}

.cards-slider {
    .swiper-button-prev,
    .swiper-button-next {
        display: none;
    }

    .swiper-slide {
        &.swiper-slide-prev {
            .gift-card-item,
            .gift-card {
                box-shadow: none;
            }
        }

        &.swiper-slide-active {
            .gift-card-item {
                box-shadow: 0 20px 20px 0 rgba(92, 99, 105, 15%);
            }
        }

        .swiper-item {
            .swiper-pagination {
                display: none;
            }
        }

        &:hover {
            .swiper-item {
                .swiper-pagination {
                    display: block;
                }
            }
        }
    }

    & + .cards-slider {
        position: relative;
        margin-top: -144px;
    }
}

/** lazy load override */
.lazyload-card-slider {
    + .lazyload-card-slider > .cards-slider {
        position: relative;
        margin-top: -144px;
    }
}

.happy-card {
    .swiper-button-prev,
    .swiper-button-next {
        display: none;
    }

    .swiper,
    .swiper-wrapper {
        width: 130%;
        left: -4%;

        .swiper-slide.swiper-slide-active {
            margin: 0 3% !important;
        }

        @include rtl-styles {
            right: -4%;
        }
    }

    .cards-slider + .lazyload-wrapper > & {
        margin-top: -113px;
    }
}

.personalization-banner {
    .cards-slider + .lazyload-wrapper > & {
        margin-top: -113px;
    }
}

.banner.banner {
    .swiper-button-next,
    .swiper-button-prev {
        height: 100%;
        top: 0;
        margin: 0;
        width: 50px;

        &::after {
            content: '';
            width: 30px;
            height: 30px;
        }
    }

    .swiper-button-next {
        right: 0;

        @include rtl-styles {
            right: auto;
            left: 0;
            transform: rotate(-180deg);
        }

        &:hover {
            background: linear-gradient(
                270deg,
                $white 0%,
                rgba($white, 0) 100%
            );
            border-bottom-left-radius: 0;
            border-top-left-radius: 0;
        }
    }

    .swiper-button-prev {
        left: 0;

        @include rtl-styles {
            left: auto;
            right: 0;
            transform: rotate(-180deg);
        }

        &:hover {
            background: linear-gradient(
                90deg,
                $white 0%,
                rgba($white, 0) 100%
            ) !important;
            border-bottom-left-radius: 0;
            border-top-left-radius: 0;
        }
    }
}

// blog swiper slider
.blog {
    .swiper-pagination-bullet.swiper-pagination-bullet {
        width: 10px;
        height: 10px;
    }

    .swiper-horizontal,
    .swiper-container-horizontal {
        & > .swiper-pagination-bullets,
        .swiper-pagination-bullets.swiper-pagination-horizontal,
        .swiper-pagination-bullets.swiper-pagination {
            width: auto;
            right: 28px;
            top: 40px;
            bottom: auto;
            left: auto;

            @media (max-width: ( $md + 40)) {
                top: 290px;
            }
        }
    }
}

.featured-in {
    .marquee {
        min-width: auto;
        justify-content: space-around;
    }
}

.about-us {
    position: relative;
    border-bottom: 1px solid #ebebeb;

    .main-section {
        display: flex;
        height: 100%;
        gap: 10px;

        // @media (max-width: ( $md + 40)) {
        //   flex-direction: column;
        //   width: 100%;
        //   align-items: center;
        // }

        .right-area {
            display: flex;
            flex-direction: column;
            padding: 30px 0 100px 64px;

            @media (max-width: ( $md + 40)) {
                padding: 30px 0 100px 30px;
            }
            @include rtl-styles {
                padding: 30px 64px 100px 0;
            }
        }
    }
}

/* Signin Signup */
.signin-signup {
    .submit-button {
        border-radius: 8px;
        background: $dark-charcoal;
        // padding: 15.5px;
        width: 100%;
        height: 50px;
        @include font-size(16);

        font-family: "Mona Sans" !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px; /* 150% */
        letter-spacing: -0.16px;

        @include rtl-styles{
            font-family: $arabic-font-family;
        }

        &:focus-visible {
            outline: none;
        }
    }

    .button-container, .button-container-email, .button-container-email-mweb {
        display: flex;
        justify-content: center;

        .button-submitted {
            outline: none;
            width: 40px !important;
            height: 40px;
            text-align: center;
            border-radius: 40px;
            background: $white;
            border: 4px solid $barney-purple;
            border-color: $warm-grey;
            font-size: 0 !important;
            border-left-color: $barney-purple;
            transition: all 0.25s ease;
            animation: rotating 2s 0.25s linear infinite;
            padding: 0 !important;
            min-width: auto;
            display: flex;
            justify-content: center;

            span{
                display: none;
            }

            &::before {
                content: '';
            }

            @include rtl-styles {
                margin-left: 0;
                //margin-right: 70px;
            }
        }
    }

       

    @keyframes rotating {
        from {
            transform: rotate(0deg);
        }

        to {
            transform: rotate(360deg);
        }
    }
}

.profile {
    .button-submitted {
        outline: none;
        width: 40px !important;
        height: 40px;
        text-align: center;
        border-radius: 40px;
        background: $white;
        border: 4px solid $barney-purple;
        border-color: $warm-grey;
        font-size: 0 !important;
        border-left-color: $barney-purple;
        transition: all 0.25s ease;
        animation: rotating 2s 0.25s linear infinite;
        padding: 0 !important;
        min-width: auto;
        display: flex;
        justify-content: center;

        &::before {
            content: '';
        }

        @include rtl-styles {
            margin-left: 0;
            margin-right: 70px;
        }
    }
}

// MUI theme overighting

/* stylelint-disable */
.MuiPaper-rounded.MuiPaper-rounded {
  border-radius: 12px;
}


.MuiDialogTitle-root.MuiDialogTitle-root {
    padding: 0;
    margin: 0;
}

.MuiDialog-paper.MuiDialog-paper {
    max-width: 832px;
}

.warning{
    .MuiPaper-root{
        margin: 15px !important;
    }
}

.MuiBadge-root.MuiBadge-root {
    position: relative;

    .MuiBadge-badge {
        z-index: auto;
        min-width: 20px;
        height: 20px;
        color: $white;
        font-weight: 400;
        @include font-size(10);

        line-height: 21px;
        white-space: nowrap;
        text-align: center;
        background: $barney-purple;
        border-radius: 12px;
        position: absolute;
        right: -15px;
        top: -15px;

        @include rtl-styles {
            right: auto;
            left: -15px;
        }
    }
}

.search {
    .MuiAutocomplete-root {
        width: 100%;

        input {
            padding: 7.5px 22px 7.5px 22px !important;
            height: 17px;
            margin: 0 10px;
        }
    }

    fieldset {
        display: none;
    }

    .MuiAutocomplete-endAdornment {
        @include rtl-styles {
            right: auto;
            left: 9px;
        }
    }

    .MuiAutocomplete-clearIndicator {
        visibility: hidden !important;

        svg {
            width: 2rem;
            height: 2rem;
            color: $black;
        }
    }

    // #. Remove mask icon from the Safari
    input::-webkit-credentials-auto-fill-button {
        mask-size: 0px !important;
    }
}

.search + .widgets .reward-business {
    margin-top: 30px;
}

.search-autocomplete-papper {
    border-radius: 0 0 12px 12px !important;
    border: 1px solid $light-grey;
    margin: 0 -3px 0 4px;
    padding: 0 25px;
    box-shadow: 0 14px 20px 0 rgba(0, 0, 0, 0.16) !important;
    font-family: $default-font-family;
    font-optical-sizing: none;

    @include rtl-styles {
        margin: 0 4px 0 -3px;
        font-family: $arabic-font-family;
    }

    .MuiAutocomplete-option {
        padding: 11px 16px;

        &.Mui-focused {
            background-color: $light-purple !important;
        }

        img {
            @include rtl-styles {
                margin: 0 0 0 16px;
            }
        }
    }

    .MuiAutocomplete-noOptions {
        color: $black;

        @include font-size(16);
    }

    .search-info-container {
        display: block;

        .title {
            color: $black;

            @include font-size(16);

            em {
                font-weight: 700;
                font-style: normal;
            }
        }

        .description {
            color: $warm-grey;

            @include font-size(12);
        }
    }
}

// minor header global
.header-popover {
    top: 35px !important;

    .MuiPopover-paper {
        box-shadow: 0 20px 60px 0 rgba($black, 0.16);
        border: solid 1px $silver;
        border-radius: 0 0 12px 12px;
        top: 0 !important;
    }

    .MuiBackdrop-root {
        top: 35px !important;
    }
}

// quick view panel accordion component style override
.quickview-accordion {
    .MuiPaper-root {
        box-shadow: none;
        border-bottom: 1px solid $silver;

        &.Mui-expanded {
            margin: 0;
        }

        h6 {
            margin: 10px 0;
        }

        p {
            margin: 0;
        }
    }

    .MuiAccordion-root::before {
        display: none;
    }

    .MuiSvgIcon-root {
        font-size: 1.75rem;
        color: $black;
    }

    .MuiButtonBase-root.MuiAccordionSummary-root {
        padding: 0;
    }

    .MuiAccordionSummary-root.Mui-expanded {
        min-height: auto;
    }

    .MuiAccordionSummary-content {
        align-items: center;
    }

    .MuiAccordionSummary-content.Mui-expanded {
        margin: 12px 0 20px;
    }

    .MuiAccordionDetails-root {
        padding: 0 0 20px;
    }
}

// logout accordion component style override
.logout-accordion {
    .MuiPaper-root {
        box-shadow: none;

        &.Mui-expanded {
            margin: 0;
        }

        h6 {
            margin: 10px 0;
        }

        p {
            margin: 0;
        }
    }

  .MuiAccordion-root::before {
    display: none;
  }
  .MuiPaper-rounded.MuiPaper-rounded {
    border-radius: 0;
    background-color: #f6f8f9;
  }

    .MuiSvgIcon-root {
        margin: 0 15px;
        font-size: 2rem;
        color: $black;
    }

    .MuiButtonBase-root.MuiAccordionSummary-root {
        background-color: #f6f8f9;
        padding: 0;
        display: inline-flex;
        // width: 100%;
    }

    .MuiAccordionSummary-root.Mui-expanded {
        min-height: auto;
    }

    .MuiAccordionSummary-content {
        align-items: center;
    }

    .MuiAccordionSummary-content.Mui-expanded {
        margin: 12px 0 20px;
    }

    .MuiAccordionDetails-root {
        padding: 0 5px 5px;
    }
}

.quickview-crossbrands {
    .MuiTypography-root {
        font-family: $default-font-family !important;
        font-optical-sizing: none;
        @include rtl-styles {
            font-family: $arabic-font-family !important;
            text-align: right;
        }
    }

    .MuiListItemText-primary {
        @include font-size(16);

        font-weight: 500;
    }

    .MuiListItemText-secondary {
        @include font-size(14);

        color: $warm-grey;
        font-weight: 200;
    }
}
.download-app-input
    .MuiOutlinedInput-notchedOutline.MuiOutlinedInput-notchedOutline {
    border: 0;
}

.skip-rtl-style {
    @include rtl-styles {
        direction: ltr;
        display: inline-block;
    }
}

.MuiTypography-root,
.MuiInputLabel-formControl {
    font-weight: 500 !important;
    font-size: 14px !important;
    font-family: $default-font-family !important;
    font-optical-sizing: none;
    @include rtl-styles {
        font-family: $arabic-font-family !important;
    }
}

.MuiPickersToolbar-penIconButton{
    display: none !important;
}

.phone-verification {
    .MuiInputLabel-formControl {
        font-weight: $regular;
        color: $dark-grey;
        @include font-size(16);

        &.MuiInputLabel-shrink {
            @include font-size(12);
        }
    }

    .MuiSelect-select {
        display: grid;
        grid-template-columns: 17px auto;
        gap: 10px;
        min-height: 23px;

        .MuiListItemIcon-root {
            display: flex;
            align-items: center;
            img {
                width: 17px;
                height: 13px;
            }
        }

        .MuiListItemText-root {
            margin: 0;

            .MuiTypography-root {
                @include font-size(16);
            }
        }
    }

    .MuiSvgIcon-root {
        top: 12px;
        font-size: 30px;
        color: $dark-charcoal;
        padding-left: 4px;

        @include rtl-styles{
            right:auto;
            left: 0;
        }
    }

    .MuiInput-input.MuiSelect-select {
        @include rtl-styles{
            padding-right: 0;
    padding-left: 22px;
        }
    }

    .MuiInput-input {
        @include font-size(16);
        min-width: 65px !important;
        align-items: center;
    }
    .country-select {
        margin-top: 27px !important;
    }

    .Mui-error {
        color: $error-text !important;;
        font-family: $default-font-family !important;;
        font-optical-sizing: none;
        @include font-size(12);

        @include rtl-styles {
            font-family: $arabic-font-family !important;;
        }

        &.MuiFormHelperText-root {
            padding: 0;
            margin: 10px 0 0 !important;;
        }

        &::after {
            border-color: $error-border !important;;
        }
    }
}

.css-bkrceb-MuiButtonBase-root-MuiPickersDay-root,
.css-l0iinn {
    @include font-size-important(14);
}

.signin-signup {
    .MuiInput-underline:not(.Mui-error) {
        &::before {
            border-color: $border-color-sign-input;
            border: 0px;
        }
        &:hover:not(.Mui-disabled):before {
            border-bottom-width: 1px;
        }
        &:hover:not(.Mui-disabled):before,
        &::after {
            border-color: $barney-purple;
            border: 0px;
        }
    }
    .Mui-error {
        color: $error-text;
        font-family: $mona-sans-font-family;
        @include font-size(12);

        @include rtl-styles {
            font-family: $arabic-font-family;
        }

        &.MuiFormHelperText-root {
            padding: 0;
            margin: 5px 0px 0px 5px;
        }

        &::after {
            border-color: $error-border;
        }
    }
}

.add-info{
    .Mui-error {
        &.MuiFormHelperText-root {
            margin: -8px 0px 0px 0px;
        }
    }
}

.MuiInputBase-formControl {
    font-family: $default-font-family;
    font-optical-sizing: none;

    @include rtl-styles {
        font-family: $arabic-font-family;
    }
}

// hack for zendesk chat
#launcher {
    transition: transform 225ms cubic-bezier(0, 0, 0.2, 1) 0ms !important;
}

.side-menu-opened {
    #launcher {
        transform: translateX(-520px);
    }
}

/*Simple scroll bar plug-in styles*/
.simplebar-track.simplebar-vertical {
    width: 7px !important;
}

.country-phone-code {
    ul {
        li.Mui-selected {
            background-color: $grey-bg !important;
        }
    }

    .MuiSelect-select:focus{
        background: none;
    }
}

.PrivateDatePickerToolbar-penIcon {
    display: none;
}

// #. Hide recaptcha bade
// .grecaptcha-badge {
//     visibility: hidden;
//     opacity: 0;
// }

// mobile responsiveness
.mb-show {
    display: none;
    @media only screen and (max-width: $sm) {
        display: initial;
    }
}

.mb-hide {
    @media only screen and (max-width: $sm) {
        display: none;
    }
}

.brand-send-name-field .MuiFormHelperText-root {
    position: absolute;
    bottom: -30%;
    color: red;
}
.brand-send-email-field .MuiFormHelperText-root {
    color: red;
    position: absolute;
    bottom: -30%;
}
.brand-send-phone-text .MuiFormHelperText-root {
    position: absolute;
    bottom: -30%;
    color: red;
}

//common page layout
.indipendent-layout {
    &__header {
        height: 70px;
        padding: 10px 0;
        box-sizing: border-box;
        background-color: $white;
        display: flex;
        justify-content: center;

        a {
            display: flex;
            align-items: center;
        }
    }

    &__footer {
        direction: ltr;
        text-align: center;
        padding-top: 34px;
        padding-bottom: 45px;
        border-top: 1px #ebebeb solid;
        font-size: 12px;
        height: 96px;
        box-sizing: border-box;
        font-weight: normal;
        color: var(--warm-grey);
    }

    &__children {
        height: calc(100vh - 146px);
    }
}

// Error page styles
.error-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: $pale-grey;
    padding: 70px 0;
    height: 100%;

    h2 {
        @include font-size(34);

        margin: 0;

        @media (max-width: ($mob)) {
            @include font-size(24);
        }
    }

    h5 {
        font-weight: 400;
        margin-bottom: 30px;

        @media (max-width: ($md)) {
            text-align: center;
            width: 70%;
        }

        @media (max-width: ($mob)) {
            @include font-size(12);
        }
    }

    button.error-goto-home {
        color: $barney-purple;
        border: 1px solid $barney-purple;
        margin: 30px 0;
        padding: 7px 58px;
        border-radius: 12px;
        text-transform: none;

        @include font-size(14);

        @media (max-width: ($mob)) {
            @include font-size(12);
        }
    }

    .error-message {
        margin-top: 40px;
        @include font-size(16);

        color: $warm-grey;
    }
}

// #. Confirm Modal

.login-require-confirm {
    .MuiPaper-root.MuiPaper-rounded {
      border-radius: 16px;
      width: 610px;
    }

    .confirm-dialog-icon {
      width: 25px;
      height: 25px;
    }

    .confirm-dialog-section {
      padding: 4px 0 40px 0 !important;
      color: $dark-purple;
      @media only screen and (max-width: $sm) {
        padding: 4px 0 26px 0 !important;
     }
    }

    .confirm-dialog-title {
      font-size: 20px !important;
      font-weight: 600 !important;
      margin-bottom: 16px !important;
      @media only screen and (max-width: $sm) {
        font-size: 16px !important;
        margin-bottom: 7px !important;
     }
    }

    .confirm-dialog-content {
      font-size: 16px !important;
      color: var(--black-header);
      padding: 0 !important;
      margin-bottom: 0 !important;
      @media only screen and (max-width: $sm) {
        font-size: 14px !important;
      }
    }

    .confirm-dialog-buttons {
      padding: 0;
      gap: 12.5px;

      button {
        padding: 12px 50px;
        font-size: 16px;
        font-weight: 500;

        @include rtl-styles {
          padding: 9px 63px;
        }
      }

      .confirm-dialog-button-confirm.confirm-dialog-button-confirm {
        border: 1px solid $barney-purple !important;
        box-shadow: none !important;
      }
    }
  }


  .fb-warning-dialog{
    .MuiPaper-root.MuiPaper-rounded{
        position: absolute;
        top: 100px !important;

        @media (max-width: ($sm)) {
            position: relative;
            margin-bottom: 170px !important;
            border-radius: 24px 24px 0 0;
        }
    }
  }

  .MuiDialog-root.fb-warning-dialog{
        right: -31px !important;
        bottom: -160px !important;
        left: -31px !important;

        // @media (max-width: ($sm)) {
        //     bottom: -20% !important;
        // }

        // @media (max-height: (670px)) {
        //     bottom: -45% !important;
        // }

  }


  .highlight-helpCenter{
    color: $barney-purple;
}

.MuiDialog-root.existing-email-dialog {
    @media (max-width: ($sm)) {
    right: -31px !important;
    left: -31px !important;
    }
}
.existing-email-dialog{
    .MuiPaper-root.MuiPaper-rounded{

        @media (max-width: ($sm)) {
            border-radius: 24px 24px 0 0;
            position: absolute;
            bottom: -33px;
            width: 100vw;
        }
    }
  }

// #. OTP Input 

.otp-input-field {
    width: 74px !important;
    height: 70px;
    border-radius: 12px;
    border: solid 1px $grey-primary;
    color: $dark-purple;
    margin: 0;

    @media only screen and (max-width: $sm) { 
        width: 43.6px !important;
        height: 50px;
    }
}

.otp-input-field:focus-visible {
    outline: none;
    border: solid 1px rgba(84, 84, 84, 0.5);
}

.otp-input-field-error { 
    width: 74px !important;
    height: 70px;
    border-radius: 12px;
    border: solid 1px $red-secondary;
    color: $red-secondary;
    margin: 0;

    @media only screen and (max-width: $sm) { 
        width: 43.6px !important;
        height: 50px;
    }
}

.otp-input-field-error:focus-visible {
    outline: none;
    border: solid 1px $red-secondary;
}

.otp-input-field-error::placeholder {
    color: $red-secondary !important;
}

.white-divider{
    width: 528px;
    height: 1px;
    background: $white;

    @media only screen and (max-width: $sm) { 
        width: 100%;
    }


    @media only screen and (max-height: 650px){
        width: 0;
    }
}

.button-container-email-mweb{
    display: none !important;
}


@media only screen and (max-width: $sm) {
    .button-container-email{
        display: none !important;
    }

    .button-container-email-mweb{
        display: block !important;
        margin-top: 16px;
    }
}

.width-100{
    width: 100% !important;
}

.error-outline{
    border-color: $error-red !important;
}

.confirm-dialog{
    .MuiDialog-paper{
        position: absolute;
        top: 90px;

        @media only screen and (max-width: $sm) { 
            top: unset;
        }

    }
    .MuiBackdrop-root{
        background: rgba(0, 0, 0, 0.60);
        /* Blur/Background Blur */
        backdrop-filter: blur(3px);
    }
}



.add-info{
    .MuiSelect-select:focus{
        background: none;
    }
}