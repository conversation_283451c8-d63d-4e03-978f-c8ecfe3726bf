$mob-view-max-width: 375px;
$mob-view-max-height: 793px;

.gift-open {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;

    &__container {
        width: $mob-view-max-width;
        height: $mob-view-max-height;
        background-color: $white;
        border-radius: 24px;
        box-shadow: 0 10px 20px 0 rgba(128, 128, 128, 12%);

        &-public {
            height: 711px !important;
            min-height: 711px !important;
            max-height: 711px !important;
        }

        @include for-tablet-device-only {
            min-width: $mob-view-max-width !important;
            max-width: $mob-view-max-width !important;
            min-height: $mob-view-max-height !important;
            max-height: $mob-view-max-height !important;
            border-radius: 24px;
        }

        @include for-mobile-phone-only {
            min-width: 100% !important;
            max-width: 100% !important;
            min-height: 100vh !important;
            max-height: 100vh !important;
            border-radius: 0;
            overflow: hidden;
        }

        @include for-landscape-orientation {
            @include for-mobile-phone-only {
                max-width: $mob-view-max-width !important;
                max-height: $mob-view-max-height !important;
                min-width: auto !important;
                min-height: auto !important;
                border-radius: 24px;
            }
        }
    }

    @include for-landscape-orientation {
        padding: 24px;
    }
}

.gift-open__children {
    height: calc(100% - 110px);
}

.gift-open__children.stories_children {
    height: calc(100% - 70px);
}

.opening-introduction {
    button {
        .button-label.button-label {
            padding: 10px;
        }

        .button-icon-wrapper.button-icon-wrapper i {
            @include font-size(12);

            font-weight: 800;
        }
    }
}

.stories-display {
    .prev-story.swiper-button-disabled {
        display: none;
    }
}

.story-area {
    display: flex;
    flex-direction: column;
}

// No-header layout
.gift-open__children.gift-open__no-header {
    height: 100%;
}

.gift-details-area {
    height: 100%;
    overflow: auto;
}

.align-right {
    text-align: right;

    @include rtl-styles {
        text-align: left;
    }
}

.gift-details__welcome {
    img {
        border-radius: 65px;

        @include for-mobile-phone-only {
            border-radius: 0;
        }
    }
}

.phonenumber-submit {
    .MuiInputBase-root {
        color: $warm-grey;
        font-weight: 500;
        height: 50px;

        @include font-size(16);
    }

    fieldset {
        border: 0;
    }
}
