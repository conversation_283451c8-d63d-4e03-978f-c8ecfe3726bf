.brand-page {
    padding: 40px 0;

    .main-section {
        width: 100%;
        min-height: 300px;
        display: flex;
    }

    .half-width {
        width: 50%;

        @media (max-width: ($md + 40)) {
            width: 30%;
        }
    }

    .left-section {
        padding: 0 45px;

        @media (max-width: ($xl + 40)) {
            padding: 0;
        }

        @media (max-width: ($md + 40)) {
            padding: 0;
        }
    }

    .left-sticky {
        position: sticky;
        top: 150px;
    }

    .right-section {
        padding-left: 30px;
        width: 50%;

        @include rtl-styles {
            padding-left: 0;
            padding-right: 30px;
        }
        @media (max-width: ($md + 40)) {
            width: 70%;
        }
    }

    .bottom-section {
        border-top: 1px solid $very-light-grey2;
        border-bottom: 1px solid $very-light-grey2;
        width: 100%;
        margin-top: 100px;
    }

    .brand-accordion,
    .brand-description {
        .MuiPaper-root {
            box-shadow: none;
            border-bottom: 1px solid $silver;
            border-radius: 0 !important;

            &.Mui-expanded {
                margin: 0;
            }

            h6 {
                margin: 10px 0;
            }

            p {
                margin: 0;
            }
        }

        .MuiAccordion-root::before {
            display: none;
        }

        .MuiSvgIcon-root {
            font-size: 1.75rem;

            // color: $black;
        }

        .MuiButtonBase-root.MuiAccordionSummary-root {
            padding: 0;
        }

        .MuiAccordionSummary-root.Mui-expanded {
            min-height: auto;
        }

        .MuiAccordionSummary-content {
            align-items: center;
        }

        .MuiAccordionSummary-content.Mui-expanded {
            margin: 12px 0 20px;
        }

        .MuiAccordionDetails-root {
            padding: 0 0 20px;
        }
    }

    .brand-accordion {
        .MuiAccordion-root.Mui-expanded {
            border-bottom: 0 none;
        }
    }

    .brand-value {
        /* stylelint-disable */

        .MuiInputLabel-root,
        .MuiInputLabel-root.Mui-focused {
            color: $warm-grey !important;
            font-weight: $small !important;

            @include rtl-styles {
                display: contents;
                font-size: 10px;
            }
        }

        .MuiInput-root.MuiInputBase-root {
            &::before {
                border-bottom: 1px solid $very-light-grey !important;
            }

            &::after {
                border-bottom: 1px solid $very-light-grey !important;
            }

            input {
                @include font-size(24);

                font-weight: 600;
                color: $black;
            }
        }

        .MuiFormHelperText-root {
            @include font-size(10);

            font-family: $default-font-family;
            font-optical-sizing: none;
            color: $error-text;
        }

        .MuiInputAdornment-root {
            p {
                font-size: 24px !important;
                font-weight: 600 !important;
                font-family: $default-font-family !important;
                font-optical-sizing: none;
                color: $black;

                @include rtl-styles {
                    font-weight: 600 !important;
                    padding: 0px 0px 0px 8px;
                    font-family: $arabic-font-family;
                }
            }
        }

        /* stylelint-enable */
    }

    .brand-order-type {
        /* stylelint-disable */
        .MuiRadio-root {
            color: $cool-grey !important;
        }

        .MuiRadio-root.Mui-checked {
            color: $barney-purple !important;
        }

        .MuiSelect-select {
            display: flex;
            gap: 8px;
            align-items: center;
            img {
                width: 17px;
                height: 17px;
            }
        }

        .MuiPaper-rounded.MuiPaper-rounded {
            border-radius: 0 !important;
        }

        .MuiOutlinedInput-root {
            border-radius: 12px !important;
        }

        .MuiPickerStaticWrapper-content {
            width: 321px;
        }

        .MuiInputBase-formControl {
        }

        .MuiOutlinedInput-root {
            &.Mui-focused {
                border: 1px solid #b408a4;
            }
        }

        .MuiInputBase-root {
            border: 1px solid #d9dfe4;
            background-color: #fff;
        }

        .MuiOutlinedInput-notchedOutline.MuiOutlinedInput-notchedOutline {
            border: none;
        }

        .MuiSvgIcon-root {
            height: 20px;
            width: 20px;
            color: default !important;
        }
        /* stylelint-enable */
    }
}

.brand-order {
    .MuiRadio-root {
        color: $cool-grey !important;
    }

    .MuiRadio-root.Mui-checked {
        color: $barney-purple !important;
    }

    .MuiSvgIcon-root {
        height: 20px;
        width: 20px;
        color: default !important;
    }
}

/* greetings mui override */
.greetings {
    /* stylelint-disable */
    .MuiOutlinedInput-root {
        border-radius: 12px;
        height: 44px;

        &.Mui-focused {
            border: 1px solid #b408a4;
        }
    }

    .MuiInputBase-root {
        border: 1px solid #d9dfe4;
    }

    .MuiSelect-select {
        font-family: 'Poppins';
        font-size: 14px;
        display: flex;
        align-items: center;
        font-weight: 500;
    }

    .MuiOutlinedInput-notchedOutline.MuiOutlinedInput-notchedOutline {
        border: none;
    }

    .MuiPaper-root {
        border-radius: 12px !important;
    }

    .MuiMenuItem {
        display: none;
        background-color: #b408a4 !important;
    }

    .MuiSvgIcon-root {
        font-size: 1.45rem !important;
    }

    .swiper-button-prev {
        width: 65px;
        height: 65px;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #b408a4;
        position: absolute;
        -webkit-box-shadow: -8px 0 6px 0 rgba(0, 0, 0, 16%);
        box-shadow: -8px 0 6px 0 rgba(0, 0, 0, 16%);
        transform: rotate(180deg);
        left: -27px;

        @include rtl-styles {
            right: -33px !important;
            transform: rotate(360deg);
            margin-right: 7px !important;
            margin-left: 0;
        }

        &::after {
            display: none;
        }

        img {
            margin-left: -7px;
            display: block;
        }
    }

    .swiper-button-next {
        height: 65px;
        width: 65px;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #b408a4;
        position: absolute;
        box-shadow: -8px 0 6px 0 rgba(0, 0, 0, 16%);
        right: -27px;

        @include rtl-styles {
            left: -27px !important;
            transform: rotate(180deg);
            margin-right: 7px !important;
            margin-left: 0;
        }

        &::after {
            display: none;
        }

        img {
            margin-left: -7px;
            display: block;
        }
    }
    /* stylelint-enable */
}

.brand-photo-editor-tool {
    width: 1056px;
    height: 525px;

    /* stylelint-disable */
    [data-test='MainBarButtonClose'] {
        display: none;
    }
    /* stylelint-enable */
}

.brand-video-editor-tool {
    width: 784px;
    height: 525px;
    overflow: hidden;

    @media (max-width: ( $md + 40)) {
        width: 678px !important;
    }

    @media (max-width: ( $md - 240)) {
        width: 500px !important;
    }

    .camera_tag {
        width: 100% !important;
        height: 100% !important;
    }
}

.brand-send-to {
    .MuiInput-underline:not(.Mui-error) {
        &::before {
            border-color: $border-color-sign-input;
        }

        &:hover:not(.Mui-disabled)::before {
            border-bottom-width: 1px;
        }

        &:hover:not(.Mui-disabled)::before,
        &::after {
            border-color: $barney-purple;
        }
    }

    .MuiInput-input.MuiSelect-select {
        @include font-size(14);
    }

    .MuiSelect-icon {
        margin-top: -1px !important;
    }
}

.flag-select {
    input {
        display: none;

        @include font-size(16);

        margin: 0;
        border: none;
    }

    .MuiInput-underline:not(.Mui-error) {
        &::before {
            opacity: 0;
            border-color: $border-color-sign-input;
        }

        &:hover:not(.Mui-disabled)::before {
            opacity: 0;
            border-bottom-width: 1px;
        }

        &:hover:not(.Mui-disabled)::before,
        &::after {
            opacity: 0;
            border-color: $barney-purple;
        }
    }

    .MuiSvgIcon-root {
        font-size: 12px;
        margin: 0 8px 0 2px;

        @include rtl-styles {
            margin: 0 2px 0 8px;
        }
    }

    button {
        svg {
            border-radius: 3px;
            width: 30px;
            height: 30px;

            @include rtl-styles {
                margin-right: -14px;
            }
        }
    }
}

.brand-button {
    height: 75px;
    display: flex;
    align-items: center;
    gap: 24px;

    &__continue {
        @include font-size(16);

        font-weight: 500;
        height: 50px;
        width: 100%;
    }

    &__cart {
        @include font-size(16);

        font-weight: 500;
        height: 50px;
        border: 1px solid $barney-purple !important;
        color: $barney-purple !important;
    }

    &__half {
        width: 50%;
    }

    &__skip {
        @include font-size(16);

        font-weight: 500;
        height: 50px;
        width: 100%;
        text-align: center;
        color: $barney-purple;
        margin-top: 10px;
        cursor: pointer;
    }

    &__disabled {
        opacity: 0.3;
        pointer-events: none;
    }
}

.notifier {
    .checked-icon {
        width: 30px;
        height: 30px;
        background-color: $white;
        color: $carbon-black;
        border-radius: 50%;
        padding: 5px;
    }

    .error-outlined-icon {
        width: 30px;
        height: 30px;
        color: $white !important;
        background-color: transparent;
        border-radius: 50%;
    }
}

.phone-code-select {
    .MuiPaper-rounded.MuiPaper-rounded {
        border-radius: 12px;
        max-height: 300px;
    }
}

.MuiMenuItem-root {
    font-size: 15px !important;
    font-family: 'Poppins';
}

.time-picker {
    @include rtl-styles {
        .MuiOutlinedInput-input {
            direction: ltr !important;
            text-align: end !important;
        }

        .MuiCalendarPicker-root {
            direction: ltr !important;
        }
    }
}
