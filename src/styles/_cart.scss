.delete-cart-confirm.confirm-dialog {
    .MuiPaper-root.MuiPaper-rounded {
        border-radius: 16px;
        width: 385px;
    }

    .confirm-dialog-icon {
        width: 25px;
        height: 25px;
    }

    .confirm-dialog-section {
        padding: 23px 0;
        color: $dark-purple;
    }

    .confirm-dialog-title {
        font-size: 16px !important;
    }

    .confirm-dialog-content {
        font-size: 14px !important;
        padding: 12px 0;
        width: 80%;

        @include rtl-styles {
            width: 70%;
        }
    }

    .confirm-dialog-buttons {
        padding: 0;
        gap: 12.5px;

        button {
            padding: 12px 50px;
            font-size: 16px;
        }
    }
}

.flip-x-anim {
    animation: 1s anim-flip-x ease infinite;
}

@keyframes anim-flip-x {
    0% {
        opacity: 0;
        transform: rotateX(90deg);
    }

    50% {
        opacity: 1;
        transform: rotateX(720deg);
    }

    100% {
        /* animate nothing to pause animation at the end */
        opacity: 1;
        transform: rotateX(720deg);
    }
}
