@charset "UTF-8";

$icon-family: icomoon, arial, helvetica, sans-serif !important;

[class^='icon-'],
[class*=' icon-'] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: $icon-family;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-happy::before {
    content: '\e900';
}

.icon-not-happy::before {
    content: '\e901';
}

.icon-very-happy::before {
    content: '\e902';
}

.icon-logout::before {
    content: '\e903';
}

.icon-orders::before {
    content: '\e904';
}

.icon-payments::before {
    content: '\e905';
}

.icon-menu-icon::before {
    content: '\e906';
}

.icon-facebook-logo::before {
    content: '\e907';
}

.icon-linkedin-logo::before {
    content: '\e908';
}

.icon-youtube::before {
    content: '\e909';
}

.icon-arrow-forward::before {
    content: '\e90a';
}

.icon-checkbox-on-2::before {
    content: '\e90b';
}

.icon-close::before {
    content: '\e90c';
}

.icon-right-arrow::before {
    content: '\e90d';
}

.icon-dropdown::before {
    content: '\e90e';
}

.icon-profile::before {
    content: '\e90f';
}

.icon-home::before {
    content: '\e910';
}

.icon-gift::before {
    content: '\e91c';
}

.icon-search::before {
    content: '\e926';
}

.icon-instagram::before {
    content: '\e927';
}

.icon-line-chart::before {
    content: '\e928';
}

.icon-mail::before {
    content: '\e929';
}

.icon-e-mail::before {
    content: '\e92c';
}

.icon-quotes::before {
    content: '\e92d';
}

.icon-telephone::before {
    content: '\e92e';
}

.icon-external-link::before {
    content: '\e92f';
}

.icon-hint::before {
    content: '\e930';
}

.icon-comment::before {
    content: '\e931';
}

.icon-wallet::before {
    content: '\e932';
}

.icon-gift-wrap::before {
    content: '\e933';
}

.icon-delete::before {
    content: '\e911';
}

.icon-edit::before {
    content: '\e912';
}

.icon-arrow-backward::before {
    content: '\ea40';
}

.icon-video-camera::before {
    content: '\e934';
}

.icon-photo-camera::before {
    content: '\e935';
}
