@mixin font-size($sizeValue: 1.6) {
    font-size: $sizeValue + px;
    font-size: calc($sizeValue / 10) + rem;
}
@mixin font-size-important($sizeValue: 1.6) {
    font-size: $sizeValue + px !important;
    font-size: calc($sizeValue / 10) + rem !important;
}

@mixin rtl-styles {
    [dir='rtl'] & {
        @content;
    }
}

@mixin rtl-rotate {
    [dir='rtl'] & {
        transform: scaleX(-1);
    }
}

@mixin content-ellipsis-overflow {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@mixin for-mobile-phone-only {
    @media (max-width: $mob) {
        @content;
    }

    [mobile='yes'] & [tablet='false'] & {
        @content;
    }
}

@mixin for-tablet-device-only {
    @media (max-width: $sm) {
        @content;
    }

    [mobile='yes'] & [tablet='yes'] & {
        @content;
    }
}

@mixin for-landscape-orientation {
    @media (orientation: landscape) {
        @content;
    }
}

@mixin keyframes($animation-name) {
    @-webkit-keyframes #{$animation-name} {
      @content;
    }
    @-moz-keyframes #{$animation-name} {
      @content;
    }  
    @-ms-keyframes #{$animation-name} {
      @content;
    }
    @-o-keyframes #{$animation-name} {
      @content;
    }  
    @keyframes #{$animation-name} {
      @content;
    }
  }
  
  @mixin animation($str) {
    -webkit-animation: #{$str};
    -moz-animation: #{$str};
    -ms-animation: #{$str};
    -o-animation: #{$str};
    animation: #{$str};      
  }