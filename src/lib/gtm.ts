import { USER_TYPES } from '@constants/common';

export const pageview = (url: string): void => {
    (window as any)?.dataLayer?.push({
        event: 'pageview',
        page: url,
    });
};
export const loginGA = (): void => {
    const store = localStorage.getItem('selectedStore') || 'AE';
    try {
        const authMethod = localStorage.getItem('authMethod');
        (window as any)?.dataLayer?.push({
            event: 'login',
            login_method: authMethod,
            store: store?.toUpperCase(),
        });
    } catch (error) {
        console.log('Error in loginGA:', error);
    }
};
export const signUpGA = (
    gender: string,
    user_id: string,
    language: string
): void => {
    const store = localStorage.getItem('selectedStore') || 'AE';
    try {
        const authMethod = localStorage.getItem('authMethod');
        (window as any)?.dataLayer?.push({
            event: 'sign_up',
            sign_up_method: authMethod,
            store: store?.toUpperCase(),
            gender: gender,
            user_id: user_id,
            language: language,
        });
    } catch (error) {
        console.log('Error in signUpGA:', error);
    }
};

export const loginGuestGA = (): void => {
    const store = localStorage.getItem('selectedStore') || 'AE';
    try {
        (window as any)?.dataLayer?.push({
            event: 'guest_login',
            store: store?.toUpperCase(),
            user_type: USER_TYPES.GUEST,
        });
    } catch (error) {
        console.log('Error in loginGuestGA:', error);
    }
};