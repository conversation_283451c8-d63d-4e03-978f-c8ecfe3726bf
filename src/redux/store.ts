// redux store setup
import { configureStore, ThunkAction, Action } from '@reduxjs/toolkit';
import { combineReducers } from 'redux';
import {
    nextReduxCookieMiddleware,
    wrapMakeStore,
} from 'next-redux-cookie-wrapper';
import { createWrapper } from 'next-redux-wrapper';
import commonReducer from '@features/common/commonSlice';
import loginFlowSlice from '@features/login/loginFlowSlice';
import getConfig from 'next/config';

const {
    publicRuntimeConfig: { cookieDomain },
} = getConfig();

// #. Combine the reducers
const reducers = combineReducers({
    common: commonReducer,
    login: loginFlowSlice,
});

// #. Make the store
const makeStore = wrapMakeStore(() =>
    configureStore({
        reducer: reducers,
        devTools: process.env.NODE_ENV !== 'production',
        middleware: (getDefaultMiddleware) =>
            getDefaultMiddleware().prepend(
                nextReduxCookieMiddleware({
                    subtrees: [
                        `otpPopup.redemptionType`,
                        `loginflowslice.login`,
                    ],
                    domain: cookieDomain,
                })
            ),
    })
);

export type AppStore = ReturnType<typeof makeStore>;
export type AppState = ReturnType<AppStore['getState']>;
export type AppDispatch = AppStore['dispatch'];

export type AppThunk<ReturnType = void> = ThunkAction<
    ReturnType,
    AppState,
    unknown,
    Action<string>
>;

// #. This wrapper will be the provider of the app root - wrapped
export const wrapper = createWrapper<AppStore>(makeStore, { debug: false });
