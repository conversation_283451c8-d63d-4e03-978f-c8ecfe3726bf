import { BANNER_TYPE } from '@constants/common';
import { ReactNode } from 'react';

export interface DropDownMenuIterface {
    children: JSX.Element;
    maxWidth?: number;
    dropDown: boolean;
    className?: string;
}

export interface SelectedRegion {
    region: string;
    flag: string;
    name: string;
}

// #. Quick view clicked event action method type
export type QuickViewClickAction = () => void;

/// to be removed
export interface CardSlider {
    sliderType: 'category' | 'gift';
    moreLink: string;
    title: string;
    cardsList: any[];
    slidesPerView?: number;
    spaceBetween?: number;
    mdSlidesPerView?: number;
    mdSpaceBetween?: number;
    lgSlidesPerView?: number;
    lgSpaceBetween?: number;
    onQuickViewClicked?: QuickViewClickAction;
}
//////////////////////////////

export interface ButtonInterface {
    theme:
        | 'white'
        | 'white-theme2'
        | 'white-large'
        | 'purple'
        | 'purple-large'
        | 'transparent';
    className?: string;
    action?: (ev: React.MouseEvent<HTMLButtonElement>) => void;
    children?: ReactNode;
    borderTheme?: 'purple';
    arrow?: 'arrow-forward';
    id?: string;
    attribues?: any;
    buttonRef?: any;
    loadingState?: 'inProcess' | 'complete';
}

//Gift card interface
export interface GiftCardSlider {
    cardName: string;
    image: string;
    category: string;
    label?: string;
    special?: string;
    offerTag?: string;
    highlight?: string;
    url: string;
    itemSlider?: boolean;
    denomination?: string;
    hidden?: boolean;
    onQuickViewClicked?: QuickViewClickAction;
}

//category card interface
export interface CategoryCardSlider {
    title: string;
    icon: string;
    url: string;
    hidden?: boolean;
}

// #. Quick view clicked event action method type
export type QuickViewPanelCloseAction = () => void;

// #. Data model for the quick view panel
export interface QuickViewPanel {
    open: boolean;
    anchor?: 'left' | 'right' | 'top' | 'bottom' | undefined;
    onClose?: QuickViewPanelCloseAction;
}

export interface QuickViewHeaderInterface {
    onClose?: QuickViewPanelCloseAction;
    brandName: string | undefined;
    brandLabel: string | undefined;
    logoImageData: string | undefined;
    brandID?: string | undefined;
}

// #. Interface for the search component

export interface Search {
    children?: JSX.Element;
    cssWrapperClass?: string;
    autocomplete?: boolean;
    autocompleteOptions?: Array<SearchAutocompleteOption>;
    onAutocompleteItemSelected?: (value?: string) => void;
    clearButton?: boolean;
    onSearchChange?: (value?: string) => void;
    searchText?: string;
    isOnLoad?: boolean;
}

// #. Interface for the promotional banner
export interface PromotionalBannerData {
    fullWidth: boolean;
    title: string;
    image: string;
    startDate: Date;
    endDate: Date;
}

// Download Data
export interface DowloadData {
    data: {
        image: string;
        title: string;
        subTitle: string;
        list: string[];
        appStoreImage: string;
        playStoreImage: string;
    };
}

// #. Testimonial video node
export interface TestimonialVideoNode {
    node: {
        link: string;
    };
}

// #. Testimonial video
export interface TestimonialVideo {
    edges: TestimonialVideoNode[];
}

export interface TestimonialOrderNode {}

//#. Testimonial review node
export interface TestimonialReviewNode {
    node: {
        description: string;
        customerName: string;
        happinessIndex: number;
        testimonialOrder: {
            edges: {
                node: {
                    productCode: string;
                    productName: string;
                    dateTime: Date;
                    country: {
                        code: string;
                        codeThreeLetter: string;
                    };
                };
            }[];
        };
    };
}

// #. Testimonial review
export interface TestimonialReview {
    edges: TestimonialReviewNode[];
}

// #. Testimonials
export interface Testimonials {
    data: {
        testimonials: {
            edges: {
                node: {
                    heading: string;
                    testimonialVideo: TestimonialVideo;
                    testimonialReview: TestimonialReview;
                };
            }[];
        };
    };
}
export interface SearchData {
    title: string;
    category: string;
    image: string;
}

// Minor Header Data
export interface MinorHeaderDataNodes {
    node: {
        backgroundImage: string;
        headerType: {
            code: string;
        };
        tagLine: string;
    };
}

export interface MinorHeaderDataHeaders {
    headers: {
        edges: MinorHeaderDataNodes[];
    };
}
export interface MinorHeaderData {
    data: MinorHeaderDataHeaders;
}

export interface MajorHeaderData {
    data: {
        headers: {
            edges: headerNodeInterface[];
        };
    };
}

// Site config data
export interface HelpDeskItem {
    phone: string;
    mobile: string;
    timing: string;
    country: {
        code: string;
        name: string;
        flagImage: string;
    };
}
export interface HelpDeskNode {
    node: HelpDeskItem;
}
export interface SiteConfigDataNode {
    node: {
        defaultCountry: string;
        defaultLanguage: string;
        email: string;
        zendeskChatApiKey: string;
        clevertapAccountId: string;
        recaptchaSiteKey: string;
        helpDesk: {
            edges: HelpDeskNode[];
        };
    };
}
export interface SiteConfigData {
    data: {
        siteConfigs: {
            edges: SiteConfigDataNode[];
        };
    };
}

// #. Data node of the promotion banner popup
export interface PromotionDataNode {
    backgroundColor: string;
    url: string;
    ctaText: string;
    title: string;
    subTitle: string;
    isPopup: boolean;
    description: string;
    image: string;
    __typename: string;
}

// #. Data format of the promotion banner popup
export interface PromotionData {
    data: {
        promotion: PromotionDataNode;
    };
}
// stores data

export interface StoresLanguages {
    node: {
        name: string;
    };
}
export interface StoresItem {
    node: {
        code: string;
        name: string;
        country: {
            code: string;
            flagImage: string;
            codeThreeLetter: string;
            languages: {
                edges: StoresLanguages[];
            };
        };
    };
}
// stores data
export interface StoresData {
    data: {
        stores: {
            edges: StoresItem[];
        };
    };
}

// Main Banner
export interface BannerItems {
    name: string;
    image: string;
    url: string;
    bannerDescription: string;
    bannerCategory: {
        code: BANNER_TYPE;
        slidingTimeInterval?: null | number;
        isSliding: boolean;
        floatingType?: 'HORIZONTAL' | 'VERTICAL' | null;
    };
    isPopup: boolean;
}
export interface BannerInterface {
    data: {
        banners: BannerItems[];
    };
}

// Side Banner
export interface SideBannerItems {
    image: string;
    url: string;
}
export interface SideBanner {
    data: {
        banners: SideBannerItems[];
    };
}

// Rewards Interface
export interface RewardsItemInterface {
    image: string;
    bannerCategory: {
        code: BANNER_TYPE;
    };
    url: string;
}

export interface RewardsInterface {
    data: {
        banners: RewardsItemInterface[];
    };
}

export interface PressRoomItemInterface {
    name: string;
    pressUrl: string;
    pressLogo: string;
    code: string;
    orderNumber: number;
}
export interface PressRoomInterface {
    data: {
        pressRooms: PressRoomItemInterface[];
    };
}

// Footer app banner
export interface AppBannerInterface {
    itemLabel?: string | null;
    itemImage: string;
    itemName: string;
    itemUrl: string;
    id?: string;
}
// Footer Contact Interface
export interface ContactInterface {
    email: string;
    helpDesk: {
        edges: HelpDeskNode[];
    };
}
// Footer Stores Interface
export interface FooterStoresInterface {
    store: {
        country: {
            name: string;
        };
    };
}
// Footer payment partners Interface
export interface PaymentPartnersNodeInterface {
    node: {
        name: string;
        logo: string;
        code: string;
    };
}
export interface PaymentPartnersInterface {
    edges: PaymentPartnersNodeInterface[];
}
// Footer Social media Interface

// Footer SubMenu Interface

export interface SubMenuNodeInterface {
    node: {
        itemLabel: string;
        itemName: string;
        itemUrl: string | null;
        itemImage?: string;
    };
}
export interface SubMenuInterface {
    itemLabel: string;
    itemName: string;
    itemUrl: string | null;
    children: {
        edges: SubMenuNodeInterface[];
    };
}

// Footer Item

export interface FooterItemInterface {
    appBanner: AppBannerInterface[];
    contact: ContactInterface;
    copyrightNotice: string;
    footerStores: FooterStoresInterface[];
    logo: string;
    paymentPartners: PaymentPartnersInterface;
    subMenu: SubMenuInterface[];
    tagLine: string;
}

// Aside menu

export interface AsideMenu {
    heading: string;
    menu: { name: string; url: string; value: string }[];
}

// Footer data
export interface FooterInterface {
    data: {
        footer: FooterItemInterface;
    };
}

// Blogs data

export interface BlogsItemInterface {
    node: {
        title: string;
        image: string;
        url: string;
    };
}
export interface BlogsInterface {
    data: {
        blogs: {
            edges: BlogsItemInterface[];
        };
    };
}

// Download app

export interface DownloadTextNodeInterface {
    node: {
        id: string;
        text: string;
    };
}
export interface DownloadAppDataInterface {
    downloadApp: [
        {
            appBanner: AppBannerInterface[];
            bannerImage: string;
            text: {
                edges: DownloadTextNodeInterface[];
            };
            title: string;
        }
    ];
}
export interface DownloadAppInterface {
    data: DownloadAppDataInterface;
}

export interface DownloadAppPopupInterface {
    downloadApp: [
        {
            appBanner: AppBannerInterface[];
            bannerImage: string;
            text: {
                edges: DownloadTextNodeInterface[];
            };
            title: string;
        }
    ];
}

// Signature interface

export interface SignatureSiteConfigInterface {
    node: {
        signatureSubtitle: string;
        signatureTitle: string;
    };
}
export interface SignatureInterface {
    data: {
        siteConfigs: {
            edges: SignatureSiteConfigInterface[];
        };
    };
}

export interface HappyCardBrand {
    brand: {
        code: string;
        name: string;
        brandImageData: string;
        primaryCategory: {
            id: string;
            name: string;
        };
    };
}

export interface HappyCardRedeemableBrands {
    totalCount: number;
    edges: {
        node: HappyCardBrand;
    }[];
}
export interface HappyCard {
    title: string;
    subTitle: string;
    description: string;
    cardImage: string;
    url: string;
    brand: {
        brandImageData: string;
    };
    redeemableBrands: HappyCardRedeemableBrands;
}

export interface HappyCardData {
    data: {
        happyCard: HappyCard;
    };
}

// #. News letter response data model
export interface NewsletterSubscriptionData {
    emailSubscription: {
        subscription: {
            emailAddress: string;
        };
    };
}

// #. News letter data
export interface NewsletterConfigData {
    data: {
        newsletterConfiguration: {
            title: string;
            subTitle: string;
            buttonName: string;
        };
    };
}

// #. News letter response data model
export interface GetDownloadLink {
    downloadAppRequest: {
        downloadAppRequest: {
            deliveryStatus: string;
        };
    };
}

// header interface

export interface headerNodeInterface {
    node: {
        backgroundImage: string;
        eGiftCards_SeoName?: string;
        gamingCards_SeoName?: string;
        headerType: {
            code: string;
        };
        tagLine: string;
        logo?: string;
    };
}
export interface headerInterface {
    headers: {
        edges: headerNodeInterface[];
    };
}

// Site config data
export interface SiteConfigNode {
    node: {
        defaultCountry: string;
        defaultLanguage: string;
        signatureSubtitle: string;
        signatureTitle: string;
        email: string;
        clevertapAccountId: string;
        zendeskChatApiKey: string;
        recaptchaSiteKey: string;
        helpDesk: {
            edges: HelpDeskNode[];
        };
    };
}

export interface SecureConfigs {
    cameraTagAppId: string;
    imglyLicense: string;
}

// slider config data

export interface BrandImageGallery {
    node: {
        image: string;
    };
}

export interface BrandStoreLocations {
    edges: {
        node: {
            contactNumber: string;
            address: string;
            city: {
                name: string;
            };
        };
    }[];
}

export interface BrandRedeemables {
    edges: {
        node: {
            brand: {
                name: string;
            };
        };
    }[];
}

export interface Brand {
    brandImageData: string;
    logoImageData: string;
    buyForYourself: boolean;
    company: {
        code: string;
        name: string;
    };
    crossSellBrands: {
        edges: {
            node: {
                brand: {
                    brandImageData: string;
                    code: string;
                    name: string;
                    primaryCategory: {
                        code: string;
                        name: string;
                    };
                };
            };
        }[];
    };
    denominationRange: string;
    description: string;
    expiry: string;
    id: string;
    imageGallery: {
        edges: BrandImageGallery[];
    };
    label: string;
    name: string;
    nameEn?: string;
    code?: string;
    slug?: string;
    primaryCategory: {
        name: string;
        nameEn?: string;
    };
    redemptionDetails: string;
    redemptionType: string;
    store: {
        country: {
            flagImage: string;
        };
    };
    website: string;
    variableDenomination?: boolean;
    storeLocations?: BrandStoreLocations;
    currency?: {
        name: string;
        code: string;
    };
    maxDenominationAmount?: number;
    minDenominationAmount?: number;
    redeemableBrands?: BrandRedeemables;
}

export interface SliderBrandInterface {
    node: {
        brandData: {
            brand: Brand;
            orderNumber: string;
        };
    };
}

export interface SliderCategoriesInterface {
    node: {
        code: string;
        iconImage: string;
        name: string;
        nameEn?: string;
        title: string;
        titleEn?: string;
        seoName: string;
        tagType: string;
    };
}
export interface SliderInterface {
    ctaText: string;
    heading: string;
    sliderBrand: {
        edges: SliderBrandInterface[];
    };
    sliderCategories: {
        edges: SliderCategoriesInterface[];
    };
    sliderType: string;
    category: {
        seoName: string;
    };
}

// Home main data interface
export interface HomeDataInterface {
    widgetOrder: {
        widget:
            | 'BLOG'
            | 'REWARDS_BANNER'
            | 'PROMOTIONAL_BANNER'
            | 'DOWNLOAD_APP';
    }[];
    sliders: SliderInterface[];
    banners: BannerItems[];
    blogs: { edges: BlogsItemInterface[] };
    downloadApp: [
        {
            appBanner: AppBannerInterface[];
            bannerImage: string;
            text: {
                edges: DownloadTextNodeInterface[];
            };
            title: string;
        }
    ];
    happyCard: HappyCard;
    pressRooms: PressRoomItemInterface[];
    promotion: PromotionDataNode;
    testimonials: {
        edges: {
            node: {
                heading: string;
                testimonialVideo: TestimonialVideo;
                testimonialReview: TestimonialReview;
            };
        }[];
    };
    searchTags: SearchTags[];
}

export interface AboutDataInterface {
    headers: headerInterface;
    footer: FooterItemInterface;
    siteConfigs: {
        edges: SiteConfigNode[];
    };
    stores: {
        edges: StoresItem[];
    };
}

export interface UpcomingOccasions {
    occasionName: string;
    message: string;
    seoName: string;
    isToday: boolean;
}

export interface BrandFilterData {
    code?: string;
    name: string;
    nameEn?: string;
    seoName: string;
    iconImage: string;
}

export interface BrandFilters {
    data: {
        categories: BrandFilterData[];
        occasions: BrandFilterData[];
    };
}

export interface AllBrandsInterface {
    upcomingOccasions: UpcomingOccasions[];
    brandFilters: BrandFilters;
    productCatalogs: BrandsByCategory | BrandsByOccasion;
    banners: BannerItems[];
    howToUses: HowToUseData;
}

// quickview
export interface CrossSellBrandsItem {
    node: {
        brand: {
            code: string;
            name: string;
            id: string;
            slug: string;
            primaryCategory: {
                code: string;
                name: string;
            };
            brandImageData: string;
        };
    };
}
export interface QuickviewInterface {
    brand: {
        redeemAt: boolean;
        primaryCategory: {
            code: string;
            name: string;
            nameEn?: string;
        };
        brandImageData: string;
        imageGallery: {
            edges: {
                node: {
                    image: string;
                    caption: string;
                };
            }[];
        };
        code: string;
        name: string;
        nameEn?: string;
        slug?: string;
        logoImageData: string;
        company: {
            code: string;
            name: string;
        };
        denominationRange: string;
        storeLocations: {
            edges: [];
        };
        description: string;
        redemptionType: string;
        redemptionDetails: string[];
        store: {
            country: {
                name: string;
                flagImage: string;
            };
        };
        expiry: string;
        label: string;
    };
    crossSellBrandConfig: [
        {
            crossSellBrands: {
                totalCount: number;
                edges: CrossSellBrandsItem[];
            };
        }
    ];
}

export interface QuickviewSlider {
    brandSlug: string | undefined;
    brandLabel: string | undefined;
    denominationRange: string | undefined;
    brandImageData: string | undefined;
    imageGallery:
        | { edges: { node: { image: string; caption: string } }[] }
        | undefined;
}

export interface QuickViewProductInfoInterface {
    redeemAt: boolean | undefined;
    brandName: string | undefined;
    country:
        | {
              name: string;
              flagImage: string;
          }
        | undefined;
    expiry: string | undefined;
    type: string | undefined;
}

export interface HowToUseData {
    edges: {
        node: {
            title: string;
            howToUseBanner: {
                edges: {
                    node: {
                        id: string;
                        bannerImage: string;
                    };
                }[];
            };
        };
    }[];
}

export interface HowToUseInterface {
    howToUses: HowToUseData;
}

export interface ProductFeedbackInterface {
    productFeedbackBox: {
        productFeedbackBox: {
            emailAddress: string;
            brandName: string;
        };
    };
}

export interface BrandsByCategory {
    data: {
        brandsByCategory: {
            totalCount: number;
            edges: {
                node: {
                    brand: SliderBrandInterface;
                };
            }[];
            categoryMessage: string;
        };
        recommendedGiftCards: RecommendedGiftCards[];
    };
}

export interface BrandsByOccasion {
    data: {
        brandsByOccasion: {
            totalCount: number;
            edges: {
                node: {
                    brand: SliderBrandInterface;
                };
            }[];
            occasionMessage: string;
        };
        recommendedGiftCards: RecommendedGiftCards[];
    };
}

export interface RecommendedGiftCards {
    brand: Brand;
}

export interface CommonDataInterface {
    data: {
        footer: FooterItemInterface;
        headers: headerInterface;
        siteConfigs: {
            edges: SiteConfigNode[];
        };
        stores: {
            edges: StoresItem[];
        };
    };
}

export interface WidgetOrderInterface {
    data: {
        widgetOrder: {
            widget:
                | 'BLOG'
                | 'REWARDS_BANNER'
                | 'PROMOTIONAL_BANNER'
                | 'DOWNLOAD_APP';
        }[];
    };
}
export interface SearchAutocompleteOption {
    name: string;
    slug: string;
    highlight: string;
}

export interface SearchTags {
    brand: {
        name: string;
        slug: string;
    };
}

export interface NotifierInterface {
    title: string;
    description: string;
    icon?: string;
    autoHideDuration?: number;
    onClose?: () => void;
}

export interface ThemeSettingsInterface {
    snowFlakeCount: number;
    speedMin: number;
    speedMax: number;
    windMax: number;
    windMin: number;
    radiusMax: number;
    radiusMin: number;
    useImage: boolean;
    image: string;
    rotationSpeedMin: number;
    rotationSpeedMax: number;
    color: string;
}

export interface ThemeConfigInterface {
    themeConfig: ThemeSettingsInterface[];
}

export interface CaptchaConfig {
    actionName:string,
    captchaVersion: string,
    hasCaptchaEnabled: boolean
}

export interface GraphQLError {
    graphQLErrors : [{ message : string }] 
}

export interface CommunicationConfigsInterface {
    data: {
      communicationConfigs: CommunicationConfigs[];
    };
  }
  
  export interface CommunicationConfigs {
    resendDeliveryEnabled: boolean;
  }
  
