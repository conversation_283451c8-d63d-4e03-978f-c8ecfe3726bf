@import '@styles/variables';
@import '@styles/mixins';

.respond-message {
    position: absolute;
    top: -66px;
    gap: 10px;
    display: flex;
    gap: 12px;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 56px;
    padding: 16px 16px 16px 16px;
    border-radius: 12px;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 10%);
    z-index: 99;
    background: $dark-charcoal;
    box-shadow: 0px 16px 20px -8px rgba(3, 5, 18, 0.10);

    @include font-size(12);

    p {
        width: 100%;
        font-stretch: normal;
        font-style: normal;
        text-align: left;
        color: $white;
        font-family: "Mona Sans";
        font-size: 14px;
        font-weight: 600;
        line-height: 18px; /* 128.571% */
        letter-spacing: -0.14px;


        @include rtl-styles {
            font-family: $arabic-font-family;
                margin-right: 35px;
                text-align: right;
            }
    }

    svg {
        width: 24px;
        height: 24px;
    }

    h5 {
        width: 100%;
        height: 23px;
        margin: 0 177px 5px 16px;
        font-family: Poppins;
        font-size: 16px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: $dark-purple;

        @include rtl-styles {
            margin-right: 35px;
            text-align: right;
        }
    }

    &--error {
        color: $error-text;

        svg {
            path {
                color: $error-text;
            }
        }
    }

    &--success {
        background-color: $white;

        svg {
            path {
                color: $barney-purple;
            }
        }
    }

    &--info {
        background-color: $white;

        svg {
            path {
                color: $barney-purple;
            }
        }
    }
}

.respond-message--mweb {
    border: none;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.16);
}

@media only screen and (max-width: 600px) {
    .respond-message {
        position: absolute;
        top: -90px;
        left: 50%;
        transform: translateX(-50%);
        display: grid;
        grid-template-columns: 40px auto;
        gap: 12px;
        margin-bottom: 0;
        width: 343px;
        height: 91px;
        padding: 16px;
        @include font-size(12);

        &__content {
            width: 259px !important;
        }

        p {
            width: initial;
            height: initial;
            margin: 5px 0 0;
            font-family: Poppins;
            font-size: 10px;
        }

        h5 {
            height: initial;
            margin: 0;
            font-family: Poppins;
        }
    }

    .respond-message--mweb {
        top: -65px;
        border: solid 1px $barney-purple;
    }

    .respond-message--app {
        border: none;
        box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.16);
        top: -5px;
    }
}
