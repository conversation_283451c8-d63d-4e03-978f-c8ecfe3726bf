import { SEVERITY } from '@constants/messageTypes';
import { useEffect, useState } from 'react';
import styles from './ResponseInfoHOC.module.scss';
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded';
import CancelRoundedIcon from '@mui/icons-material/CancelRounded';
import InfoRoundedIcon from '@mui/icons-material/InfoRounded';
import getConfig from 'next/config';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { PLATFORM_TYPE } from '@constants/common';
import { useAppSelector } from '@redux/hooks';
import { getPlatform } from '@features/login/loginFlowSlice';

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const ResponseInfoHOC = (WrappedComponent: any) =>
    function Comp(props: any) {
        // #. Get the query  info from the router
        const { query } = useRouter();

        // #. Get platform type from redux
        const platform = useAppSelector(getPlatform);

        const [open, setOpen] = useState(false);
        const [message, setMessage] = useState('Message');
        const [heading, setHeading] = useState('');
        const [online, setOnline] = useState(true);
        const [severity, setSeverity] = useState<SEVERITY>(SEVERITY.SUCCESS);
        const [platformType, setPlatformType] = useState<any>('');
        const [isHtml, setIsHtml] = useState<any>(false);


        const checkRoundedIcon = `${imageBaseUrl}/icons/check-alert.svg`;
        const errorRoundedIcon = `${imageBaseUrl}/icons/caution-alert.svg`;
        const exclamationIcon = `${imageBaseUrl}/icons/excla-icon.png`;


        const showMessage = (
            message: string,
            severity = SEVERITY.ERROR,
            heading?: string | null,
            online?: boolean | true,
            isHtml?: boolean | false
        ) => {
            setMessage(message);
            if (online == undefined) {
                setOnline(true);
            } else {
                setOnline(online);
            }
            heading && setHeading(heading);
            setSeverity(severity);
            setOpen(true);
            setIsHtml(isHtml);
            setTimeout(() => {
                setOpen(false);
            }, 4000);
        };

        // #. Get platform type from local storage
        useEffect(() => {
            const stored = localStorage.getItem('platform');
            setPlatformType(platform ? platform : stored);
        }, [query?.platform, platform]);

        return (
            <>
                {open && (
                    <div
                        className={
                            platformType ===
                            PLATFORM_TYPE.MWEB.toLocaleLowerCase()
                                ? `${styles['respond-message--mweb']} ${
                                      styles['respond-message']
                                  } 
								  ${styles['respond-message--' + severity]}`
                                : platformType ===
                                  PLATFORM_TYPE.APP.toLocaleLowerCase()
                                ? `${styles['respond-message--app']} ${
                                      styles['respond-message']
                                  } 
							  ${styles['respond-message--' + severity]}`
                                : `respond-message ${
                                      styles['respond-message']
                                  } ${styles['respond-message--' + severity]}`
                        }
                    >
                        <div className={styles['respond-message__icon']}>
                            {severity === SEVERITY.SUCCESS &&
                                (platformType ===
                                PLATFORM_TYPE.APP.toLocaleLowerCase() ? (
                                    <Image
                                        src={checkRoundedIcon}
                                        width={44}
                                        height={44}
                                        alt="check"
                                    />
                                ) : (
                                    <CheckCircleRoundedIcon />
                                ))}
                            {severity === SEVERITY.ERROR &&
                                (platformType ===
                                PLATFORM_TYPE.APP.toLocaleLowerCase() ? (
                                    online && (
                                        <Image
                                            src={errorRoundedIcon}
                                            width={44}
                                            height={44}
                                            alt="check"
                                        />
                                    )
                                ) : (
                                    <img src={exclamationIcon} alt='error' />
                                ))}
                            {severity === SEVERITY.INFO && <InfoRoundedIcon />}
                        </div>
                        <div className={styles['respond-message__content']}>
                            {/* {!!heading && <h5> {heading} </h5>} */}
                            {
                                isHtml && <p dangerouslySetInnerHTML={{ __html: message }}></p>
                            }
                            {
                                !isHtml && <p>{message}</p>
                            }
                            
                        </div>
                    </div>
                )}
                <WrappedComponent {...props} showMessage={showMessage} />
            </>
        );
    };

export default ResponseInfoHOC;
