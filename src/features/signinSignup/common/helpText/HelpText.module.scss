@import '@styles/variables';
@import '@styles/mixins';

.help-panel {
    @include font-size(12);

    white-space: nowrap;
    text-align: center;
    position: relative;
    width: 100%;
    font-family: $mona-sans-font-family;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.12px;

    @include rtl-styles{
        font-family: $arabic-font-family;
    }


    @media only screen and (max-width: $sm) {
        // position: absolute;
        bottom: 0;
        left: 0;
        margin-top: 16px;
        // padding-bottom: 64px;
        text-align: center;

        @media only screen and (max-height: 670px) {
            position: relative;
        }
    }

    a {
        color: $dark-purple;
    }

    span {
    color: #868785;
    font-weight: 600;
    }
}

.arrow-class {
    background-color: $white !important;
}
