import {
    styled,
    Tooltip,
    tooltipClasses,
    TooltipProps,
    Zoom,
} from '@mui/material';
import { useTranslation } from 'next-i18next';
import styles from './HelpText.module.scss';
import { HELP_CENTER_URL, HELP_CENTER_URL_AR } from '@constants/common';
import useAppRouter from '@features/common/router.context';
const HelpText = () => {
    //getting locale
    const {
        state: { locale },
    } = useAppRouter();
    const HC_URL = locale === 'ar' ? HELP_CENTER_URL_AR : HELP_CENTER_URL;

    const LightTooltip = styled(({ className, ...props }: TooltipProps) => (
        <Tooltip
            {...props}
            classes={{
                popper: className,
                arrow: className,
            }}
            TransitionComponent={Zoom}
            arrow
            // componentsProps={{ arrow: styles["arrow-class"] }}
        />
    ))(({ theme }) => ({
        [`& .${tooltipClasses.tooltip}`]: {
            backgroundColor: theme.palette.common.white,
            border: '#d9dfe4 1px solid',
            boxShadow: '0 20px 60px 0 rgba(0, 0, 0, 0.16)',
            borderRadius: '12px',
        },
    }));

    const { t } = useTranslation('common');

    return (
        <div className={styles['help-panel']}>
           <span> {t('needHelpQ')} {t('contactUsVia')}</span>{' '}
            <a href={HC_URL}>{t('helpCentre')}</a>
        </div>
    );
};

export default HelpText;
