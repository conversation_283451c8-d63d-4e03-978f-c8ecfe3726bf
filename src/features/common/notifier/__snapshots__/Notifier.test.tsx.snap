// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Notifier component renders notifier component 1`] = `
Object {
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="notifier notifier"
      >
        <div
          class="notifier__container"
        >
          <div
            class="notifier__icon"
            data-testid="notiIcon"
          >
            <svg
              aria-hidden="true"
              class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium checked-icon css-i4bv87-MuiSvgIcon-root"
              data-testid="CheckIcon"
              focusable="false"
              viewBox="0 0 24 24"
            >
              <path
                d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"
              />
            </svg>
          </div>
          <div
            class="notifier__text-section"
          >
            <div
              class="notifier__title"
              data-testid="notiTitle"
            >
              title
            </div>
            <div
              class="notifier__description"
              data-testid="notiDescription"
            >
              description
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="notifier notifier"
    >
      <div
        class="notifier__container"
      >
        <div
          class="notifier__icon"
          data-testid="notiIcon"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium checked-icon css-i4bv87-MuiSvgIcon-root"
            data-testid="CheckIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"
            />
          </svg>
        </div>
        <div
          class="notifier__text-section"
        >
          <div
            class="notifier__title"
            data-testid="notiTitle"
          >
            title
          </div>
          <div
            class="notifier__description"
            data-testid="notiDescription"
          >
            description
          </div>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
