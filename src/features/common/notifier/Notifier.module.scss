@import '@styles/variables';
@import '@styles/mixins';

.notifier {
    position: fixed;
    top: 40px;
    left: 50%;
    z-index: 9999;
    border-radius: 6px;
    color: $white;

    &__container {
        min-width: 500px;
        min-height: 100px;
        background-color: $carbon-black;
        position: relative;
        left: -50%;
        border-radius: 24px;
        padding: 25px 30px;
        display: flex;
        gap: 22px;
        align-items: center;
    }

    &__icon {
        display: flex;
        align-items: center;
    }

    &__text-section {
        display: flex;
        flex-direction: column;
        gap: 7px;
    }

    &__title {
        @include font-size(18);

        font-weight: 500;
    }

    &__description {
        @include font-size(12);

        font-weight: 200;
    }
}
