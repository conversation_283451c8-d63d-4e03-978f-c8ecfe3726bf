import React from 'react';
import { act, cleanup, RenderResult, render } from '@redux/test-utils';
import Notifier from './Notifier';

async function wait(ms = 0) {
    await act(() => {
        return new Promise((resolve) => {
            setTimeout(resolve, ms);
        });
    });
}

const onClose = jest.fn();

const setup = () => {
    const component = render(<Notifier />, {
        preloadedState: {
            common: {
                notifier: {
                    title: 'title',
                    description: 'description',
                    icon: 'checkOutline',
                    autoHideDuration: 1000,
                    onClose: onClose,
                },
            },
        },
    });

    return component;
};

describe('Notifier component', () => {
    let component: RenderResult<
        typeof import('@testing-library/dom/types/queries'),
        HTMLElement
    >;

    afterEach(cleanup);

    it('renders notifier component', async () => {
        component = setup();
        expect(component).toMatchSnapshot();
    });

    it('Title and description must render', async () => {
        const { getByTestId } = setup();

        // #. Title should be rendered
        expect(getByTestId('notiTitle')).toHaveTextContent('title');

        //#. Must have description
        expect(getByTestId('notiDescription')).toHaveTextContent('description');
    });

    it('Icon should be render', async () => {
        const { getByTestId } = setup();

        expect(getByTestId('notiIcon').firstChild).toBeTruthy();
    });

    it('Notification should auto hide after 1s', async () => {
        await wait(1000);

        expect(onClose).toBeCalled();
    });
});
