import { useAppDispatch, useAppSelector } from '@redux/hooks';
import { useEffect, useState } from 'react';
import { getNotifierState, setNotifierState } from '../commonSlice';
import styles from './Notifier.module.scss';
import ErrorOutlineRoundedIcon from '@mui/icons-material/ErrorOutlineRounded';
import CheckIcon from '@mui/icons-material/Check';

const icons: any = {
    errorOutline: <ErrorOutlineRoundedIcon className="error-outlined-icon" />,
    checkOutline: <CheckIcon className="checked-icon" />,
};

const NotifierBody = ({ icon, title, description }: any) => {
    return (
        <div className={`${styles['notifier']} notifier`}>
            <div className={styles['notifier__container']}>
                <div
                    className={styles['notifier__icon']}
                    data-testid="notiIcon"
                >
                    {icons[icon]}
                </div>
                <div className={styles['notifier__text-section']}>
                    <div
                        className={styles['notifier__title']}
                        data-testid="notiTitle"
                    >
                        {title}
                    </div>
                    <div
                        className={styles['notifier__description']}
                        data-testid="notiDescription"
                    >
                        {description}
                    </div>
                </div>
            </div>
        </div>
    );
};

/**
 * @component Notifier
 * @description Common component to display the notifications
 * @param param0
 * @returns
 */
const Notifier = () => {
    // #. Notifier state
    const [showNotifier, setShowNotifier] = useState<boolean>(false);
    // #. Get notification details from the state
    const {
        title,
        description,
        icon,
        autoHideDuration = 5000,
        onClose,
    } = useAppSelector(getNotifierState);
    // #. Dipatch the hidden state
    const dipatch = useAppDispatch();

    useEffect(() => {
        // #. Must have title to show the notification
        if (title) {
            setShowNotifier(true);
            // #. Autohide the notifier
            setTimeout(() => {
                setShowNotifier(false);
                onClose && onClose();
                // #. Dispath the hidden state - title muse be false
                dipatch(
                    setNotifierState({
                        title: '',
                        description: '',
                    })
                );
            }, autoHideDuration);
        }

        return () => {
            setShowNotifier(false);
        };
    }, [title]);

    return (
        <>
            {showNotifier && (
                <NotifierBody
                    title={title}
                    description={description}
                    icon={icon}
                />
            )}
        </>
    );
};

export default Notifier;
