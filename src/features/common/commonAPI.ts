import { initialize<PERSON><PERSON><PERSON> } from "@graphql/apolloClient";
import { BLACK_LISTED_CONTRIES_QUERY, CAPTCHA_CONFIG_QUERY, COMMUNICATION_CONFIG_QUERY, GUEST_ENABLED, SITE_META } from "./common.query";
import { getPlatformType } from "@utils/signInSignUpHelper";
import { LOGO_QUERY, THEME_SETTINGS_QUERY } from './common.query';
import { useQuery } from '@apollo/client';
import { CommunicationConfigsInterface, ThemeSettingsInterface } from '@interfaces/common.inteface';
import { PLATFORM_TYPE } from '@constants/common';
import * as Sentry from '@sentry/nextjs';

export const getLoginMetaData = async (
    store: string,
    locale: string,
    platform: string,
    ipAddress: string
) => {
    try {
        const apolloClient = initializeApollo(locale);

        let response = await apolloClient.query({
            query: SITE_META,
            variables: {
                platformType_Code: getPlatformType(platform),
                store: store ? store.toUpperCase() : '',
            },
            context: {
                headers: {
                    'Yg-Ip-Address': ipAddress,
                },
            },
        });
        return {
            metaData: response?.data?.siteConfigs?.edges[0]?.node || {},
        };
    } catch (error) {
        console.log('Error occured in SITE_META ', error);
        Sentry.captureMessage(`Error occured in SITE_META ${JSON.stringify(error)}`);
        return {
            metaData: {},
        };
    }
}

export const guestLoginEnabled = async (platform: string, locale: string, storeLocale: string) => {
    try {
        const store = "STAE";
        const apolloClient = initializeApollo(locale);
        let response = await apolloClient.query({
            query: GUEST_ENABLED,
            context: {
                headers: {
                    'app-platform': platform || 'web',
                    'access-locale': store
                },
                clientName: 'guestLogin',
            }
        });
        const { isGuestEnabled } = response?.data?.cartConfig
        return isGuestEnabled
    } catch (error) {
        console.log('Error occured in GUEST_ENABLED ', error);
        Sentry.captureMessage(`Error occured in GUEST_ENABLED ${JSON.stringify(error)}`);
        return false;
    }
}

export const logoDataQuery = async (locale: string, platform: string) => {
    try{
        const apolloClient = initializeApollo(locale);
        let response = await apolloClient.query({
            query: LOGO_QUERY,
            variables: {
                platformType_Code: platform?.toLowerCase() || PLATFORM_TYPE.WEB,
            },
        });
        return response?.data;
    } catch(error : any){
        console.log('Error occured in LOGO_QUERY ', error);
        Sentry.captureMessage(`Error occured in LOGO_QUERY ${JSON.stringify(error)}`);
        return {};
    }
    
};

/**
 * @method getThemeSettingsData
 * @description Get theme settings data for snowfall effect
 * @param param0
 * @returns
 */
export const GetThemeSettingsData = (): any => {
    let {
        loading: themeSettingsDataLoading,
        error: themeSettingsDataError,
        data: themeSettingsData,
    } = useQuery<ThemeSettingsInterface>(THEME_SETTINGS_QUERY, {
        variables: {
            platformType_Code: PLATFORM_TYPE.WEB,
        },
        context: {
            clientName: 'webstore-with-cdn',
        },
    });

    return {
        themeSettingsDataLoading,
        themeSettingsDataError,
        themeSettingsData,
    };
};

export const getCaptchaConfig = async (locale: string,) => {
    try {
        const apolloClient = initializeApollo(locale);
        let response = await apolloClient.query({
            query: CAPTCHA_CONFIG_QUERY,
            context: {
                clientName: "ecom-users",
            }
        });
        return response?.data;
    } catch (error) {
        console.log('Error occured in CAPTCHA_CONFIG_QUERY ', error);
        Sentry.captureMessage(`Error occured in CAPTCHA_CONFIG_QUERY ${JSON.stringify(error)}`);
        return {};
    }
};

export const getBlacklistedCountries = async (locale: string) => {
    try{
        const apolloClient = initializeApollo(locale);
        let response = await apolloClient.query({
            query: BLACK_LISTED_CONTRIES_QUERY,
            context: {
                clientName: "ecom-users"
            }
        });
        return response?.data;
    } catch(error){
        console.log('Error occured in BLACK_LISTED_CONTRIES_QUERY ', error);
        Sentry.captureMessage(`Error occured in BLACK_LISTED_CONTRIES_QUERY ${JSON.stringify(error)}`);
        return {blacklistedCountries : []}
    }
};

/**
 * @method communicationConfigs
 * @param {string} locale
 * @param {string} countryCode
 * @param {string} flow
 * @param {string} channel
 * @returns {data} object
 */

export const CommunicationConfigs = (
    countryCode: string,
    flow: string,
    channel: string
): any => {
    let {
        loading: communicationConfigsLoading,
        data: communicationConfigsData,
        error: communicationConfigsError,
    } = useQuery<CommunicationConfigsInterface>(COMMUNICATION_CONFIG_QUERY, {
        variables: {
            countryCode: countryCode.toUpperCase() , 
            flow: flow,
            channel: channel,
        },
        context: {
            clientName: 'ecom-users',
        },
    });

    return {
        communicationConfigsLoading,
        communicationConfigsData,
        communicationConfigsError,
    };
};