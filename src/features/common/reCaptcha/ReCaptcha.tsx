import Script from 'next/script';
import { useEffect } from 'react';
import useReCaptchaAPI from './ReCaptchaAPI';

interface ReCaptchaInterface {
    recaptchaSiteKey: string;
}

/**
 * @method ReCaptcha
 * @param {recaptchaSiteKey}
 * @returns
 */
const ReCaptcha = ({ recaptchaSiteKey }: ReCaptchaInterface) => {
    const { setSiteKey } = useReCaptchaAPI();

    // #. Set the recaptha api reference url with the site key
    const url = `https://www.google.com/recaptcha/api.js?render=${recaptchaSiteKey}`;

    useEffect(() => {
        setSiteKey && setSiteKey(recaptchaSiteKey);
    }, []);

    // #. Returns the scripts tag
    return (
        <Script
            src={url}
            onLoad={() => {
                console.log('ReCaptcha loaded.');
            }}
        />
    );
};

export default ReCaptcha;
