@import '/src/styles/variables';
@import '/src/styles/mixins';

.recaptch-validation {
    display: flex;
    flex-direction: column;
    gap: 20px;
    color: $dark-grey;

    @include font-size(16);

    &__input-holder {
        display: flex;
        flex-direction: row;
        border: 1px solid $cool-grey;
        border-radius: 12px;
        padding: 5px 5px 5px 15px;

        @include rtl-styles {
            padding: 5px 15px 5px 5px;
        }
    }

    &__input {
        flex-grow: 1;
        color: $black-header !important;

        @include font-size-important(14);
    }

    &__button {
        @include font-size(16);

        font-weight: 500;
        padding: 7px 50px;

        @include rtl-styles {
            padding: 4px 50px;
        }

        &-submitted {
            outline: none;
            width: 40px;
            height: 40px;
            text-align: center;
            border-radius: 40px;
            background: $white;
            border: 4px solid $barney-purple;
            border-color: $warm-grey;
            font-size: 0;
            border-left-color: $barney-purple;
            transition: all 0.25s ease;
            animation: rotating 2s 0.25s linear infinite;
            padding: 0;
            min-width: auto;
            margin-left: 70px;

            &::before {
                content: '';
            }

            @include rtl-styles {
                margin-left: 0;
                margin-right: 70px;
            }
        }

        &-success {
            background-color: $barney-purple;
            color: $white;
            border-radius: 50%;
            transition: all 0.25s ease;
            width: 40px;
            height: 40px;
            min-width: auto;
            padding: 0;
            margin-left: 70px;

            @include font-size(30);

            @include rtl-styles {
                margin-left: 0;
                margin-right: 70px;
            }

            &::before {
                font-size: 42px;
            }
        }

        &-failed {
            background-color: $warm-grey;
            box-shadow: 0 5px 6px 0 $warm-grey;
        }
    }

    &__validation-message {
        color: $error-text !important;
        margin-top: -15px !important;
        font-weight: 500 !important;

        @include font-size-important(12);

        @include rtl-styles {
            text-align: right;
        }
    }
}

@keyframes rotating {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
