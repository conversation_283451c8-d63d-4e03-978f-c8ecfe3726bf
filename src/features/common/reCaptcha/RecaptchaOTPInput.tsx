import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'next-i18next';
import { FormHelperText, Input } from '@mui/material';
import styles from './RecaptchaOTPInput.module.scss';
import Button from '../button/Button';

// #. Set different states of the news subscription process
export enum CAPTCHA_BUTTON_STATE {
    INIT,
    SUBMITTED,
    SUCCESS,
    FAILED,
}

interface IRecaptchaOTPInput {
    titleText: string;
    onOTPEntered: (otp: string) => void;
    buttonTheme?: string | any;
    className?: string;
    errorMessage?: string | 'clear_msg';
    processState?: CAPTCHA_BUTTON_STATE;
}

/**
 * @method RecaptchaOTPInput
 * @param {recaptchaSiteKey}
 * @returns
 */
const RecaptchaOTPInput = ({
    buttonTheme = 'purple',
    className = '',
    titleText,
    onOTPEntered,
    errorMessage = '',
    processState = CAPTCHA_BUTTON_STATE.INIT,
}: IRecaptchaOTPInput) => {
    // #. Get translations
    const { t } = useTranslation('common');

    // #. set input ref
    const otpInput: any = useRef(null);

    const [validationMsg, setValidationMsg] = useState<string>('');

    const INVALID_MSG = t('invalidCode');

    /**
     * @method onChangeOTP
     */
    const onChangeOTP = (event: any) => {
        setValidationMsg(Boolean(event.target.value) ? '' : INVALID_MSG);
    };

    /**
     * @method onKeydownEvent
     */
    const onKeydownEvent = (event: any) => {
        const keyCode = event.keyCode;

        // #. Prevent adding char "e" from input
        // #. Prevent +,-,. from being add
        if (
            keyCode === 69 ||
            keyCode === 107 ||
            keyCode === 109 ||
            keyCode === 110 ||
            keyCode === 190
        ) {
            event.preventDefault();
            return false;
        } else if (keyCode === 13) {
            event.preventDefault();
            onSubmitOTP();
        }
    };

    /**
     * @method onSubmitOTP
     */
    const onSubmitOTP = () => {
        const otp = otpInput.current.value;
        if (!otp) {
            setValidationMsg(INVALID_MSG);
        } else {
            onOTPEntered && onOTPEntered(otp);
        }
    };

    useEffect(() => {
        const isClearMsg = errorMessage === 'clear_msg';
        setValidationMsg(isClearMsg ? '' : errorMessage);

        // #. Reset the value
        if (isClearMsg) {
            otpInput!.current!.value = '';
        }
    }, [errorMessage]);

    // #. Returns the scripts tag
    return (
        <div
            className={`${styles['recaptch-validation']} ${className} recaptch-validation`}
        >
            <span className={styles['recaptch-validation__title']}>
                {titleText}
            </span>
            <div className={styles['recaptch-validation__input-holder']}>
                <Input
                    type="number"
                    disableUnderline={true}
                    className={styles['recaptch-validation__input']}
                    placeholder={t('enteVerificationCode') || ''}
                    inputRef={otpInput}
                    onChange={onChangeOTP}
                    onKeyDown={onKeydownEvent}
                    onWheel={(e: any) => e.target.blur()}
                    error={true}
                />
                <Button
                    theme={buttonTheme}
                    action={onSubmitOTP}
                    className={`${styles['recaptch-validation__button']}
                  ${
                      processState === CAPTCHA_BUTTON_STATE.SUBMITTED
                          ? `${styles['recaptch-validation__button-submitted']} rc-button-submitted`
                          : ''
                  }

                  ${
                      processState === CAPTCHA_BUTTON_STATE.SUCCESS
                          ? `icon-checkbox-on-2 ${styles['recaptch-validation__button-success']} rc-button-success`
                          : ''
                  }

                  ${
                      processState === CAPTCHA_BUTTON_STATE.FAILED
                          ? `${styles['recaptch-validation__button-failed']} rc-button-failed`
                          : ''
                  }
          `}
                >
                    {t('submit')}
                </Button>
            </div>
            {validationMsg && (
                <FormHelperText
                    error={Boolean(validationMsg)}
                    className={
                        styles['recaptch-validation__validation-message']
                    }
                >
                    {validationMsg}
                </FormHelperText>
            )}
        </div>
    );
};

export default RecaptchaOTPInput;
