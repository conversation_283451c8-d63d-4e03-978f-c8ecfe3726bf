import { HAS_WINDOWS_REF } from '@utils/hasWindowRef';
import { useState } from 'react';

/**
 * @method useReCaptchaAPI
 * @reference https://developers.google.com/recaptcha/docs/v3
 * @version 3.0
 */
const useReCaptchaAPI = () => {
    const CAPTCHA_SESSION_KEY = 'YGAG-RC-V3';

    /**
     * @method setSiteKey
     * @description Set the key to session storage
     * @param siteKey
     */
    const setSiteKey = (siteKey: string) => {
        if (HAS_WINDOWS_REF) {
            sessionStorage.setItem(CAPTCHA_SESSION_KEY, siteKey);
        }
    };

    /**
     * @method getSiteKey
     * @description Get the key from session storage
     */
    const getSiteKey = () => {
        if (HAS_WINDOWS_REF) {
            return sessionStorage.getItem(CAPTCHA_SESSION_KEY);
        }
    };

    /**
     * @method evaluateCaptcha
     * @description Evaluvate the captcha the recieve the token
     */
    const evaluateCaptcha = (actionName: string) => {
        // #. Get the site key
        const siteKey = getSiteKey();

        // #. Throw error, when script not loaded
        let captcha = HAS_WINDOWS_REF ? (window as any).grecaptcha : false;
        console.log('captcha is ', captcha);
        console.log('site key is ', siteKey);
        if (!captcha || !siteKey) {
            throw new Error(
                'useReCaptchaAPI grecaptcha is missing from window object'
            );
        }
        try{
            return new Promise((resolve: (token: string) => void, reject) => {
                console.warn(siteKey, 'siteKey');
    
                captcha.ready(() => {
                    captcha
                        .execute(siteKey, { action: actionName })
                        .then((token: string) => {
                            resolve(token);
                        })
                });
            });
        } catch(error){
            console.log('error is ', error);
        }

        
    };

    return {
        evaluateCaptcha,
        setSiteKey
    };
};

export default useReCaptchaAPI;
