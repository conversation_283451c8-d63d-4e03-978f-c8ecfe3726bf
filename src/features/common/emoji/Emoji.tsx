import { useTranslation } from 'next-i18next';
import styles from './Emoji.module.scss';

// #. Types of emojies
// #. reference: https://unicode.org/emoji/charts/full-emoji-list.html
export const EMOJI_SYMBOLS: any = {
    VERY_HAPPY: '0x1f60d',
    HAPPY: '0x1f604',
    NOT_HAPPY: '0x1f641',
};

interface EmojiInterface {
    symbol: any;
    label?: string;
}

/**
 * @method Emoji
 * @description To show an emoji
 * @returns JSX Element
 */
const Emoji = ({ symbol, label }: EmojiInterface): JSX.Element => {
    const { t } = useTranslation('common');

    return (
        <div className={`emoji ${styles['emoji']}`}>
            {label && (
                <span className={`label ${styles['emoji__label']}`}>
                    {label}
                </span>
            )}
            <span
                className={`symbol ${styles['emoji__symbol']}`}
                role="img"
                aria-label={label ? label : ''}
            >
                {String.fromCodePoint(symbol)}
            </span>
        </div>
    );
};

export default Emoji;
