import React, { useEffect } from 'react';
import AudioPlayer from '../audioPlayer/AudioPlayer';
import { GetThemeSettingsData } from '../commonAPI';
import SnowfallEffect from '../snowfallEffect/SnowfallEffect';
import ToggleButtons from '../toggleButtons/ToggleButtons';
import { SNOW_ENABLED, VOLUME_ENABLED } from '@constants/common';

/**
 * @method ThemeSeter
 * @description Component to handle visual and audio theme
 * @returns {JSX.Element}
 */

const ThemeSeter = (): JSX.Element => {
    // #. Get theme settings data
    const { themeSettingsData } = GetThemeSettingsData();

    const { audioEnabled, snowEnabled, audio } =
        themeSettingsData?.themeConfig[0] || {};

    const clearCookie = (cookieName: string) => {
        if (document) {
            document.cookie =
                cookieName +
                '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
        }
    };

    // #. To clear cookie once disabled from BE
    useEffect(() => {
        if (audioEnabled === false) {
            clearCookie(VOLUME_ENABLED);
        }
        if (snowEnabled === false) {
            clearCookie(SNOW_ENABLED);
        }
    }, []);

    return (
        <>
            {snowEnabled && (
                <SnowfallEffect {...themeSettingsData?.themeConfig[0]} />
            )}
            {audioEnabled && <AudioPlayer audio={audio} />}
            <ToggleButtons
                isSnowEnabled={snowEnabled}
                isAudioEnabled={audioEnabled}
            />
        </>
    );
};

export default ThemeSeter;
