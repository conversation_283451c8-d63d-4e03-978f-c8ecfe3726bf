import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Dialog from '@mui/material/Dialog';
import { useTranslation } from 'next-i18next';
import styles from './Confirm.module.scss';
import Button from '../button/Button';

interface ConfirmationDialogProps {
    icon?: string;
    title?: string;
    message: string;
    confirmText?: string;
    open: boolean;
    dialogProps?: any;
    className?: string;
    onClose: (isConfirmed: boolean) => void;
    showCancel?: boolean;
    fullsizeButton?: boolean;
}

/**
 * @method Confirm
 * @description A common component usding as a confirmation dialog
 * @param param0
 * @returns
 */
const Confirm = ({
    icon,
    title,
    message,
    confirmText,
    open,
    dialogProps = {},
    className,
    onClose,
    showCancel = true,
    fullsizeButton = false,
}: ConfirmationDialogProps) => {
    // #. Get translations
    const { t } = useTranslation('common');

    /**
     * @method onConfirm
     */
    const onConfirm = (isConfirm: boolean) => {
        onClose && onClose(isConfirm);
    };

    return (
        <Dialog
            open={open}
            {...dialogProps}
            className={`confirm-dialog ${styles['confirm-dialog']} ${className}`}
        >
            <div
                className={`confirm-dialog-container ${styles['confirm-dialog-container']}`}
            >
                {icon && (
                    <img
                        src={icon}
                        className={`confirm-dialog-icon ${styles['confirm-dialog-icon']}`}
                    />
                )}
                <div
                    className={`confirm-dialog-section ${styles['confirm-dialog-section']}`}
                >
                    {title && (
                        <DialogTitle
                            className={`confirm-dialog-title ${styles['confirm-dialog-title']}`}
                        >
                            {title}
                        </DialogTitle>
                    )}
                    <DialogContent
                        className={`confirm-dialog-content ${styles['confirm-dialog-content']}`}
                    >
                        {message}
                    </DialogContent>
                </div>
                <DialogActions
                    className={`confirm-dialog-buttons ${styles['confirm-dialog-buttons']}`}
                >
                    {showCancel && (
                        <Button
                            action={() => onConfirm(false)}
                            theme="white-theme2"
                            borderTheme="purple"
                            className={`confirm-dialog-button-cancel ${styles['confirm-dialog-button-cancel']}`}
                        >
                            {t('cancel')}
                        </Button>
                    )}
                    <Button
                        action={() => onConfirm(true)}
                        theme="purple"
                        className={`confirm-dialog-button-confirm ${
                            styles['confirm-dialog-button-confirm']
                        } ${
                            fullsizeButton
                                ? styles['confirm-dialog-button-fullsize']
                                : ''
                        }`}
                    >
                        {confirmText ? confirmText : t('ok')}
                    </Button>
                </DialogActions>
            </div>
        </Dialog>
    );
};

export default Confirm;
