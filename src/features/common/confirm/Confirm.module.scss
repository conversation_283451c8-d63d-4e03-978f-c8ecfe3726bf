@import '@styles/variables';
@import '/src/styles/mixins';
.confirm-dialog {
    &-container {
        padding: 20px;
        top: 100px !important;
    }

    &-icon {
        width: 16px;
        height: 16px;
    }

    &-content {
        border-radius: 6px;
        padding: 0px !important;
        margin-bottom: 16px !important;
        color: $dark-charcoal;
        font-family: $mona-sans-font-family;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: -0.16px;

        @include rtl-styles{
            font-family: $arabic-font-family;
        }
    }
    &-title {
        font-size: 24px !important;
        font-weight: 800 !important;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: $dark-charcoal;
        margin-bottom: 16px !important;
        @include rtl-styles {
            text-align: right;
        }
    }

    &-buttons {
        padding-left: 0px !important;
        padding-right: 0px !important;
        button {
            padding: 5px 15px;
            width: 100%;
        }
    }
    &-button-cancel {
        border-radius: 12px !important;
        height: 50px;
        width: 182px;
        border-color: $dark-charcoal; 
        border-radius: 12px;
        border-top: 2px solid $dark-charcoal;
        border-right: 3px solid $dark-charcoal;
        border-bottom: 4px solid $dark-charcoal;
        border-left: 2px solid $dark-charcoal;
        background-color: $white;
        span {
            color: $dark-charcoal;
            font-size: 16px;
            font-weight: 500;
        }
         @media only screen and (max-width: $sm) {
            width: 100%;
        }
    }
    &-button-confirm {
        border-radius: 12px !important;
        height: 50px;
        width: 182px;
        background-color: $dark-charcoal !important;
        span {
            font-size: 16px;
            font-weight: 500;
        }
        @include rtl-styles {
            margin-right: 8px;
        }
        @media only screen and (max-width: $sm) {
            width: 100%;
        }
    }

    &-button-fullsize{
        width: 100% !important;
    }
}
