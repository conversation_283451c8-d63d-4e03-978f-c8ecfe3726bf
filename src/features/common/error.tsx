import React, { Component } from 'react';
import * as Sentry from '@sentry/nextjs';
interface ErrorBoundaryState {
    hasError: boolean;
}

export class ErrorBoundary extends React.Component<any, ErrorBoundaryState> {
    constructor(props: any) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: any) {
        return { hasError: true };
    }

    componentDidCatch(error: any, info: any) {
        console.log("error", error);
        console.log("error message", error?.message);
        try {
            if (error?.message !== "social_signup") {
                console.error({ error, info });
                Sentry.captureMessage("Error in error boundary");
                Sentry.captureException({ error, info });
            }
        } catch (err) {
            console.error("Failed to process error data:", err);
            Sentry.captureException(err);
        }
    }

    render() {
        // if (this.state.hasError) {
        //     return this.props.fallback;
        // }

        return this.props.children;
    }
}
