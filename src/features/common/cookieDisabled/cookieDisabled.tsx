import CommonLayout from "@features/layouts/common"
import { useTranslation } from "next-i18next";
import getConfig from "next/config";
import Image from 'next/image';

const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const CookieDisabled = ({statusCode} : any) => {
    const { t } = useTranslation('common');

    return (
        <div data-testid="error-page" className="error-page-wrapper">
            <CommonLayout>
                <div className="error-page">
                    <h2 data-testid="errorTitle">{t('errorTitle')}</h2>
                    <h5 data-testid="errorSubTitle">{t('cookieDisabled')}</h5>
                    <Image
                        src={`${imageBaseUrl}/images/error-page.png`}
                        alt="error-page-icon"
                        height={164}
                        width={135}
                        data-testid="error-image"
                    />
                </div>
            </CommonLayout>
        </div>
    )
}

export default CookieDisabled;