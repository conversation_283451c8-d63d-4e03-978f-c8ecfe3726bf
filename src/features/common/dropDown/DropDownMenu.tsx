import React from 'react';

import styles from './DropDownMenu.module.scss';
import { DropDownMenuIterface } from '@interfaces/common.inteface';

/**
 * @method DropDownMenu
 * @description Dropdown component
 * @returns {JSX.Element}
 */

const DropDownMenu = ({
    children,
    maxWidth,
    dropDown,
    className = '',
}: DropDownMenuIterface): JSX.Element => {
    return (
        <>
            <div
                className={`${styles['drop-down-menu']} ${
                    dropDown ? styles['drop-down-menu--active'] : ''
                } ${className}`}
                style={maxWidth ? { maxWidth: maxWidth } : {}}
                data-testid="dropDownMenu"
            >
                {children}
            </div>
        </>
    );
};

export default DropDownMenu;
