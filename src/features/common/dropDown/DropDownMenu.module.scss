@import '@styles/variables';
@import '@styles/mixins';

.drop-down-menu {
    border-radius: 0 0 12px 12px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 100%;
    background: $white;
    overflow-y: auto;
    overflow-x: hidden;
    min-width: 16px;
    outline: 0;
    transition: all 300ms ease-in;
    opacity: 0;
    height: 0;

    &--active {
        height: fit-content;
        opacity: 1;
        box-shadow: 0 20px 60px 0 rgba($black, 0.16);
    }
}
