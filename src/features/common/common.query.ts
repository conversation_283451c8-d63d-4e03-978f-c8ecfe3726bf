import { gql } from '@apollo/client';

export const SITE_META = gql`
    query ($platformType_Code: String!, $store: String!) {
        siteConfigs(platformType_Code: $platformType_Code, store: $store) {
            edges {
                node {
                    loginPageSiteMeta {
                        id
                        title
                        description
                        keywords
                    }
                    ipCountry
                }
            }
        }
    }`;

export const GUEST_LOGIN = gql`
mutation ($data: String!){
    guestLogin(data:$data){
        login{
            message
            status
        }
    }
}`;

export const GUEST_ENABLED = gql`
query{
    cartConfig{
        isGuestEnabled
    }
}`;

export const GUEST_LOGOUT = gql`
mutation($user: String){
    guestLogout(user:$user){
        logout{
            message
        }
    }
}`;

export const LOGO_QUERY = gql`
    query ($platformType_Code: String!) {
        headers(platformType_Code: $platformType_Code) {
            edges {
                node {
                    logo
                }
            }
        }
    }
`;

export const THEME_SETTINGS_QUERY = gql`
    query themeSettings {
        themeConfig {
            name
            audio
            audioEnabled
            snowFlakeCount
            snowEnabled
            speedMin
            speedMax
            windMax
            windMin
            radiusMax
            radiusMin
            useImage
            image
            rotationSpeedMin
            rotationSpeedMax
            color
        }
    }
`;

export const CAPTCHA_CONFIG_QUERY = gql`
query {
    captchaConfig{
      actionName
      captchaVersion
      hasCaptchaEnabled
    }
  }
`;

export const BLACK_LISTED_CONTRIES_QUERY = gql`
    query BlacklistedCountriesQuery {
        blacklistedCountries
    }
`;

export const COMMUNICATION_CONFIG_QUERY = gql`
  query communicationConfigsQuery(
    $countryCode: String!
    $flow: MessageType!
    $channel: ChannelType!
  ) {
    communicationConfigs(
      countryCode: $countryCode
      flow: $flow
      channel: $channel
    ) {
      resendDeliveryEnabled
    }
  }
`;