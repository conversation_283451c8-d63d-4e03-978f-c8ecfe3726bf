import Head from 'next/head';

const Meta = ({ loginPageSiteMeta, activeSlug }: any) => {
    return (
        <>
            <Head>
                <title>{loginPageSiteMeta?.title}</title>
                <meta
                    name="description"
                    content={loginPageSiteMeta?.description}
                />
                <meta name="keywords" content={loginPageSiteMeta?.keywords} />
                {activeSlug === '' && (
                    <meta name="robots" content="index,follow" />
                )}
            </Head>
        </>
    );
};

export default Meta;
