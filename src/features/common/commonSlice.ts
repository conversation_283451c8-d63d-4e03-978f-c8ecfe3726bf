import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { AppState } from '@redux/store';
import { NotifierInterface } from '@interfaces/common.inteface';
import { HYDRATE } from 'next-redux-wrapper';
export interface CommonSlice {
    isLoading: boolean;
    notifier: NotifierInterface;
    ipAddress: string;
    enableVolume: boolean;
    enableSnow: boolean;
    isCaptchaRequired:boolean;
}

const initialState: CommonSlice = {
    isLoading: true,
    notifier: {
        title: '',
        description: '',
    },
    ipAddress: '',
    enableVolume: true,
    enableSnow: true,
    isCaptchaRequired: true,
};

// #. Common slice contains the common state methods
export const commonSlice = createSlice({
    name: 'common',
    initialState,
    reducers: {
        showLoader: (state, action: PayloadAction<boolean>) => {
            state.isLoading = action.payload;
        },
        setNotifierState: (state, action: PayloadAction<NotifierInterface>) => {
            state.notifier = action.payload;
        },
        setIpAddress: (state, action: PayloadAction<string>) => {
            state.ipAddress = action.payload;
        },
        setEnableVolume: (state, action: PayloadAction<any>) => {
            state.enableVolume = action.payload;
        },
        setEnableSnow: (state, action: PayloadAction<any>) => {
            state.enableSnow = action.payload;
        },
        setIsCaptchaRequired: (state, action: PayloadAction<any>) => {
            state.isCaptchaRequired = action.payload;
        },
    },
    extraReducers(builder) {
        builder.addCase<
            typeof HYDRATE,
            PayloadAction<AppState, typeof HYDRATE>
        >(HYDRATE, (state, { payload }) => ({ ...state, ...payload.common }));
    },
});

export const {
    showLoader,
    setNotifierState,
    setIpAddress,
    setEnableVolume,
    setEnableSnow,
    setIsCaptchaRequired,
} = commonSlice.actions;

// #. State for loader component
export const selectIsLoading = (state: AppState) => state.common.isLoading;

// #. State of the notifier component
export const getNotifierState = (state: AppState) => state.common.notifier;

// #. State of Ip Address
export const getIpAddress = (state: AppState) => state.common.ipAddress;

// #. State of Volume effect
export const getEnableVolume = (state: AppState) => state.common.enableVolume;

// #. State of Snow effect
export const getEnableSnow = (state: AppState) => state.common.enableSnow;

// #. State of captcha config
export const getIsCaptchaRequired = (state: AppState) => state.common.isCaptchaRequired;

// #. Export the reducers
export default commonSlice.reducer;
