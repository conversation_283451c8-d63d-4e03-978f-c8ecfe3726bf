import { ThemeSettingsInterface } from '@interfaces/common.inteface';
import { useAppSelector } from '@redux/hooks';
import getConfig from 'next/config';
import React, { useEffect, useState } from 'react';
import Snowfall from 'react-snowfall';
import { getEnableSnow } from '../commonSlice';

interface SnowfallPropsInterface {
    snowflakeCount: number;
    color: string;
    speed: [number, number];
    radius: [number, number];
    images?: never[];
    wind: [number, number];
    rotationSpeed: [number, number];
}

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method SnowfallEffect
 * @description Snowfall effect component
 * @returns {JSX.Element}
 */

const SnowfallEffect = ({
    snowFlakeCount,
    speedMin,
    speedMax,
    windMax,
    windMin,
    radiusMax,
    radiusMin,
    useImage,
    image,
    rotationSpeedMin,
    rotationSpeedMax,
    color,
}: ThemeSettingsInterface): JSX.Element => {
    const snowflakeImage = `${imageBaseUrl}/icons/snowflake.png`;

    // #. Get snowfall effect enabler from redux
    const enableSnow = useAppSelector(getEnableSnow);

    const [images, setImages] = useState([]);

    useEffect(() => {
        var snowflake = document.createElement('img');
        snowflake.src = snowflake.src = image;
        const images: any = [snowflake, snowflake];
        setImages(images);
    }, []);

    const snowfallProps: SnowfallPropsInterface = {
        snowflakeCount: snowFlakeCount || 200,
        color: color,
        speed: [Number(speedMin), Number(speedMax)] || [2, 2],
        radius: [Number(radiusMin), Number(radiusMax)] || [5, 20],
        images: useImage ? images || snowflakeImage : undefined,
        wind: [Number(windMin), Number(windMax)] || [-0.5, 2.0],
        rotationSpeed: [rotationSpeedMin, rotationSpeedMax] || [-1.0, 1.0],
    };

    return (
        <>
            {enableSnow && (
                <Snowfall
                    {...snowfallProps}
                    style={{
                        position: 'fixed',
                        width: '100vw',
                        height: '100vh',
                        zIndex: 1000,
                    }}
                />
            )}
        </>
    );
};

export default SnowfallEffect;
