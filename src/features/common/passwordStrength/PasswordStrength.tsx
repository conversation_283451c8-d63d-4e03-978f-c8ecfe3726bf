import { PASSWORD_STRENGTH } from '@constants/common';
import hasDigit from '@utils/hasDigit';
import hasSpecialCharacter from '@utils/hasSpecialCharacter';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import styles from './PasswordStrength.module.scss';

const PasswordStrength = ({ password, passwordStrengthCallback }: any) => {
    const { t } = useTranslation('common');

    const [minCharStrength, setMinCharStrength] = useState(false);

    const [specialCharStrength, setSpecialCharStrength] = useState(false);

    const [oneDigitStrength, setOneDigitStrength] = useState(false);

    useEffect(() => {
        let minCharStrength = false,
            specialCharStrength = false,
            oneDigitStrength = false;
        if (!password || !password.length) return;
        if (password.length >= 6) {
            minCharStrength = true;
            setMinCharStrength(true);
        } else {
            minCharStrength = false;
            setMinCharStrength(false);
        }
        if (hasSpecial<PERSON>haracter(password)) {
            setSpecialCharStrength(true);
            specialCharStrength = true;
        } else {
            setSpecialCharStrength(false);
            specialCharStrength = false;
        }
        if (hasDigit(password)) {
            setOneDigitStrength(true);
            oneDigitStrength = true;
        } else {
            setOneDigitStrength(false);
            oneDigitStrength = false;
        }

        passwordStrengthCallback(
            minCharStrength && specialCharStrength && oneDigitStrength
        );
    }, [password]);

    return (
        <div className={`password-info ${styles['password-info']}`}>
            {t('chooseASecurePasswordWhich')}:
            <ul>
                <li
                    className={
                        styles[`password${minCharStrength ? '-ok' : '-bad'}`]
                    }
                >
                    &bull; {t('hasMinimum6Characters')}
                </li>
                <li
                    className={
                        styles[
                            `password${specialCharStrength ? '-ok' : '-bad'}`
                        ]
                    }
                >
                    &bull; {t('has1SpecialCharacter')}
                </li>
                <li
                    className={
                        styles[`password${oneDigitStrength ? '-ok' : '-bad'}`]
                    }
                >
                    &bull; {t('has1Digit')}
                </li>
            </ul>
        </div>
    );
};

export default PasswordStrength;
