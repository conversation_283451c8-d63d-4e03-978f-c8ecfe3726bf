import {
    clevertapEvents,
    clevertapEventsProperties,
} from '@constants/clevertap';
import {LANGUAGES, USER_TYPES } from '@constants/common';

function pushEvent(eventName: string, payload: {}) {
    typeof window !== 'undefined' &&
        window?.clevertap?.event?.push(eventName, payload);
}

export const cleverTapService = {
    pushLoginSignup,
    pushGuestLogin,
    pushClevertapProfile,
};

function pushLoginSignup() {
    const platform = localStorage.getItem('platform');
    const eventData = {
        [clevertapEventsProperties.PLATFORM]:
            platform === 'app' ? localStorage.getItem('AppPlatform') : platform?.toUpperCase(),
        [clevertapEventsProperties.LOGIN_METHOD]: localStorage
            .getItem('authMethod')
            ?.toString()
            .toUpperCase(),
        [clevertapEventsProperties.STORE]: localStorage
            .getItem('selectedStore')
            ?.toString()
            .toUpperCase(),
        [clevertapEventsProperties.USER_TYPE]: USER_TYPES.LOGGED_IN,
    };

    pushEvent(clevertapEvents.LOGIN_SIGNUP, eventData);
}

function pushGuestLogin() {
    const platform = localStorage.getItem('platform');
    const eventData = {
        [clevertapEventsProperties.PLATFORM]:
            platform === 'app'
                ? localStorage.getItem('AppPlatform')
                : platform?.toUpperCase(),
        [clevertapEventsProperties.STORE]: localStorage
            .getItem('selectedStore')
            ?.toString()
            .toUpperCase(),
        [clevertapEventsProperties.USER_TYPE]: USER_TYPES.GUEST,
    };

    pushEvent(clevertapEvents.GUEST_LOGIN, eventData);
}


function pushClevertapProfile(
    email: string,
    name: string,
    locale: string = 'en'
) {
    typeof window !== 'undefined' &&
        window?.clevertap.onUserLogin.push({
            Site: {
                Identity: email,
                Email: email,
                Name: name,
                Language: locale == 'ar' ? LANGUAGES.AR : LANGUAGES.EN,
            },
        });
}
