import { useEffect, useState } from 'react';
import clevertap from 'clevertap-web-sdk';

function Clevertap({ clevertapAccountId }: { clevertapAccountId: string }) {
    const [clevertapModule, setClevertapModule] = useState<
        typeof clevertap | null
    >(null);

    useEffect(() => {
        const clevertapInit = async () => {
            if (!clevertapModule) {
                const initializedModule = await initializeClevertap();
                setClevertapModule(initializedModule);
            }
        };

        clevertapInit();
    }, [clevertapModule]);

    const initializeClevertap = async () => {
        const clevertapModule = await import('clevertap-web-sdk');
        clevertapModule.default.init(clevertapAccountId);
        clevertapModule.default.privacy.push({ optOut: false });
        clevertapModule.default.privacy.push({ useIP: false });
        clevertapModule.default.setLogLevel(0);
        return clevertapModule.default;
    };

    return null;
}

export default Clevertap;
