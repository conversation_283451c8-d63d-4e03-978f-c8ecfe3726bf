@import '@styles/variables';
@import '@styles/mixins';

.placeholder {
    height: 400px;
    margin: 10px 0;
    padding-top: 80px;
}

.spinner {
    margin: 0 auto;
    width: 50px;
    height: 40px;
    text-align: center;
    font-size: 10px;

    & > div {
        background-color: var(--barney-purple);
        height: 100%;
        width: 6px;
        display: inline-block;
        animation: sk-stretchdelay 1.2s infinite ease-in-out;
    }

    .rect2 {
        animation-delay: -1.1s;
    }

    .rect3 {
        animation-delay: -1s;
    }

    .rect4 {
        animation-delay: -0.9s;
    }

    .rect5 {
        animation-delay: -0.8s;
    }
}

@keyframes sk-stretchdelay {
    0%,
    40%,
    100% {
        transform: scaleY(0.4);
    }

    20% {
        transform: scaleY(1);
    }
}

.align-flex {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.placeholder-container {
    max-width: 800px;
    margin: 0 auto;
    height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
