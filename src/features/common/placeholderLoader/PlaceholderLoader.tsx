import { Skeleton } from '@mui/material';
import React from 'react';
import styles from './PlaceholderLoader.module.scss';

const PlaceholderLoader = ({
    flex,
    contentLoader,
}: {
    flex?: boolean;
    contentLoader?: 'text' | 'circular' | 'rectangular';
}) => {
    return (
        <>
            {contentLoader ? (
                <div
                    className={styles['placeholder-container']}
                    data-testid="placeholderContentLoader"
                >
                    {contentLoader === 'text' ? (
                        <>
                            <Skeleton
                                variant={contentLoader}
                                animation="wave"
                            />
                            <Skeleton
                                variant={contentLoader}
                                animation="wave"
                            />
                            <Skeleton
                                variant={contentLoader}
                                animation="wave"
                            />
                        </>
                    ) : (
                        <Skeleton variant={contentLoader} animation="wave" />
                    )}
                </div>
            ) : (
                <div
                    className={`${styles.placeholder} ${
                        flex && styles['align-flex']
                    }`}
                    data-testid="placeholder"
                >
                    <div className={styles.spinner}>
                        <div className={styles.rect1}></div>
                        <div className={styles.rect2}></div>
                        <div className={styles.rect3}></div>
                        <div className={styles.rect4}></div>
                        <div className={styles.rect5}></div>
                    </div>
                </div>
            )}
        </>
    );
};
export default PlaceholderLoader;
