import React from 'react';
import { cleanup, render, RenderResult } from '@redux/test-utils';
import PlaceholderLoader from './PlaceholderLoader';

jest.mock('react-i18next', () => ({
    useTranslation: () => ({ t: (key: any) => key }),
}));

describe('Loader', () => {
    let component: RenderResult<
        typeof import('@testing-library/dom/types/queries'),
        HTMLElement
    >;

    afterEach(cleanup);

    it('should render my component', () => {
        component = render(<PlaceholderLoader />);
        expect(component).toMatchSnapshot();
    });

    it('should have loader', () => {
        const { getByTestId } = render(<PlaceholderLoader />);

        // #. Check loader image has a src value
        expect(getByTestId('placeholder')).toBeInTheDocument();
    });

    it('should have content loader', () => {
        const { getByTestId } = render(
            <PlaceholderLoader contentLoader="text" />
        );

        // #. Check loader image has a src value
        expect(getByTestId('placeholderContentLoader')).toBeInTheDocument();
    });
});
