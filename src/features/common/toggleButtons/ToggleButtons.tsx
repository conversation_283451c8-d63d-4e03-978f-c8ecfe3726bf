import { useAppSelector } from '@redux/hooks';
import getConfig from 'next/config';
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
    getEnableSnow,
    getEnableVolume,
    setEnableSnow,
    setEnableVolume,
} from '../commonSlice';
import ToggleButton from './toggleButton/ToggleButton';
import styles from './ToggleButtons.module.scss';
import { HAS_WINDOWS_REF } from '@utils/hasWindowRef';
import { getCookie, setCookie } from '@utils/getAndsetCookie';
import { SNOW_ENABLED, VOLUME_ENABLED } from '@constants/common';



const {
    publicRuntimeConfig: { imageBaseUrl, cookieDomain },
} = getConfig();

/**
 * @method ToggleButtons
 * @description Toggle buttons for snowfall and audio effects
 * @returns {JSX.Element}
 */

const ToggleButtons = ({
    isSnowEnabled,
    isAudioEnabled,
}: {
    isSnowEnabled: boolean;
    isAudioEnabled: boolean;
}): JSX.Element => {
    //# . Button Icons
    const toggleVolumeIcon = `${imageBaseUrl}/icons/toggle-volume.svg`;
    const toggleEffectIcon = `${imageBaseUrl}/icons/toggle-effect.svg`;

    // #. Get snowfall effect enablers
    const enableVolume = useAppSelector(getEnableVolume);
    const enableSnow = useAppSelector(getEnableSnow);

    const dispatch = useDispatch();

// #. Check if cookie values are available for Theme configuration
useEffect(() => {
    setTimeout(() => {
      if (HAS_WINDOWS_REF && getCookie(VOLUME_ENABLED) == "true") {
        dispatch(setEnableVolume(true));
      } else if (HAS_WINDOWS_REF && getCookie(VOLUME_ENABLED) == "false") {
        dispatch(setEnableVolume(false));
      }

      if (HAS_WINDOWS_REF && getCookie(SNOW_ENABLED) == "true") {
        dispatch(setEnableSnow(true));
      } else if (HAS_WINDOWS_REF && getCookie(SNOW_ENABLED) == "false") {
        dispatch(setEnableSnow(false));
      }
    }, 50);

  }, []);

    // #. Method to dispatch button button states
    const onButtonClick = (enableSnow: boolean, enableVolume: boolean) => {
        dispatch(setEnableSnow(enableSnow));
        dispatch(setEnableVolume(enableVolume));
        if (HAS_WINDOWS_REF) {
            setCookie(SNOW_ENABLED, String(enableSnow), 60, cookieDomain);
            setCookie(VOLUME_ENABLED, String(enableVolume), 60, cookieDomain);
          }
    };

    return (
        <div className={styles['toggle-buttons__container']}>
            {isAudioEnabled && (
                <ToggleButton
                    enabled={enableVolume}
                    onClick={() => onButtonClick(enableSnow, !enableVolume)}
                    icon={toggleVolumeIcon}
                    altText="Toggle Volume"
                />
            )}
            {isSnowEnabled && (
                <ToggleButton
                    enabled={enableSnow}
                    onClick={() => onButtonClick(!enableSnow, enableVolume)}
                    icon={toggleEffectIcon}
                    altText="Toggle Snow"
                />
            )}
        </div>
    );
};

export default ToggleButtons;
