import React from "react";
import styles from "../ToggleButtons.module.scss";

/**
 * @method ToggleButton
 * @description Toggle button
 * @returns {JSX.Element}
 */
const ToggleButton = ({
  enabled,
  onClick,
  icon,
  altText,
}: {
  enabled: boolean;
  onClick: () => void;
  icon: string;
  altText: string;
}): JSX.Element => {
  return (
    <div
      className={styles["toggle-buttons__container__button"]}
      onClick={onClick}
    >
      {!enabled ? (
        <>
          <span
            className={styles["toggle-buttons__container__button__toggle-off"]}
          />
          <div
            className={styles["toggle-buttons__container__button__inactive"]}
          >
            <img src={icon} alt={altText} />
          </div>
        </>
      ) : (
        <img src={icon} alt={altText} />
      )}
    </div>
  );
};

export default ToggleButton;
