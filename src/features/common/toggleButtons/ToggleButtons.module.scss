@import '@styles/variables';
@import '@styles/mixins';

.toggle-buttons {
    &__container {
        position: fixed;
        top: 56%;
        right: 0;
        transform: translate(0, -50%);
        z-index: 1;

        &__button {
            cursor: pointer;
            width: 80px;
            height: 50px;
            padding: 13.9px 34px 13.9px 21px;
            border-radius: 40px 0 0 40px;
            border: solid 2px $pale-grey-bg;
            background-color: $white;
            display: flex;
            justify-content: center;
            align-items: center;

            &:first-child {
                margin-bottom: 16px;
            }

            &__toggle-off {
                position: absolute;
                width: 30px;
                height: 3px;
                flex-grow: 0;
                padding: 0.6px 0.5px 0.6px;
                transform: rotate(-318.99deg);
                background-color: #b800c4;
                border: 0.5px solid white;
            }

            &__inactive {
                opacity: 0.5;
                display: flex;
            }
        }

        @media (max-height: (1050px)) {
            top: 66%;
        }

        @media (max-height: (958px)) {
            top: 70%;
        }

        @media (max-height: (900px)) {
            top: 75%;
        }
    }
}
