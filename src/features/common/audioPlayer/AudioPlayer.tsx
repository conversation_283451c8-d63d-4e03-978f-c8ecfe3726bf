import { useAppSelector } from "@redux/hooks";
import React, { useEffect, useRef } from "react";
import { getEnableVolume } from "../commonSlice";

/**
 * @method AudioPlayer
 * @description Audio player component
 * @returns {JSX.Element}
 */

const AudioPlayer = ({ audio }: any): JSX.Element => {
  // #. Get audio enabler from redux
  const enableVolume = useAppSelector(getEnableVolume);

  // const xmasAudio = `${imageBaseUrl}/audios/xmas.mp3`;
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.play();
    }
  }, []);

  return (
    <>
      {enableVolume && (
        <audio ref={audioRef} controls autoPlay loop hidden>
          <source src={audio} type="audio/ogg" />
        </audio>
      )}
    </>
  );
};

export default AudioPlayer;
