import React from 'react';

import styles from './Button.module.scss';
import { ButtonInterface } from '@interfaces/common.inteface';

/**
 * @method Button
 * @description Button component
 * @returns {JSX.Element}
 */

const Button = ({
    theme,
    className,
    action,
    children,
    borderTheme,
    arrow,
    id,
    attribues = {},
    buttonRef,
    loadingState = "complete"
}: ButtonInterface): JSX.Element => {
    const {disabled} = attribues;
    return (
        
        <div className={`${styles['btn-wrapper']} ${loadingState == "complete" && !disabled ? styles['btn-wrapper__bg'] : ""}`}>
             <button
            className={`
      button ${styles['button']} ${styles['button--' + theme]} 
      ${className ? className : ''} 
      ${borderTheme ? styles['border'] : ''} 
      ${borderTheme ? styles['border--' + borderTheme] : ''} 
      ${arrow ? styles['arrow-enabled'] : ''}
      ${disabled ? styles['no-pointer'] : ''}
      `}
            data-testid="themedButton"
            onClick={action}
            id={id}
            {...attribues}
            ref={buttonRef}
        >
            {children && (
                <span className={`${styles['button-label']} button-label`}>
                    {children}
                </span>
            )}
            {arrow === 'arrow-forward' && (
                <span
                    className={`${styles[theme + '-icon-wrapper']} ${
                        styles['icon-wrapper']
                    } button-icon-wrapper`}
                >
                    <i className={`icon-${arrow}`}></i>
                </span>
            )}
        </button>
        </div>
       
    );
};

export default Button;
