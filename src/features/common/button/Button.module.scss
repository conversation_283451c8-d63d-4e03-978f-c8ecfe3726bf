@import '@styles/variables';
@import '@styles/mixins';

%icon-wrapper {
    width: 40px;
    height: 40px;
    display: inline-block;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: transform ease-in-out 400ms;
}

.button {
    padding: 4px 4px 4px 10px;
    cursor: pointer;
    border: 0;
    border-radius: 12px;
    animation: returnToOriginal 0.3s ease forwards;

    @include rtl-styles{
      animation: returnToOriginalRtl 0.3s ease forwards;
    }

    &:disabled {
        opacity: 0.3;
        box-shadow: none;
    }

    &--white {
        background: $white;
        color: $cornflower-blue;
    }

    &--white-theme2 {
        background: $white;
        color: $barney-purple;
    }

    &--white-large {
        background: $white;
        color: $barney-purple;
        padding: 10px 73px;
        @include font-size(16);

        &:focus {
            transform: translateY(1px);
        }
    }

    &--purple {
        background: $barney-purple;
        color: $white;
    }

    &--purple-large {
        background: $barney-purple;
        color: $white;
        padding: 10px 73px;
        @include font-size(16);

        &:focus {
            transform: translateY(1px);
        }
    }

    &--transparent {
        background: transparent;
        color: $barney-purple;
        padding: 0;
        @include font-size(16);

        &:focus {
            transform: translateY(1px);
        }
    }

    &:hover {
        animation: bounceUp 0.2s ease forwards;
  
        @include rtl-styles{
          animation: bounceUpRtl 0.2s ease forwards;
        }

        .icon-wrapper {
            transform: translateX(4px);
        }
    }

    .large {
        padding: 0;
    }
}

.arrow-enabled {
    display: flex;
    align-items: center;

    .button-label {
        padding: 5px 25px 5px 52px;
        display: inline-block;
    }
}

.border {
    border: 1px solid transparent;

    &--purple {
        border-color: $barney-purple;
    }
}

.white {
    &-icon-wrapper {
        @extend %icon-wrapper;

        background-color: rgba($cornflower-blue, 10%);
    }
}

.btn-wrapper{
    border-radius: 12px;
    width: 100%;
    display: flex;
    justify-content: center;
    
    &__bg{
      background: linear-gradient(90deg, #FF65C1, #8168FF, #FF7A7A, #2082FF);
    }

    @media only screen and (max-width: $sm) {
      &__bg{
        background: none;
      }
    }
  }


  // Define animations for each direction
@keyframes bounceUp {
    0% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(1px, -1px);
    }
    50% {
      transform: translate(2px, -2px);
    }
    75% {
      transform: translate(3px, -3px);
    }
    100% {
      transform: translate(4px, -4px);
    }
  }
  
  @keyframes returnToOriginal {
    0% {
      transform: translate(4px, -4px);
    }
    100% {
      transform: translate(0, 0);
    }
  }
  
  @keyframes bounceUpRtl {
    0% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(-1px, -1px);
    }
    50% {
      transform: translate(-2px, -2px);
    }
    75% {
      transform: translate(-3px, -3px);
    }
    100% {
      transform: translate(-4px, -4px);
    }
  }
  
  @keyframes returnToOriginalRtl {
    0% {
      transform: translate(-4px, -4px);
    }
    100% {
      transform: translate(0, 0);
    }
  }

  .no-pointer{
    pointer-events: none;
  }