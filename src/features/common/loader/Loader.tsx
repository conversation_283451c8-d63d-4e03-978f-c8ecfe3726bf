import { useTranslation } from 'next-i18next';
import getConfig from 'next/config';
import Image from 'next/image';
import styles from './Loader.module.scss';

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method Loader
 * @description To show a loader wrapper on poastback and other API calls
 * @returns JSX Element
 */
const Loader = (): JSX.Element => {
    const { t } = useTranslation('common');

    return (
        <div className={styles.loader}>
            <div className={styles.loader__image}>
                <Image
                    src={`${imageBaseUrl}/icons/loader.gif`}
                    width={80}
                    height={80}
                    data-testid="loaderImage"
                    alt=""
                    priority
                />
            </div>
        </div>
    );
};

export default Loader;
