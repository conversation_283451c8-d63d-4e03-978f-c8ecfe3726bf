import React from 'react';
import { cleanup, render, RenderResult } from '@redux/test-utils';
import Loader from './Loader';
jest.mock('react-i18next', () => ({
    useTranslation: () => ({ t: (key: any) => key }),
}));

describe('Loader', () => {
    let component: RenderResult<
        typeof import('@testing-library/dom/types/queries'),
        HTMLElement
    >;

    afterEach(cleanup);

    it('should render my component', () => {
        component = render(<Loader />);
        expect(component).toMatchSnapshot();
    });

    it('should have loader image', () => {
        const { getByTestId } = render(<Loader />);

        // #. Check loader image has a src value
        expect(getByTestId('loaderImage')).toHaveAttribute('src');
    });
});
