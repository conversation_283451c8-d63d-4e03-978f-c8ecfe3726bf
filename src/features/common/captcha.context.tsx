import getConfig from 'next/config';
import { useRouter } from 'next/router';
import React, { useState, useRef, createContext, useEffect } from 'react';
import ReCAPTCHA from 'react-google-recaptcha';

const {
    publicRuntimeConfig: { captchSiteKey },
} = getConfig();

export const CaptchaContext = createContext({});

const CaptchaContextProvider = ({ children }: any) => {
    const [token, setToken] = useState('');
    const [tokenGenerated, setTokenGenerated] = useState(false);
    const [appLocale, setAppLocale] = useState<string>('en');
    const reCaptchaRef: any = useRef({});
    const router = useRouter();

    const reset = () => {
        setTokenGenerated(false);
    };
    const value = {
        elem: reCaptchaRef,
        tokenGenerated: tokenGenerated,
        token: token,
        function: { reset },
    };

    const onError = (error?: any) => {
        console.log('Error in captcha ', error);
    };

    const captchaOnChange = async (token: any) => {
        setToken(token);
        setTokenGenerated(true);
    };

    const asyncScriptOnLoad = () => {
        console.log('Google reCaptcha v2 loaded');
    };

    useEffect(() => {
        const locale = router?.locale;
        if (locale) {
            setAppLocale(locale);
        }
    }, [router?.locale]);

    return (
        <CaptchaContext.Provider value={value}>
            <ReCAPTCHA
                id="id1"
                theme="light"
                size="invisible"
                hl={appLocale}
                ref={reCaptchaRef}
                badge="bottomright"
                sitekey={captchSiteKey}
                onErrored={() => onError}
                onChange={captchaOnChange}
                asyncScriptOnLoad={asyncScriptOnLoad}
            />
            {children}
        </CaptchaContext.Provider>
    );
};

export default CaptchaContextProvider;
