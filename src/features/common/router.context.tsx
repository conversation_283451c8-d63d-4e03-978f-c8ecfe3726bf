import { StoresItem } from '@interfaces/common.inteface';
import { getCookie, setCookie } from '@utils/getAndsetCookie';
import { useRouter } from 'next/router';
import {
    createContext,
    useCallback,
    useContext,
    useEffect,
    useReducer,
} from 'react';

// #. Create the context actions and exported
enum ROUTER_CONTEXT_ACTION {
    ALL_REGION = 'all-region',
    ROUTER_PROPS = 'router-props',
}

// export default RouterContext;
type Action = { type?: ROUTER_CONTEXT_ACTION; payload?: any };
type Dispatch = (action: Action) => void;
type RouterProviderProps = {
    children: React.ReactNode;
};

type State = {
    region: string;
    regionUrl: string;
    locale: string;
    allRegions: StoresItem[];
    activeRegion: StoresItem;
    allBrandsPageDefaultUrl: string;
};

// #. Create the brand context
const AppRouterContext = createContext<
    | {
          state: State;
          dispatch: Dispatch;
          redirect: (region: string, locale: string | undefined) => void;
          router: any;
          utils: {
              setLocaleCookie: (locale: string) => void;
              getLocaleCookie: () => void;
              goToAllBrands: () => void;
          };
      }
    | undefined
>(undefined);

// #. Set the brand context name
AppRouterContext.displayName = 'AppRouterContext';

// #. Create a reducer method to update the state
function AppRouterReducer(state: State, action: Action) {
    switch (action.type) {
        case undefined:
        case ROUTER_CONTEXT_ACTION.ROUTER_PROPS: {
            let regionProps = {};
            const region = action?.payload?.region;

            /**
             * Auto update region url and active region object
             */
            if (region) {
                // #. Update region url
                regionProps = {
                    regionUrl: '/' + region,
                };
            }

            return { ...state, ...action.payload, ...regionProps };
        }

        case ROUTER_CONTEXT_ACTION.ALL_REGION: {
            return {
                ...state,
                allRegions: { ...state.allRegions, ...action.payload },
            };
        }

        default: {
            return state;
        }
    }
}

// #. Set the defaults
const DEFAULT_REGION = 'ae';
const DEFAULT_LOCALE: string = 'en';
const NEXT_LOCALE_COOKIE_KEY = 'NEXT_LOCALE';

// #. Initial value for the context
const initialValue = {
    region: "",
    regionUrl: '',
    locale: DEFAULT_LOCALE,
    allBrandsPageDefaultUrl: '',
};

// #. Create brand context provider
const AppRouterContextProvider = (props: RouterProviderProps) => {
    const cbReducer = useCallback(AppRouterReducer, []);

    //#. catalog title state
    const [state, dispatch] = useReducer(cbReducer, initialValue);

    // #. Get the router
    const router = useRouter();

    useEffect(() => {
        const region = router?.query?.region || state?.region;
        if (state.region !== region) {
            dispatch({
                type: ROUTER_CONTEXT_ACTION.ROUTER_PROPS,
                payload: { region },
            });
        }
    }, [router?.query]);

    useEffect(() => {
        const locale = router?.locale;
        if (locale !== state.locale) {
            dispatch({
                type: ROUTER_CONTEXT_ACTION.ROUTER_PROPS,
                payload: { locale },
            });
        }
    }, [router?.locale]);

    useEffect(() => {
        // #. Update the all regions array
        if (state.allRegions) {
            dispatchActiveRegion();
        }
    }, [state?.region]);

    useEffect(() => {
        setLocaleCookie(state?.locale);
    }, [state?.locale]);

    useEffect(() => {
        if (state?.allRegions && !state?.activeRegion) {
            dispatchActiveRegion();
        }
    }, [state?.allRegions]);

    /**
     * @method dispatchActiveRegion
     * @returns
     */
    const dispatchActiveRegion = () => {
        const activeRegion = findActiveRegion();

        dispatch({
            type: ROUTER_CONTEXT_ACTION.ROUTER_PROPS,
            payload: { activeRegion },
        });
    };

    /**
     * @method findActiveRegion
     * @returns
     */
    const findActiveRegion = () => {
        let activeRegion = (state?.allRegions).filter(
            ({ node: { country } }: any) => {
                return country?.code === state.region?.toUpperCase();
            }
        );

        activeRegion = activeRegion?.length > 0 ? activeRegion[0] : {};

        return activeRegion;
    };

    /**
     * @metho onRedirect
     * @description redirect with region and locale info
     * @param {region} - current region
     * @param {locale} - current locale
     */
    const onRedirect = (region: string, locale?: string | undefined) => {
        // #. replace the old region with latest
        let formattedPath = router?.asPath;
        if (formattedPath.indexOf(state.region) === -1) {
            formattedPath = `/${region}${formattedPath}`;
        } else {
            formattedPath = router?.asPath?.replace(
                `${state.region}`,
                `${region}`
            );
        }
        router?.push(formattedPath, undefined, { locale });
    };

    /**
     * @method setLocaleCookie
     * @param locale
     */
    const setLocaleCookie = (locale: string) => {
        setCookie(NEXT_LOCALE_COOKIE_KEY, locale, 1);
    };

    /**
     * @method getLocaleCookie
     * @returns
     */
    const getLocaleCookie = () => {
        return getCookie(NEXT_LOCALE_COOKIE_KEY);
    };

    /**
     * @method goToAllBrands
     * @description Redirect to default all brands page
     */
    const goToAllBrands = () => {
        const pathName =
            state?.allBrandsPageDefaultUrl || `/${router?.query?.region}`;

        // #. redirect to all-brands page
        router.push(pathName, undefined, {
            locale: router?.locale,
        });
    };

    const value = {
        state,
        dispatch,
        router,
        redirect: onRedirect,
        utils: {
            setLocaleCookie,
            getLocaleCookie,
            goToAllBrands,
        },
    };

    return (
        <AppRouterContext.Provider value={value}>
            {props.children}
        </AppRouterContext.Provider>
    );
};

// #. Create the context as a hook
function useAppRouter() {
    const context = useContext(AppRouterContext);

    if (context === undefined) {
        throw new Error('useAppRouter context not available');
    }

    return context;
}

//#. Export all the mandatory items
export {
    AppRouterContextProvider,
    AppRouterContext,
    ROUTER_CONTEXT_ACTION,
    DEFAULT_REGION,
    DEFAULT_LOCALE,
};

export default useAppRouter;
