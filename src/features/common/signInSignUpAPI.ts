import { AUTH_FLOW, COGNITO_CONFIG, COGNITO_USER_TYPE, EMAIL_EXISTS } from '@constants/common';
import { UserInfo } from '@features/login/loginFlowSlice';
import fetchAPI from '@utils/fetcher';
import { parsePreSignUpErrorMessage } from '@utils/signInSignUpHelper';
import { Auth } from 'aws-amplify';
import getConfig from 'next/config';
import { v4 as uuidv4 } from 'uuid';
import { DEFAULT_LOCALE } from './router.context';
import * as Sentry from '@sentry/nextjs';


const {
    publicRuntimeConfig: {
        userTokenUrl,
        userTokenUrlForApp,
        sendEmailOTPUrl,
        verifyOTPUrl,
        verifyEmailUrl,
        getTokenUrl,
        profileDetailsAPI,
        verifyEmailOTPUrl,
        sendSMSUrl,
        userSignUpAPI,
        userLoginAPI,
        updatePrefillAPI,
        validateEmailAPI
    },
    serverRuntimeConfig : {
        ratify<PERSON><PERSON><PERSON><PERSON>
    }
} = getConfig();

/**
 * Method to sign to cognito with mobile
 * @param mobileNumber
 * @returns
 */
export const signInWithMobile = (mobileNumber: string) => {
    Auth.configure({
        authenticationFlowType: COGNITO_CONFIG.CUSTOM_AUTH_TYPE,
    });
    return Auth.signIn(mobileNumber);
};

/*
* Method to get current authenticated user
* @returns
*/
export const currentAuthenticatedUser = async () => {
   return Auth.currentAuthenticatedUser({ bypassCache: true });
};

/**
 * Method to sign to cognito with mobile
 * @param mobileNumber
 * @returns
 */
export const signInWithEmailOtp = (email: string) => {
    Auth.configure({
        authenticationFlowType: COGNITO_CONFIG.CUSTOM_AUTH_TYPE,
    });
    return Auth.signIn(email);
};

/**
 * Method to sign in using username
 * @param username
 * @returns
 */
export const signInWithUsername = async (username: string) => {
    Auth.configure({
        authenticationFlowType: COGNITO_CONFIG.CUSTOM_AUTH_TYPE,
    });
    let response = await Auth.signIn(username);
    return response;
};

/**
 * Method to send custom auth flow challenge answer
 * @param user
 * @param otp
 * @returns
 */
export const sendCustomChallengeAnswer = (user: any, otp: string) => {
    return Auth.sendCustomChallengeAnswer(user, otp);
};

/**
 * Method to sign out signed in user
 */
export const signOut = async () => {
    await Auth.signOut();
    await deleteRefreshToken();
};

/**
 * Method to initiate social sign in
 * @param provider
 * @returns
 */
export const federatedSignIn = async (provider: any, config?: any) => {
    if (config) {
        Auth.configure(config);
    }
    return Auth.federatedSignIn(provider);
};

export const sendSMS = async (mobileNumber: string, token: string) => {
    try {
        let response = await fetchAPI(sendSMSUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                channel: 'sms',
                phone_number: mobileNumber,
                token: token,
            }),
        });
        return response?.data || {};
    } catch (error: any) {
        console.log('error while sending SMS ', error?.statusCode);
        return undefined;
    }
};

export const sendEmailOTP = async (email: string) => {
    try {
        let response = await fetchAPI(sendEmailOTPUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                channel: 'email',
                email_address: email,
            }),
        });
        return response?.data || {};
    } catch (error) {
        console.log('error while sending SMS ', error);
        return undefined;
    }
};

export const getUserProfileDetails = async (accessToken: any) => {
    try {
        let response = await fetchAPI(profileDetailsAPI, {
            method: 'GET',
            headers: {
                'ACCESS-TOKEN': accessToken,
            },
        });
        return response?.data || {};
    } catch (error) {
        // console.warn(t("error"), SEVERITY.ERROR, t("error"));
        console.log(error, 'getProfileDetails');
    }
};

export const getAuthInfo = async (refreshToken: string) => {
    try {
        const tokens = await getTokenInfo(refreshToken);
        const idToken = tokens?.idToken ?? '';
        const accessToken = tokens?.accessToken ?? '';
        return {
            idToken,
            accessToken,
        };
    } catch (error) {
        console.log('error is ', error);
        return {
            idToken: '',
            accessToken: '',
        };
    }
};

export const getTokenInfo = async (token: any) => {
    try {
        const myHeaders = new Headers({
            'Content-Type': 'application/json',
            Cookie: `REFRESH_TOKEN=${token}`,
        });

        let tokenInfo: any = await fetchAPI(getTokenUrl, {
            method: 'GET',
            credentials: 'include',
            headers: myHeaders,
        });

        if (tokenInfo?.status == 200) {
            return {
                accessToken: tokenInfo?.data?.AccessToken,
                idToken: tokenInfo?.data?.IdToken,
            };
        }
        return {
            accessToken: '',
            idToken: '',
        };
    } catch (error) {
        console.log('Error fetching token', error);
        return {
            accessToken: '',
            idToken: '',
        };
    }
};

export const refreshTokenValid = async (token: string) => {
    try {
        const myHeaders = new Headers({
            'Content-Type': 'application/json',
            Cookie: `REFRESH_TOKEN=${token}`,
        });

        let tokenInfo: any = await fetchAPI(getTokenUrl, {
            method: 'GET',
            credentials: 'include',
            headers: myHeaders,
        });

        if (tokenInfo?.status == 200) {
            return true
        }
        return false;
    } catch (error) {
        console.log('Error fetching token', error);
        return false;
    }
}

export const emailVerification = async (email: string, accessToken: string) => {
    try {
        let response = await fetchAPI(verifyEmailUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'access-token': accessToken,
            },
            body: JSON.stringify({
                channel: 'email',
                email_address: email,
            }),
        });

        return response?.data || {};
    } catch (error) {
        console.log(error);
        return error;
    }
};

export const emailVerificationOtp = async (
    payload: any,
    accessToken: string
) => {
    try {
        let response = await fetchAPI(verifyEmailOTPUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'access-token': accessToken,
            },
            body: JSON.stringify(payload),
        });

        return response?.data || {};
    } catch (error) {
        console.log(error);
        return error;
    }
};

export const verifyOtp = async ({ reference_id, otp }: any) => {
    try {
        let response = await fetchAPI(verifyOTPUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reference_id: reference_id,
                otp: otp,
            }),
        });
        return response?.data ?? response;
    } catch (error) {
        console.log('error while verify OTP ', error);
        return error;
    }
};

export const verifySMS = async ({ reference_id, otp }: any) => {
    try {
        let response = await fetchAPI(verifyOTPUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                reference_id: reference_id,
                otp: otp,
            }),
        });
        return response?.data ?? response;
    } catch (error) {
        console.log('error while verify OTP ', error);
        return error;
    }
};

export const updateMobileNumber = async (
    email: string,
    password: string,
    phone_number: string
) => {
    try {
        let signUpResponse = await Auth.signUp({
            username: 'username',
            password: 'password',
            attributes: {
                email: email,
                phone_number: phone_number,
            },
            clientMetadata: {
                purpose: 'updateMobileNumber',
                phone_number: phone_number,
            },
        });

        return {};
    } catch (error: any) {
        try {
            let parsedErrorMessage = parsePreSignUpErrorMessage(error.message);
            if (parsedErrorMessage && parsedErrorMessage.status == 200) {
                return {
                    status: 200,
                    message: 'Mobile number updated',
                    username: parsedErrorMessage.username,
                };
            }
            return {
                status: 500,
                message: 'Failed to update phone number',
            };
        } catch (err) {
            /* Returning true on exception to prevent User creation */
            return {
                status: 500,
                message: 'Failed to update phone number',
            };
        }
    }
};

export const setRefreshToken = async () => {
    try {
        const sessions = await Auth.currentSession();
        const refreshToken = sessions.getRefreshToken();
        let token = refreshToken.getToken();
        let res = await fetch('/api/setToken', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: token,
            }),
        });
    } catch (error) {
        console.log('Error setting the set token response ', error);
    }
};

export const parseAuthSession = (str: string) => {
    try {
        if(str && str.length && str.length > 56){
            return str.slice(0,56);
        }
        return "";
    } catch (error) {
        Sentry.captureMessage("Error in parseAuthSession ");
        console.log('Error in parseAuthSession ', error);
    }
};

export const parseAuthSessionTimeStamp = (str: any) => {
    try {
        const strTimestamp = str.slice(56);
        const decodeded : any = window.atob(strTimestamp);
        return Math.floor(decodeded * 1000);
    } catch (error) {
        return 0;
    }
};

export const setRefreshTokenViaAPI = async (refreshToken: any) => {
    try {
        const platform = localStorage.getItem('platform');
        
        const authSession = localStorage.getItem('authSession') || '';
        // const authSession:any = parseAuthSession(authSessionString);

        if(platform == 'app'){
            console.log(authSession);
        }

        const myHeaders = new Headers({
            'Content-Type': 'application/json',
            'REFRESH-TOKEN': refreshToken,
            ...(platform == 'app' && authSession && authSession.length && {'AUTH-SESSION': authSession})
        });
        let response: any = await fetchAPI(userTokenUrl, {
            method: 'POST',
            headers: myHeaders,
            credentials: 'include',
        });
        return response;
    } catch (error) {
        Sentry.captureMessage("Error in setRefreshTokenViaAPI ");
        Sentry.captureException(error);
        console.log('Error in setRefreshTokenViaAPI ', error);
    }
};

export const authSessionValid = async () => {
    try{
        const authSessionString = localStorage.getItem('authSession') || '';
        const sessionTimestamp = parseAuthSessionTimeStamp(authSessionString);
        let res = await fetchAPI('/api/date', {
            method: 'GET',
        });
        const currentTimestamp = res?.data?.currentDateTimeStamp;
        if(sessionTimestamp > currentTimestamp) return true;
        return false;
    } catch(error){
        return false;
    }
}

const deleteRefreshToken = async () => {
    try {
        let res = await fetchAPI('/api/deleteToken', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
        });
    } catch (error) {
        console.log('Error deleting token cookie ', error);
    }
};

export const linkSocialAccount = async (reqParams : any, language = DEFAULT_LOCALE, linking = false) => {
    try{
        let response = await fetchAPI(userSignUpAPI, {
            method: 'POST',
            body: JSON.stringify(reqParams),
            credentials: 'include',
            headers: {
                'accept-language': language,
                'x-api-key' : ratifyAPIKey,
                'Content-Type': 'application/json',
            }
        });
        if(response?.data) return response.data;
        if(response?.offline) return response;
        return {};
    } catch(error){
        console.log('Error occured during linkSocialAccount API call ', error);
        return {};
    }
}


export const sendOTP = async (params : UserInfo, language = DEFAULT_LOCALE) => {
    try{
        let reqParams:any = params;
        let form_data = new FormData();

        for ( let key in reqParams ) {
            if(typeof reqParams[key] === 'object'){
                form_data.append(key, JSON.stringify(reqParams[key]));
            } else {
                form_data.append(key, reqParams[key]);
            }
        }
        
        let response = await fetchAPI(userSignUpAPI, {
            method: 'POST',
            body: form_data,
            credentials: 'include',
            headers: {
                'accept-language': language
            }
        });
        if(response?.data) return response.data;
        if(response?.offline) return response;
        return {};
    } catch(error){
        console.log('Error occured during sign up API call ', error);
        return {};
    }
}

export const verifyOTP = async (params : UserInfo, otp : string, authFlow: String = AUTH_FLOW.NORMAL, language = DEFAULT_LOCALE) => {
    try{
        const platform = localStorage.getItem('platform');
        
        const authSession = localStorage.getItem('authSession') || '';
        let response = await fetchAPI(userSignUpAPI, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'accept-language': language,
                ...(platform == 'app' && authSession && authSession.length && {'AUTH-SESSION': authSession})

            },
            body: JSON.stringify({SessionId : params.SessionId, OTP: otp, AuthFlow : authFlow}),
            credentials: 'include'
        });
        if(response?.data) return response.data;
        if(response?.offline) return response;
        return {};
    } catch(error){
        console.log('Error occured during sign up API call ', error);
        return {};
    }
}

export const generateLoginOTP = async (params: UserInfo, type : string, captcha : string, language = DEFAULT_LOCALE, isWhatsAppOtp?: boolean, isWhatsAppOTP: boolean = false) => {
    try{
        let requestParams : any = {};
        requestParams['captcha_reference'] = captcha;
        if(type == 'phone_number'){
            requestParams['phone_number'] = params.phone_number;
        } else if(type == 'email'){
            requestParams['email'] = params.email?.toLowerCase();
        }
        requestParams['whatsappEnabled'] = isWhatsAppOtp || isWhatsAppOTP;
        let response = await fetchAPI(userLoginAPI, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'accept-language': language
            },
            body: JSON.stringify(requestParams)
        });
        if(response?.data) return response.data;
        if(response?.offline) return response;
        return {};
    } catch(error){
        console.log('Error occured during login API call ', error);
        return {};
    }
}

export const verifyLoginOTP = async (sessionId : any, otp : string, language = DEFAULT_LOCALE, authFlow: String = AUTH_FLOW.NORMAL) => {
    try{
        let response = await fetchAPI(userLoginAPI, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'accept-language': language
            },
            body: JSON.stringify({SessionId : sessionId, OTP: otp, AuthFlow : authFlow}),
            credentials: 'include'
        });
        if(response?.data) return response.data;
        if(response?.offline) return response;
        return {};
    } catch(error){
        console.log('Error occured during sign up API call ', error);
        return {};
    }
}

export const updatePrefillStatus = async (auth_code : any) => {
    try{
        let response = await fetchAPI(updatePrefillAPI, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({auth_code : auth_code})
        });
        if(response?.data) return response.data;
        if(response?.offline) return response;
        return {};
    } catch(error){
        console.log('Error occured during sign up API call ', error);
        return {};
    }
}

export const userWithEmailExists = (email: string = "") => {
    try{
        return fetchAPI(validateEmailAPI, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-api-key' : ratifyAPIKey
            },
            body: JSON.stringify({email : email.toLowerCase()})
        });
    } catch(error){
        Sentry.captureMessage("Error occured in API call method userWithEmailExists");
        Sentry.captureException(error);
    }
}