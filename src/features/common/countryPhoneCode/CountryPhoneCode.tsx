import React, { useEffect, useState, useRef } from 'react';
import styles from './CountryPhoneCode.module.scss';
import {
    getCountries,
    getCountryCallingCode,
} from 'react-phone-number-input/input';
import {
    InputAdornment,
    ListSubheader,
    MenuItem,
    Select,
    TextField,
} from '@mui/material';
import getUnicodeFlagIcon from 'country-flag-icons/unicode';
import en from 'react-phone-number-input/locale/en.json';
import ar from 'react-phone-number-input/locale/ar.json';
import { CountryCode } from 'libphonenumber-js/types';
import SearchIcon from '@mui/icons-material/Search';
import isAlphabet from '@utils/isAlphabet';
import { useTranslation } from 'next-i18next';
import getConfig from 'next/config';
import useAppRouter from '../router.context';
import { SIGN_IN_SLUGS } from '@constants/common';
import { CircleFlag } from 'react-circle-flags';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const CountryPhoneCode = ({
    showCountryName = false,
    defaultValue,
    setCountryCallback,
    showCountryNameInSelection = true,
    dialCode,
    enableSearch = false,
    language = 'en',
    phoneNumberRef,
    ipCountry,
    blacklistedCountries,
}: {
    showCountryName: boolean;
    defaultValue: CountryCode;
    setCountryCallback: any;
    showCountryNameInSelection: boolean;
    dialCode?: string;
    enableSearch?: boolean;
    language?: string;
    phoneNumberRef: any;
    ipCountry?: CountryCode;
    blacklistedCountries?: any;
}) => {
    const { t } = useTranslation('common');
    const blackListedCountries = blacklistedCountries;
    const priorityCountryList = [
        'AE',
        'SA',
        'QA',
        'OM',
        'BH',
        'KW',
        'IN',
        'GB',
    ].filter((country: any) => !blackListedCountries.includes(country));

    const countriesList = getCountries().filter(
        (country: any) => !blackListedCountries.includes(country)
    );

    const { router } = useAppRouter();
    const [countryCode, setCountryCode] = useState<CountryCode>(
        getDefaultCountryCode(
            blackListedCountries,
            defaultValue,
            countriesList,
            ipCountry
        )
    );
    const [isWindows, setIsWindows] = useState<boolean>(false);
    const [searchText, setSearchText] = useState('');
    const [countryOptions, setCountryOptions] = useState<any>([]);

    const defaultLan = language == 'ar' ? ar : en;
    const searchRef = useRef<HTMLInputElement>(null);

    const sortCountryPriority = (countries: string[]) => [
        ...priorityCountryList,
        ...countries.filter(
            (country) => !priorityCountryList.includes(country)
        ),
    ];
    const handleSelectOpen = () => {
        setTimeout(() => {
            searchRef.current?.focus();
        }, 100);
    };
    const onChangeHandler = async (event: any) => {
        if (phoneNumberRef.current) phoneNumberRef.current.value = '';
        const countryCode = event?.target?.value;
        const dialCode = `+${getCountryCode(countryCode)}`;

        if (!countryCode) return;
        setCountryCode(countryCode);
        setCountryCallback({ countryCode, dialCode });
        setCountryOptions(sortCountryPriority(countriesList));
    };

    const containsText = (text: string, searchText: string) =>
        text.toLowerCase().indexOf(searchText.toLowerCase()) > -1;

    useEffect(() => {
        // #. Set country code based on the dial-code
        if (dialCode) {
            for (let i = 0; i < countriesList.length; i++) {
                const countryDialCode = `+${getCountryCode(countriesList[i])}`;
                if (dialCode === countryDialCode) {
                    setCountryCode(countriesList[i]);
                    break;
                }
            }
        }
    }, [dialCode]);

    const getCountryCode = (country: any) => {
        const isValidCountry = getCountries().includes(country?.toUpperCase());
        return isValidCountry ? getCountryCallingCode(country) : '0';
    };

    useEffect(() => {
        const sortedCountries = sortCountryPriority(Object.keys(en));
        let filteredCountries = sortedCountries.filter((key: any) => {
            if (
                ![
                    'ext',
                    'country',
                    'phone',
                    'ZZ',
                    'AB',
                    ...blackListedCountries,
                ].includes(key)
            ) {
                let text = en[key as keyof typeof en];
                if (containsText(text, searchText)) {
                    return defaultLan[key as keyof typeof defaultLan];
                }
            }
        });
        setCountryOptions(filteredCountries);
        setTimeout(() => {
            searchRef.current?.focus();
        }, 100);
    }, [searchText]);

    useEffect(() => {
        if (
            navigator.platform &&
            navigator.platform.length &&
            navigator.platform.toLowerCase().indexOf('win') > -1
        ) {
            setIsWindows(true);
        }
    }, []);

    return (
        <div className={`country-phone-code ${styles['country-phone-code']}`}>
            <Select
                value={countryCode}
                renderValue={(id) => {
                    return (
                        <div>
                            <span
                                className={
                                    isWindows
                                        ? styles['country-phone-code-flag']
                                        : ''
                                }
                            >
                                <CircleFlag
                                    countryCode={id?.toLowerCase()}
                                    height="24"
                                />
                                +{getCountryCode(id)}
                            </span>
                        </div>
                    );
                }}
                MenuProps={{
                    classes: {
                        paper: `${
                            router?.query?.slug?.includes(
                                SIGN_IN_SLUGS.ADD_MOBILE
                            )
                                ? 'paper-signin-dropdown--add-mobile paper-signin-dropdowns country-phone-code'
                                : 'paper-signin-dropdowns country-phone-code'
                        }`,
                    },
                }}
                className={`${styles['country-select']}`}
                onChange={onChangeHandler}
                onBlur={handleSelectOpen}
                onOpen={handleSelectOpen}
                onClick={() => {
                    if (searchText && searchText.length == 0) {
                        setCountryOptions(sortCountryPriority(countriesList));
                    }
                }}
                IconComponent={ArrowDropDownIcon}
            >
                {enableSearch && (
                    <ListSubheader
                        className={styles['country-phone-code__search-box']}
                    >
                        <TextField
                            size="small"
                            // Autofocus on textfield
                            autoFocus
                            inputRef={searchRef}
                            fullWidth
                            InputProps={{
                                startAdornment: (
                                    <img
                                        className={
                                            styles[
                                                'country-phone-code__search-icon'
                                            ]
                                        }
                                        src={`${imageBaseUrl}/icons/search-icon.svg`}
                                    />
                                ),
                            }}
                            className={
                                styles['country-phone-code__search-input']
                            }
                            onChange={(e: any) => setSearchText(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key !== 'Escape') {
                                    // Prevents autoselecting item while typing (default Select behaviour)
                                    e.stopPropagation();
                                }
                            }}
                            onKeyPress={(e: any) => {
                                if (!isAlphabet(e)) {
                                    e.preventDefault();
                                }
                            }}
                        />
                    </ListSubheader>
                )}

                {countryOptions.map((country: any, index: any) => (
                    <MenuItem
                        key={index}
                        value={country}
                        className={`${
                            showCountryName === true
                                ? styles['country-phone-code__menu-item']
                                : styles[
                                      'country-phone-code__menu-item-send_to'
                                  ]
                        } ${
                            isWindows ? styles['country-phone-code__flag'] : ''
                        } `}
                    >
                        {
                            <div className={styles['country-phone-code__flag']}>
                                <CircleFlag
                                    countryCode={country?.toLowerCase()}
                                    width="24"
                                />
                            </div>
                        }
                        {showCountryName && (
                            <span>
                                {defaultLan[country as keyof typeof defaultLan]}
                            </span>
                        )}
                        {
                            <div className={styles['country-phone-code__code']}>
                                {`+${getCountryCode(country)}`}
                            </div>
                        }
                    </MenuItem>
                ))}
            </Select>
        </div>
    );
};

const getDefaultCountryCode = (
    blackListedCountries: string[],
    defaultValue: CountryCode,
    countriesList: CountryCode[],
    ipCountry?: CountryCode
) => {
    if (ipCountry && !blackListedCountries.includes(ipCountry)) {
        return ipCountry;
    } else if (defaultValue && !blackListedCountries.includes(defaultValue)) {
        return defaultValue;
    } else {
        return countriesList[0];
    }
};

CountryPhoneCode.defaultProps = {
    showCountryName: true,
    defaultValue: 'AE',
    showCountryNameInSelection: true,
    enableSearch: false,
    language: 'en',
};

export { CountryPhoneCode, getDefaultCountryCode };
