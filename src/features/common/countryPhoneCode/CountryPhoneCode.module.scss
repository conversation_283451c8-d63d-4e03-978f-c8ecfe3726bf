@import '@styles/variables';
@import '@styles/mixins';

.country-phone-code {
    display: flex;

    .country-phone-code-flag{
        font-family: 'Twemoji Country Flags' !important;
    }

    &__flag{
        width: 24px;
        height: 24px;
  
        > span {
          @include font-size(14);
        }
        img {
        width: 24px;
        height: 24px;
        }
    }

  &__code{
    text-align: right;
    flex: 1;

    @include rtl-styles{
        text-align: left;
    }
  }

    &__menu-item {
        padding: 12px 16px !important;
        gap: 10px;
        height: 48px !important;
        width: 100%;
        font-size: 14px !important;
        font-family: $mona-sans-font-family !important;
        font-weight: 600 !important;

        @include rtl-styles{
            font-family: $arabic-font-family;
        }
        &:hover {
            background: $grey-bg;
        }


        > span {
            color: $dark-purple !important;
            white-space: break-spaces;
        }
    }

    &__menu-item-send_to {
        padding: 7px;
        display: grid;
        grid-template-columns: 70px auto;
        gap: 10px;
        min-height: 23px;
        font-size: 14px;

        &:hover {
            background-color: $light-purple;
            border-radius: $border-radius-min;
        }

        > div {
            > img {
                width: 30px;
                height: 30px;
                object-fit: contain;
                border-radius: 3px;
            }

            > span {
                @include font-size(20);
            }
        }
    }

    &__search-box {
        padding: 16px 16px 8px 16px;
    }

    &__search-input {
        div {
            display: flex;
            width: 228px;
            padding: 13px 16px;
            align-items: center;
            gap: 8px;
        }

        input {
            padding: 0;
            border-radius: 8px;
            @include font-size(14);

            &::placeholder {
                @include font-size(14);
                

                width: 94px;
                height: 20px;
                margin: 0 0 0 14px;
                font-family: $mona-sans-font-family !important;
                opacity: 1 !important;
                font-weight: 500;
                font-stretch: normal;
                font-style: normal;
                line-height: 18px;
                letter-spacing: normal;
                color: #ccc;

                @include rtl-styles{
                    font-family: $arabic-font-family;
                    text-align: right;
                    padding-right: 5px;
                }
            }
        }
    }

    &__search-icon {
        width: 19px;
        height: 18px;
    }
}