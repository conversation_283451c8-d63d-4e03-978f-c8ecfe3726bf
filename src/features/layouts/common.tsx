import Head from 'next/head';
import { ReactNode, useEffect } from 'react';
import Link from 'next/link';
import getConfig from 'next/config';

interface PropsInterface {
    children: ReactNode;
}

const CommonLayout = ({ children }: PropsInterface) => {
    // taking public image config url
    const {
        publicRuntimeConfig: { imageBaseUrl },
    } = getConfig();

    const date = new Date();
    let year = date.getFullYear();

    return (
        <main className="indipendent-layout  ">
            <Head>
                <title>
                    Online Gifts in Dubai, UAE | Gift Cards & Gift Vouchers |
                    Send Gifts Dubai - Birthday Gifts Ideas
                </title>
            </Head>
            <header className="indipendent-layout__header">
                <Link href="/login" legacyBehavior>
                    <a className="indipendent-layout__header-logo">
                        <img
                            src={`${imageBaseUrl}/images/logo.jpg`}
                            height={40}
                            alt="YouGotaGift"
                        />
                    </a>
                </Link>
            </header>
            <section className="indipendent-layout__children ">
                {' '}
                {children}
            </section>
            <footer className="indipendent-layout__footer">
                © {year} YouGotaGift.com Ltd. All rights reserved.
            </footer>
        </main>
    );
};
export default CommonLayout;
