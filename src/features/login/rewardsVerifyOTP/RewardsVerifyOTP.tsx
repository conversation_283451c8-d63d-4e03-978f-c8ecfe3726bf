import React, { useState } from 'react';
import styles from './RewardsVerifyOTP.module.scss';
import { useTranslation } from 'next-i18next';
import { useAppSelector } from '@redux/hooks';
import {
    FieldToVerify,
    getCountryCode,
    getNumberMobile,
    getPageFlow,
    getUserInfo,
} from '../loginFlowSlice';
import { PAGE_FLOW } from '@constants/common';
import getConfig from 'next/config';
import Link from 'next/link';
import Image from 'next/image';
import { parsePhoneNumber } from 'libphonenumber-js/min';
import RewardsVerifyOtpResponse from './rewardsVerifyOtpResponse/RewardsVerifyOtpResponse';
import IntervalTimer from '../verifyOTP/timer/timer';

const RewardsVerifyOTP = () => {
    // otp sent status
    const [otpSentStatus, setOtpSentStatus] = useState<'sent' | 'resent'>(
        'sent'
    );
    const [timerStarted, setTimerStarted] = useState(true);
    const triggerTimerStop = () => {
        setTimerStarted(false);
    };

    const triggerTimerStart = () => {
        setTimerStarted(true);
    };

    // Translation
    const { t } = useTranslation('common');
    const {
        publicRuntimeConfig: { defaultRedirectUrl, imageBaseUrl },
    } = getConfig();

    const userInfo = useAppSelector(getUserInfo);
    const pageFlow = useAppSelector(getPageFlow);
    let countryCode: any = useAppSelector(getCountryCode);
    let numberMobile: any = useAppSelector(getNumberMobile);
    const isEmailVerificationFlow = userInfo?.toVerify == FieldToVerify?.Email;
    const hasPhoneNumber = userInfo?.phone_number;
    const isSignUp = [
        PAGE_FLOW.EMAIL_SIGN_UP,
        PAGE_FLOW.MOBILE_SIGN_UP,
        PAGE_FLOW.SOCIAL_SIGN_UP_WITHOUT_EMAIL,
        PAGE_FLOW.SOCIAL_SIGN_UP_WITH_EMAIL,
    ].includes(pageFlow);

    if (!numberMobile || !numberMobile.length) {
        if (userInfo.phone_number) {
            let pNumber = parsePhoneNumber(userInfo.phone_number);
            countryCode = pNumber?.countryCallingCode;
            numberMobile = pNumber?.nationalNumber;
        }
    }

    let newPhone;
    if (numberMobile && numberMobile.length > 3) {
        let firstPart = numberMobile?.slice(0, 1);
        let lastPart = numberMobile?.slice(-2);

        newPhone = `+${countryCode} ${firstPart}${'x'.repeat(
            numberMobile.length - 3
        )}${lastPart}`;
    }

    return (
        <div className={styles['signin-verification-code']}>
            <div className={styles['signin-verification-code__title']}>
                <div className={styles['signin-verification-code__title-left']}>
                    <h3>Verify to sign in</h3>
                </div>
                <Link href={defaultRedirectUrl}>
                    <Image
                        src={`${imageBaseUrl}/icons/rewards-logo.svg`}
                        alt="ygg-logo"
                        height={40}
                        width={67.785}
                    />
                </Link>
            </div>
            <p className={styles['subtitle']}>
                Enter verification code sent to your email
            </p>

            <RewardsVerifyOtpResponse
                platformType={'WEB'}
                isEmailVerificationFlow={isEmailVerificationFlow}
                timerStarted={timerStarted}
                triggerTimerStart={triggerTimerStart}
                hasPhoneNumber={hasPhoneNumber}
            >
                <IntervalTimer
                    triggerTimerStop={triggerTimerStop}
                    timerStarted={timerStarted}
                />
            </RewardsVerifyOtpResponse>
        </div>
    );
};

export default RewardsVerifyOTP;
