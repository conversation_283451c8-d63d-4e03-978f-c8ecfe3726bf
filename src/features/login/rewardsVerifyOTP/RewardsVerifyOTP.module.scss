@import '@styles/variables';
@import '@styles/mixins';

.signin-verification-code {
    &__info {
        margin: 40px 0 24px;
        padding: 0;
        @include font-size(16);
        color: #868785 !important;
        font-weight: 600;
        font-family: $mona-sans-font-family;

        @include rtl-styles {
            font-family: $arabic-font-family;
        }

        @media only screen and (max-width: $sm) {
            @include font-size(14);
            line-height: 20px;
            padding-bottom: 16px;
            margin: 32px 0 16px;
        }

        &-emailMobile {
            color: $dark-charcoal;
            font-weight: 600;
            font-family: $mona-sans-font-family;

            @include rtl-styles {
                font-family: $arabic-font-family;
            }
        }
    }

    &__title {
        display: flex;
        align-items: center;
        gap: 48px;
        justify-content: space-between;
        flex-direction: row-reverse;

        &-left {
            h4 {
                color: $dark-charcoal;
                font-size: 24px;
                font-style: normal;
                font-weight: 800;
                line-height: 32px;
                margin: 0 0 8px;
            }
        }

        h3 {
            margin: 0;
            font-size: 24px;
            font-optical-sizing: none;
            font-style: normal;
            font-weight: 800;
            line-height: 32px;
            background: linear-gradient(90deg, #0071ff 0%, #99c6ff 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        @media only screen and (max-width: $sm) {
            justify-content: center;
            margin-bottom: 32px;
            gap: 0;

            h3,
            h4 {
                display: none;
            }

            img {
                height: 48px;
            }
        }
    }

    .subtitle {
        font-family: $mona-sans-font-family;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: var(--Font-Line-height-lg, 24px); /* 150% */
        letter-spacing: -0.16px;
        margin: 24px 0;

        @include rtl-styles {
            font-family: $arabic-font-family;
        }
    }

    &__mweb-title {
        h4 {
            color: $dark-charcoal;
            font-family: $default-font-family;
            font-optical-sizing: none;
            font-size: 18px;
            font-weight: 800;
            line-height: 24px;
            letter-spacing: -0.09px;
            margin: 0 0 8px;

            @include rtl-styles {
                font-family: $arabic-font-family;
            }
        }

        h3 {
            color: $dark-charcoal;
            font-family: $default-font-family;
            font-optical-sizing: none;
            font-size: 24px;
            font-weight: 800;
            line-height: 24px;

            @include rtl-styles {
                font-family: $arabic-font-family;
            }
        }
    }
}
