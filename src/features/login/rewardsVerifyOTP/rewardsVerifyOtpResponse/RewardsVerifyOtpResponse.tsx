import Button from '@features/common/button/Button';
import { FormControl } from '@mui/material';
import { useTranslation } from 'next-i18next';
import React, { useContext, useEffect, useRef, useState } from 'react';
import {
    authSessionValid,
    sendCustomChallengeAnswer,
    signInWithMobile,
} from '@features/common/signInSignUpAPI';
import styles from './RewardsVerifyOtpResponse.module.scss';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import {
    FieldToVerify,
    getCognitoCustomResponse,
    getPageFlow,
    getRdir,
    getResendOtpReq,
    getUserInfo,
    setCognitoCustomResponse,
    setSignInResponse,
    setUserInfo,
    UserInfo,
} from '@features/login/loginFlowSlice';
import useAppRouter, { DEFAULT_REGION } from '@features/common/router.context';
import {
    PLATFORM_TYPE,
    SIGN_IN_SLUGS,
    PAGE_FLOW,
    AUTH_FLOW,
    API_STATUS,
    GRAPHQL_STATUS_CODE,
    FLOW_TYPE,
    CHANNEL_TYPE,
} from '@constants/common';
import cloneDeep from 'lodash/cloneDeep';
import getConfig from 'next/config';
import { SEVERITY } from '@constants/messageTypes';
import ResponseInfoHOC from '@features/signinSignup/common/responseInfoHOC/ResponseInfoHOC';
import {
    getIpAddress,
    getIsCaptchaRequired,
} from '@features/common/commonSlice';
import * as Sentry from '@sentry/nextjs';
import { loginGA, signUpGA } from 'lib/gtm';
import { decodeToken } from '@utils/decodeToken';
import { cleverTapService } from '@features/common/clevertap/clevertap.services';
import isYouGotaGiftDomain from '@utils/isYouGotaGiftDomain';
import {
    executeCaptcha,
    getCustomSchemaErrorUrl,
    getCustomSchemaUrl,
    otpGeneratedSuccessfully,
    otpIsInvalid,
    otpIsVerified,
    platformAppWithAuthSignature,
    platformIsMobileAppWithValidAuthSession,
    platformNotApp,
    userCreated,
} from '@utils/signInSignUpHelper';
import { useMutation } from '@apollo/client';
import { GUEST_LOGOUT } from '@features/common/common.query';
import Confirm from '@features/common/confirm/Confirm';
import { CaptchaContext } from '@features/common/captcha.context';
import useAuthAPI from '@features/login/authAPI';
import {
    AuthSetTokenResponse,
    CustomChallengeResponse,
    GenerateLoginOTPResponse,
    SignUpResponse,
    VerifyOTPResponse,
    VerifySignUpOTPResponse,
} from '@features/login/authAPI.interface';
import { GraphQLError } from '@interfaces/common.inteface';
import { CommunicationConfigs } from '@features/common/commonAPI';
import OTPInput from '@features/common/otpInputField/otpInputField';
import fetchAPI from '@utils/fetcher';
import HelpText from '@features/signinSignup/common/helpText/HelpText';
import Image from 'next/image';

const {
    publicRuntimeConfig: { defaultRedirectUrl, imageBaseUrl },
} = getConfig();

const PhoneOtpConfirmation = ({
    showMessage,
    platformType,
    isEmailVerificationFlow,
    children,
    timerStarted,
    triggerTimerStart,
    hasPhoneNumber,
}: {
    state: any;
    showMessage: (
        message: string,
        severity: SEVERITY,
        heading?: string | null,
        online?: boolean | true
    ) => void;
    platformType: any;
    isEmailVerificationFlow: boolean;
    children: any;
    timerStarted: boolean;
    triggerTimerStart: any;
    hasPhoneNumber: boolean;
}) => {
    // Translation
    const { t } = useTranslation('common');

    const captchaContext: any = useContext(CaptchaContext);

    const {
        state: { locale },
        router,
    } = useAppRouter();

    const dispatch = useAppDispatch();

    const userInfo = useAppSelector(getUserInfo);
    const rdir = useAppSelector(getRdir);
    const ip = useAppSelector(getIpAddress);

    const pageFlow = useAppSelector(getPageFlow);
    const cognitoCustomResponse = useAppSelector(getCognitoCustomResponse);
    const isCaptchaRequired = useAppSelector(getIsCaptchaRequired);

    const otpReqParams = useAppSelector(getResendOtpReq);
    const reCaptchaRef: any = useRef({});
    const verifyButtonRef: any = useRef(null);

    // button status
    const [buttonDisabled, setButtonDisabled] = useState<boolean>(true);
    const [appRedirectUrl, setAppRedirectUrl] = useState('');
    const [showRedirectPrompt, setShowRedirectPrompt] =
        useState<boolean>(false);

    // Otp value
    const [otpValue, setOtpValue] = useState('');
    const {
        verifyOTP,
        userSignUpOtpVerification,
        generateLoginOTP,
        userSignUp,
        setRefreshToken,
        getUserDetails,
    } = useAuthAPI();
    const [showSendAgain, setShowSendAgain] = useState(true);

    useEffect(() => {
        if (captchaContext.tokenGenerated && captchaContext.token) {
            onCaptchaChange();
            captchaContext.function.reset();
        }
    }, [captchaContext.tokenGenerated, captchaContext.token]);

    useEffect(() => {
        if (timerStarted) {
            setStartCounter(true);
        } else {
            setStartCounter(false);
        }
    }, [timerStarted]);

    // otp status
    const [otpStatus, setOtpStatus] = useState<
        'invalid' | 'valid' | 'none' | 'expired'
    >('none');

    // counter status
    const [startCounter, setStartCounter] = useState(true);

    const [processState, setProcessState] = useState<'inProcess' | 'complete'>(
        'complete'
    );
    const [isWhatsAppOTP, setIsWhatsAppOTP] = useState<boolean>(false);

    const [currentSession, setCurrentSession] = useState(userInfo.SessionId);

    const hasOTPError = otpStatus === 'invalid' || otpStatus === 'expired';

    const isMobile =
        platformType === PLATFORM_TYPE.MWEB.toLowerCase() ||
        platformType === PLATFORM_TYPE.APP.toLowerCase();

    // #. Fetch resendDeliveryEnabled from communicationConfigsData
    const store = localStorage
        .getItem('selectedStore')
        ?.toString()
        .toUpperCase();

    const { communicationConfigsData } = CommunicationConfigs(
        store || DEFAULT_REGION,
        FLOW_TYPE.SIGN_IN,
        CHANNEL_TYPE.WHATSAPP
    );

    const resendDeliveryEnabled =
        communicationConfigsData?.communicationConfigs[0]
            ?.resendDeliveryEnabled;

    useEffect(() => {
        if (otpValue.length === 6 && verifyButtonRef.current) {
            verifyButtonRef.current?.focus();
        }
    }, [otpValue]);

    useEffect(() => {
        if (timerStarted) {
            setStartCounter(true);
        } else {
            setStartCounter(false);
        }
    }, [timerStarted]);

    const onCaptchaChange = async () => {
        const token = captchaContext.token;
        if (token) {
            setProcessState('inProcess');
            if (token) {
                await sendAgain(token);
            } else {
            }
            setProcessState('complete');
        } else {
            showMessage(
                `No captcha token found`,
                SEVERITY.ERROR,
                t('authError')
            );
        }
    };

    const proceedWithoutCaptcha = async (isWhatsAppOtp: boolean = false) => {
        setOtpStatus('none');
        setProcessState('inProcess');
        await sendAgain(null, isWhatsAppOtp);
        setProcessState('complete');
    };

    // timer set
    let processStarted = false;
    /**
     * Button Status on input text change
     */
    const inputOnChange = (otpCode: any) => {
        setOtpValue(otpCode);
        clearOtpErrorMsg();
        const re = /^[0-9\b]+$/;

        // if value is not blank, then test the regex

        if (otpCode === '' || re.test(otpCode)) {
            setOtpValue(otpCode);
            if (otpCode.length == 6) {
                setButtonDisabled(false);
                // handleConfirmOtp();
            } else {
                setButtonDisabled(true);
            }
        } else {
            setOtpValue('');
        }
    };

    const clearOtpErrorMsg = () => {
        setOtpStatus('none');
    };

    const continueToNextStep = (userInfo: UserInfo, response: any) => {
        try {
            if (userInfo.emailVerified && userInfo.phoneVerified) {
                // ## both verified
                if (
                    platformType == PLATFORM_TYPE.APP.toLocaleLowerCase() &&
                    response &&
                    response['authSignature'] &&
                    response['authSignature'].length
                ) {
                    const url = getCustomSchemaUrl(response['authSignature']);
                    const deviceType = localStorage.getItem('deviceType') || '';
                    if (deviceType == 'android') {
                        setAppRedirectUrl(url);
                        setShowRedirectPrompt(true);
                    } else {
                        window.location.href = url;
                        return false;
                    }
                } else {
                    afterSignInRedirect();
                }
            } else if (!userInfo.emailVerified) {
                router.push(
                    `?slug=${SIGN_IN_SLUGS.ADD_INFO}`,
                    `${SIGN_IN_SLUGS.ADD_INFO}`,
                    { locale, shallow: true }
                );
            } else if (!userInfo.phoneVerified) {
                router.push(
                    `?slug=${SIGN_IN_SLUGS.ADD_MOBILE}`,
                    `${SIGN_IN_SLUGS.ADD_MOBILE}`,
                    { locale, shallow: true }
                );
            }
        } catch (error) {
            console.log('Error occured in continueToNextStep :: ', error);
        }
    };

    const afterSignInRedirect = () => {
        // if (platformType == PLATFORM_TYPE.APP.toLowerCase()) {
        //     // ## dont' redirect if the platform is APP (iOS or Android)
        //     return;
        // }
        let lrDir = localStorage.getItem('rdir');
        if (rdir && rdir.length && isYouGotaGiftDomain(rdir)) {
            router.push(rdir, undefined, { locale });
        } else if (lrDir && lrDir.length && isYouGotaGiftDomain(lrDir)) {
            router.push(lrDir, undefined, { locale });
        } else {
            router.push({ pathname: defaultRedirectUrl });
        }
    };

    const socialSignIn = () => {
        return [
            PAGE_FLOW.SOCIAL_SIGN_UP_WITHOUT_EMAIL,
            PAGE_FLOW.SOCIAL_SIGN_UP_WITH_EMAIL,
        ].includes(pageFlow);
    };

    const [handleGuestLogout] = useMutation(GUEST_LOGOUT, {
        context: {
            clientName: 'guestLogin',
            headers: {
                'app-platform':
                    platformType && platformType.length
                        ? platformType
                        : PLATFORM_TYPE.WEB.toLowerCase(),
                'access-locale': `ST${
                    localStorage.getItem('selectedStore')?.toUpperCase() || 'AE'
                }`,
                'yg-ip-address': ip,
            },
            credentials: 'include',
        },
        onError: (error: any) => {
            console.log('Error occured in Guest Logout ', error);
            Sentry.captureMessage('Error occured in handleGuestLogout ', error);
        },
        onCompleted: (response: any) => {
            console.log('resposns from logout is ', response);
            if (
                response?.guestLogout?.logout?.status ==
                GRAPHQL_STATUS_CODE.SERVER_ERROR
            ) {
                Sentry.captureMessage('Guest Logout Failed ');
                Sentry.captureException(response);
            }
        },
    });

    /**
     * Method to show Error Message
     * @param message
     */
    const showErrorMsg = (message: string) => {
        showMessage(message, SEVERITY.ERROR, t('authError'));
    };

    const handleAfterSignInRedirect = (AuthSignature: string) => {
        // ##. If mobile app with valid auth signature then get the custom schema url
        // ##. and redirect to the same after showing redirection prompt
        // ##. Else do an after sign in redirect as the normal flow
        if (platformAppWithAuthSignature(platformType, AuthSignature)) {
            try {
                const url = getCustomSchemaUrl(AuthSignature);
                const deviceType = localStorage.getItem('deviceType') || '';
                if (deviceType == 'android') {
                    setAppRedirectUrl(url);
                    setShowRedirectPrompt(true);
                } else {
                    window.location.href = url;
                    return false;
                }
            } catch (error) {
                Sentry.captureMessage('Error in app redirect ');
            }
            return false;
        } else {
            afterSignInRedirect();
            return;
        }
    };

    const onSetRefreshTokenSuccess = async (
        response: AuthSetTokenResponse,
        username: string
    ) => {
        try {
            const {
                authSetTokens: { data: { accessToken, authSignature } } = {
                    data: { accessToken: '', authSignature: '' },
                },
            }: AuthSetTokenResponse = response;
            const { name = '' }: any = await getUserDetails(
                locale,
                accessToken
            );

            // ##. If not Mobile app platform handle Guest logout. This is to handle migration
            // ##. from a guest user to Normal user
            if (platformNotApp(platformType)) {
                const { data: guestSessionExists = { data: true } } =
                    await fetchAPI('/api/gexists');
                if (guestSessionExists) {
                    await handleGuestLogout({
                        variables: {
                            user: accessToken,
                        },
                    });
                }
            }

            // ##. Clevertap Integration
            cleverTapService.pushClevertapProfile(username, name);
            cleverTapService.pushLoginSignup();

            // ##. Google Analytic for login
            loginGA();

            handleAfterSignInRedirect(authSignature);
        } catch (error) {
            console.log('Error occured in onSetRefreshTokenSuccess ', error);
        }
    };

    const onSetRefreshTokenError = (error: any) => {
        console.log('Error is ', error);
        Sentry.captureMessage('Error occured in onSetRefreshTokenError ');
    };

    /**
     * Method to handle Cognito Custom Auth Challenge Verification
     * @param authenticationResult
     * @returns
     */
    const customAuthVerification = async (authenticationResult: string) => {
        try {
            const {
                signInUserSession: { refreshToken: { token } } = {
                    refreshToken: { token: '' },
                },
                username = '',
            }: CustomChallengeResponse = await sendCustomChallengeAnswer(
                cognitoCustomResponse,
                authenticationResult
            );
            if (token) {
                // ##. If refresh token is available process the token to get access token
                // ##. and to get user profile details
                setRefreshToken(
                    username,
                    token,
                    locale,
                    onSetRefreshTokenSuccess,
                    onSetRefreshTokenError
                );
            } else {
                Sentry.captureException(
                    `No refresh token returned in response, platform type ${platformType}`
                );
                showSomethingWentWrongError();
            }
        } catch (error) {
            Sentry.captureMessage(`Error in customAuthVerification ${error}`);
            console.log('Error in customAuthVerification ', error);
            showSomethingWentWrongError();
        }
    };

    /**
     * Method to show Something went wrong error message
     */
    const showSomethingWentWrongError = () => {
        showMessage(t('somethingWrong'), SEVERITY.ERROR, t('authError'));
    };

    const onOtpVerifySuccess = async (response: VerifyOTPResponse) => {
        try {
            const {
                authLogin: {
                    data: { message, statusCode, authenticationResult },
                },
            } = response;

            // ##. TODO :: Check for offline
            if (response && response?.offline) {
                showMessage(
                    t('checkYouInternetConnection'),
                    SEVERITY.ERROR,
                    t('authError'),
                    false
                );
                processStarted = false;
                setProcessState('complete');
                return;
            } else if (otpIsInvalid(statusCode as API_STATUS)) {
                setOtpValue('');
                setOtpStatus('invalid');
                setProcessState('complete');
            } else if (otpIsVerified(statusCode as API_STATUS)) {
                customAuthVerification(authenticationResult);
            } else {
                showErrorMsg(message);
                setProcessState('complete');
            }
            processStarted = false;
        } catch (error) {
            Sentry.captureMessage(
                `Error occured in onOtpVerifySuccess, ${error}`
            );
            console.log('Error occured in onOtpVerifySuccess ', error);
        }
    };

    const onOtpVerifyError = ({
        graphQLErrors: [{ message }] = [{ message: t('somethingWrong') }],
    }: GraphQLError) => {
        Sentry.captureMessage(`Error occured in onOtpVerifyError, ${message}`);
        showErrorMsg(message);
    };

    const onUserSignUpOtpVerificationSuccess = async (
        response: VerifySignUpOTPResponse
    ) => {
        try {
            const {
                authSignup: {
                    data: {
                        sessionKey,
                        message,
                        statusCode,
                        authenticationResult,
                        extraAttributes,
                    },
                },
            } = response;
            const { idToken, accessToken } = authenticationResult || {
                idToken: '',
                accessToken: '',
            };

            const decodedToken = decodeToken(idToken);
            if (decodedToken) {
                // ## mobile otp verification process

                // ## Clevertap
                cleverTapService.pushClevertapProfile(
                    decodedToken?.email,
                    decodedToken?.name
                );
                cleverTapService.pushLoginSignup();

                // ## Google Analitic for Signup
                signUpGA(
                    decodedToken?.gender,
                    decodedToken?.['cognito:username'],
                    locale
                );
            }
            if (response && response?.offline) {
                showMessage(
                    t('checkYouInternetConnection'),
                    SEVERITY.ERROR,
                    t('authError'),
                    false
                );
                processStarted = false;
                setProcessState('complete');
                return;
            }
            if (userCreated(statusCode as API_STATUS)) {
                let info = cloneDeep(userInfo);
                info.emailVerified = true;
                info.phoneVerified = true;
                dispatch(setUserInfo(info));

                if (platformType != PLATFORM_TYPE.APP.toLocaleLowerCase()) {
                    const { data: guestSessionExists = { data: true } } =
                        await fetchAPI('/api/gexists');
                    if (guestSessionExists) {
                        await handleGuestLogout({
                            variables: {
                                user: accessToken,
                            },
                        });
                    }
                }

                continueToNextStep(info, extraAttributes);
                return;
            } else if (otpIsVerified(statusCode as API_STATUS)) {
                let info = cloneDeep(userInfo);
                if (info.toVerify == FieldToVerify.Email) {
                    info.emailVerified = true;
                } else if (info.toVerify == FieldToVerify.Phone) {
                    info.phoneVerified = true;
                }
                info.SessionId = sessionKey;
                dispatch(setUserInfo(info));
                continueToNextStep(info, extraAttributes);
                return;
            } else if (otpIsInvalid(statusCode as API_STATUS)) {
                setOtpStatus('invalid');
            } else {
                Sentry.captureException(response);
                showMessage(message, SEVERITY.ERROR, t('authError'));
            }
            setProcessState('complete');
            processStarted = false;
        } catch (error) {
            Sentry.captureMessage(
                `Error occured in onUserSignUpOtpVerificationSuccess ${error}`
            );
            console.log(
                'Error occured in onUserSignUpOtpVerificationSuccess ',
                error
            );
            showSomethingWentWrongError();
        }
    };

    const onUserSignUpOtpVerificationError = ({
        graphQLErrors: [{ message }] = [{ message: t('somethingWrong') }],
    }: GraphQLError) => {
        Sentry.captureMessage(
            `Error occured in onUserSignUpOtpVerificationError ${message}`
        );
        showErrorMsg(message);
    };

    const handleConfirmOtp = async () => {
        setButtonDisabled(true);
        if (processStarted) return;
        processStarted = true;
        if (!otpValue || otpValue.length != 6) return;
        setProcessState('inProcess');

        // ## Checking if auth session exists
        const authSession =
            router?.query?.auth_session ||
            localStorage.getItem('authSession') ||
            '';
        if (
            platformIsMobileAppWithValidAuthSession(platformType, authSession)
        ) {
            // ## check for valid auth session in case of APP platform

            const validAuthSession = await authSessionValid();

            if (!validAuthSession) {
                const url = getCustomSchemaErrorUrl('SESSION_EXPIRED');
                window.location.href = url;
                return;
            }
        }

        try {
            if (pageFlow == PAGE_FLOW.CUSTOM_SIGN_IN) {
                // ##. This is Login flow, verify OTP for Login
                console.log('current session ', currentSession);
                console.log('user info ', userInfo);
                verifyOTP(
                    currentSession,
                    otpValue,
                    locale,
                    onOtpVerifySuccess,
                    onOtpVerifyError
                );
            } else {
                // ##. This is Sign Up OTP verification flow
                processStarted = true;
                setProcessState('inProcess');
                let authFlow = AUTH_FLOW.NORMAL;
                if (socialSignIn()) {
                    authFlow = AUTH_FLOW.SOCIAL;
                }
                userSignUpOtpVerification(
                    userInfo.SessionId,
                    otpValue,
                    locale,
                    onUserSignUpOtpVerificationSuccess,
                    onUserSignUpOtpVerificationError,
                    authFlow,
                    platformType
                );
            }
        } catch (error) {
            showMessage(t('somethingWrong'), SEVERITY.ERROR, t('authError'));
            setButtonDisabled(false);
            Sentry.captureMessage(
                `Error occured handleConfirmOtp ${JSON.stringify(error)}`
            );
            console.log('Error occured in handleConfirmOtp ', error);
            setProcessState('complete');
            processStarted = false;
        }
    };

    /**
     * Method to get Error message as per the OTP status
     * @returns
     */
    const getErrorMessage = () => {
        if (otpStatus == 'invalid') {
            return t('invalidOTP');
        } else if (otpStatus == 'expired') {
            return t('expiredOTP');
        } else {
            return '';
        }
    };

    const handleSendAgainClick = (isWhatsAppOtp?: boolean) => {
        setIsWhatsAppOTP(isWhatsAppOtp || false);
        if (isCaptchaRequired) {
            executeCaptcha(captchaContext['elem']);
        } else {
            proceedWithoutCaptcha(isWhatsAppOtp);
        }
    };

    const showErrorMessage = (message: string, online: boolean = true) => {
        showMessage(message, SEVERITY.ERROR, t('authError'));
    };

    const onGenerateLoginOTPSuccess = async (
        response: GenerateLoginOTPResponse,
        token: string,
        cognitoCustomQuery: any,
        reqParams: any
    ) => {
        try {
            const {
                authLogin: {
                    data: {
                        message,
                        statusCode,
                        sessionKey,
                        mfaValues: { phoneNumber },
                    },
                    errors,
                },
                offline,
            } = response;
            if (offline) {
                showMessage(
                    t('checkYouInternetConnection'),
                    SEVERITY.ERROR,
                    t('authError'),
                    false
                );
                setProcessState('complete');
            } else if (statusCode) {
                switch (statusCode) {
                    case (API_STATUS.LIMIT_EXCEEDED, API_STATUS.CAPTCHA_FAILED):
                        showErrorMessage(message);
                        break;
                    case API_STATUS.OTP_GENERATED:
                        let cognitoCustomResponse = await cognitoCustomQuery;
                        dispatch(
                            setCognitoCustomResponse(cognitoCustomResponse)
                        );
                        const userInfo = cloneDeep(reqParams);
                        userInfo.SessionId = sessionKey;
                        setCurrentSession(sessionKey);
                        dispatch(setUserInfo(userInfo));
                        dispatch(setSignInResponse(response));
                        triggerTimerStart();
                        break;
                }
                setProcessState('complete');
            }
        } catch (error) {
            Sentry.captureMessage(
                `Error occured in onGenerateLoginOTPSuccess ${error}`
            );
            console.log('Error occured in onGenerateLoginOTPSuccess ', error);
        }
    };

    const onGenerateLoginOTPError = ({
        graphQLErrors: [{ message }] = [{ message: t('somethingWrong') }],
    }: GraphQLError) => {
        Sentry.captureMessage(
            `Error occured in onGenerateLoginOTPError ${message}`
        );
        showErrorMessage(message);
        setProcessState('complete');
        setShowSendAgain(true);
    };

    const onEmailSignUpSuccess = (
        response: SignUpResponse,
        userInfo: UserInfo
    ) => {
        try {
            setProcessState('complete');
            setShowSendAgain(true);

            const {
                authSignup: { data, errors = [] },
                offline,
            }: any = response;

            if (data == null) {
                if (errors && errors.length > 0) {
                    showErrorMessage(
                        errors[0]?.['message'] || t('somethingWrong')
                    );
                    return;
                }
            }

            const { message = '', statusCode = '', sessionKey = '' } = data;

            if (offline) {
                showMessage(
                    t('checkYouInternetConnection'),
                    SEVERITY.ERROR,
                    t('authError'),
                    false
                );
            } else if (otpGeneratedSuccessfully(statusCode as API_STATUS)) {
                let info = cloneDeep(userInfo);
                info.SessionId = sessionKey;
                dispatch(setUserInfo(info));
                triggerTimerStart();
            } else {
                showErrorMessage(message);
            }
        } catch (error: any) {
            Sentry.captureMessage(
                `Error occured in onEmailSignUpSuccess ${error}`
            );
            console.log('Error occured in onEmailSignUpSuccess ', error);
            showErrorMessage(error?.message || t('somethingWrong'));
        }
    };
    const sendAgain = async (token: any = null, isWhatsAppOtp?: boolean) => {
        try {
            setProcessState('inProcess');

            // ## Checking if auth session exists
            const authSession =
                router?.query?.auth_session ||
                localStorage.getItem('authSession') ||
                '';
            if (
                platformIsMobileAppWithValidAuthSession(
                    platformType,
                    authSession
                )
            ) {
                // ## check for valid auth session in case of APP platform

                const validAuthSession = await authSessionValid();

                if (!validAuthSession) {
                    const url = getCustomSchemaErrorUrl('SESSION_EXPIRED');
                    window.location.href = url;
                    return;
                }
            }

            switch (pageFlow) {
                case PAGE_FLOW.CUSTOM_SIGN_IN: {
                    let param: any =
                        otpReqParams.type == 'phone_number'
                            ? userInfo.phone_number
                            : userInfo.email;
                    let cognitoCustomQuery = signInWithMobile(param);
                    generateLoginOTP(
                        userInfo,
                        otpReqParams.type,
                        token || '',
                        locale,
                        cognitoCustomQuery,
                        onGenerateLoginOTPSuccess,
                        onGenerateLoginOTPError
                    );
                    break;
                }
                default: {
                    let reqParams = {
                        ...otpReqParams,
                        AuthFlow: 'RetryAuthFlow',
                        whatsappEnabled: isWhatsAppOtp || isWhatsAppOTP,
                    };
                    reqParams['captchaReference'] = token;
                    userSignUp(
                        reqParams,
                        token,
                        locale,
                        userInfo,
                        onEmailSignUpSuccess,
                        onEmailSignUpError
                    );
                    break;
                }
            }
            setProcessState('complete');
        } catch (error: any) {
            setProcessState('complete');
            Sentry.captureMessage(`Error occured in sendAgain ${error}`);
            console.log('Error occurred in sendAgain ', error);
            showMessage(error?.MESSAGE, SEVERITY.ERROR, t('authError'));
        }
    };

    const handleOnConfirm = () => {
        window.location.href = appRedirectUrl;
        setShowRedirectPrompt(false);
        return false;
    };

    return (
        <div className={styles['phone-otp-confirmation__list']}>
            <ul className={styles['phone-otp-confirmation__list']}>
                <li className={styles['phone-otp-confirmation__list-item']}>
                    <FormControl variant="standard" fullWidth={true}>
                        <OTPInput
                            value={otpValue}
                            onChange={inputOnChange}
                            numInputs={6}
                            renderInput={(props) => <input {...props} />}
                            containerStyle={
                                styles[
                                    'phone-otp-confirmation__list-item__input-field'
                                ]
                            }
                            inputStyle={
                                hasOTPError
                                    ? 'otp-input-field-error'
                                    : 'otp-input-field'
                            }
                            shouldAutoFocus={true}
                            inputType={'number'}
                        />
                    </FormControl>
                </li>
                {hasOTPError && (
                    <p
                        className={` ${styles['phone-otp-confirmation__invalid-otp']}`}
                    >
                        {getErrorMessage()}
                    </p>
                )}
                <li
                    className={`button-container ${styles['phone-otp-confirmation__list-item']} ${styles['phone-otp-confirmation__verify-btn']}`}
                >
                    <Button
                        buttonRef={verifyButtonRef}
                        theme="purple"
                        action={handleConfirmOtp}
                        className={` submit-button ${styles['continue-button']}
								${processState === 'inProcess' ? `button-submitted` : ''}
                                `}
                        attribues={{
                            disabled: buttonDisabled,
                            type: 'button',
                        }}
                    >
                        {t('verify')}
                    </Button>
                </li>
            </ul>

            <div
                className={`${
                    styles['phone-otp-confirmation__resend-container']
                } ${
                    !resendDeliveryEnabled ||
                    startCounter ||
                    (isEmailVerificationFlow && startCounter) ||
                    (isEmailVerificationFlow && !hasPhoneNumber)
                        ? styles[
                              'phone-otp-confirmation__resend-container-single'
                          ]
                        : ''
                }`}
            >
                <div
                    className={
                        styles[
                            'phone-otp-confirmation__resend-container__resend-otp'
                        ]
                    }
                >
                    {startCounter ? (
                        <>
                            <p
                                className={
                                    styles[
                                        'phone-otp-confirmation__resend-container__resend-otp__timer-text'
                                    ]
                                }
                            >
                                {' '}
                                {t('resendCodeIn')} -{' '}
                            </p>{' '}
                            <span> {children} </span>
                        </>
                    ) : (
                        <p
                            className={
                                styles[
                                    'phone-otp-confirmation__resend-container__resend-otp__resend-sms'
                                ]
                            }
                            onClick={() => handleSendAgainClick()}
                        >
                            {hasPhoneNumber
                                ? t('resendOtpSMS')
                                : t('resendOTP')}
                        </p>
                    )}
                </div>

                {resendDeliveryEnabled && hasPhoneNumber && !startCounter && (
                    <div
                        className={
                            styles[
                                'phone-otp-confirmation__resend-container__whatsapp'
                            ]
                        }
                        onClick={() => handleSendAgainClick(true)}
                    >
                        <img
                            src={`${imageBaseUrl}/icons/whatsapp.svg`}
                            alt={'whatsapp'}
                        />
                        <p>{t('sendOtpOnWhatsApp')}</p>
                    </div>
                )}
            </div>

            <div className={styles['trouble-signin-container']}>
                <div className={styles['trouble-signin']}>
                    <Image
                        src={`${imageBaseUrl}/icons/info-circle.svg`}
                        alt="Trouble Sign In"
                        width={16}
                        height={16}
                    />
                    <span className={styles['content']}>
                        {t('troubleSignIn')}
                    </span>
                </div>
                <HelpText />
            </div>

            <Confirm
                icon={`${imageBaseUrl}/icons/redirect-arrow.png`}
                open={showRedirectPrompt}
                onClose={handleOnConfirm}
                message={t('youWillBeRedirectedToYougotagift')}
                title={t('redirectingToYougotagift') || ''}
                showCancel={false}
                fullsizeButton={true}
            ></Confirm>
        </div>
    );
};

export default ResponseInfoHOC(PhoneOtpConfirmation);
