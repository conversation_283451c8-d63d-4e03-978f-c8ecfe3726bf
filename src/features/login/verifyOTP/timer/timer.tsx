import { useEffect, useState } from "react";

const TIMER_MAX = 60;

const IntervalTimer = ({triggerTimerStop, timerStarted}:any) => {
    const [timerMap, setTimerMap] = useState<any>({});
    const [startTime, setStartTime] = useState(new Date().getTime());
    const [time, setTime] = useState(TIMER_MAX);

    useEffect(()=>{
        if(timerStarted){
            resetCounter();
        }
    },[timerStarted]);

    useEffect(() => {
        const timerId = setInterval(() => {
            const remainingSec : any = getRemainingSeconds();
            setTime(remainingSec);
        }, 1000);
        return () => clearInterval(timerId);
    }, [setTime]);

    /**
     * Method to calculate the remaining time
     * @returns 
     */
    const getRemainingSeconds = () => {
        const currentTime = new Date().getTime();
        const timeDiff = currentTime - startTime;
        let remainingSeconds = Math.floor((timeDiff / 1000) % TIMER_MAX);
        if ((Object.keys(timerMap).length != 0 && timerMap[remainingSeconds]) || remainingSeconds == 0) {
            triggerTimerStop();
            return;
        }
        timerMap[remainingSeconds] = true;
        setTimerMap(timerMap);
        return remainingSeconds;
    };

    /**
     * Method to reset the counter
     */
    const resetCounter = () => {
        setTimerMap({});
    };

    /**
     * Formatting time
     * @param time : Time
     * @returns : Formatted remaining time
     */
    const formatTime = (time: number) =>
    `${String(Math.floor(time / 60)).padStart(2, '0')}:${String(
        time % 60
    ).padStart(2, '0')}`;

    /**
     * Method to show the formatted remaining time
     * @param param0 
     * @returns 
     */
    const Timer = ({ time }: { time: number }) => {
        const timeRemain = TIMER_MAX - time;
        return <span>{formatTime(timeRemain)}</span>;
    };

    return <Timer time={time} />;
};

export default IntervalTimer;

