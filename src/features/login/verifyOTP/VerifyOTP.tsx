import VerifyOtpResponse from './verifyOtpResponse/VerifyOtpResponse';
import { useTranslation } from 'next-i18next';
import { useEffect, useState } from 'react';
import styles from './VerifyOTP.module.scss';
import {
    FieldToVerify,
    getCountryCode,
    getNumberMobile,
    getPageFlow,
    getUserInfo,
} from '../loginFlowSlice';
import { useAppSelector } from '@redux/hooks';
import { PAGE_FLOW, PLATFORM_TYPE } from '@constants/common';
import maskText from 'utils/maskText';
import parsePhoneNumber from 'libphonenumber-js';
import IntervalTimer from './timer/timer';
import getConfig from 'next/config';
import Image from 'next/image';
import { useMediaQuery } from '@mui/material';
import Link from 'next/link';

const VerifyOTP = ({ platformType, recaptchaLoaded, reCaptchaRef, logo }: any) => {
    // Translation
    const { t } = useTranslation('common');
    const {
        publicRuntimeConfig: { defaultRedirectUrl },
    } = getConfig();

    const userInfo = useAppSelector(getUserInfo);
    const pageFlow = useAppSelector(getPageFlow);
    let countryCode: any = useAppSelector(getCountryCode);
    let numberMobile: any = useAppSelector(getNumberMobile);
    const isEmailVerificationFlow = userInfo?.toVerify == FieldToVerify?.Email;
    const hasPhoneNumber = userInfo?.phone_number;
    const isWeb = useMediaQuery('(min-width:768px)');
    const isSignUp = [
        PAGE_FLOW.EMAIL_SIGN_UP,
        PAGE_FLOW.MOBILE_SIGN_UP,
        PAGE_FLOW.SOCIAL_SIGN_UP_WITHOUT_EMAIL,
        PAGE_FLOW.SOCIAL_SIGN_UP_WITH_EMAIL,
    ].includes(pageFlow);

    if (!numberMobile || !numberMobile.length) {
        if (userInfo.phone_number) {
            let pNumber = parsePhoneNumber(userInfo.phone_number);
            countryCode = pNumber?.countryCallingCode;
            numberMobile = pNumber?.nationalNumber;
        }
    }

    let newPhone;
    if (numberMobile && numberMobile.length > 3) {
        let firstPart = numberMobile?.slice(0, 1);
        let lastPart = numberMobile?.slice(-2);

        newPhone = `+${countryCode} ${firstPart}${'x'.repeat(
            numberMobile.length - 3
        )}${lastPart}`;
    }

    let maskedEmail = userInfo.email
        ? maskText(userInfo.email, 'email', '*')
        : '';

    // otp sent status
    const [otpSentStatus, setOtpSentStatus] = useState<'sent' | 'resent'>(
        'sent'
    );
    const [timerStarted, setTimerStarted] = useState(true);

    const triggerTimerStop = () => {
        setTimerStarted(false);
    };

    const triggerTimerStart = () => {
        setTimerStarted(true);
    };

    return (
        <div className={styles['signin-verification-code']}>
            <div className={styles['signin-verification-code__title']}>
                <div className={styles['signin-verification-code__title-left']}>
               {/* {isSignUp && <h4>{t('getStarted')}</h4>} */}
                <h3>{t('enterVerificationCode')}</h3>
                </div>
                {
                        platformType != PLATFORM_TYPE.APP.toLowerCase() ? <Link href={defaultRedirectUrl}>
                            <img
                                src={logo}
                                alt="logo"
                                width={60.142}
                                height={40}
                                
                            />
                        </Link> : <img
                            src={logo}
                            width={60.142}
                            height={40}
                            alt="logo"
                        />
                    }
                {/* <Link href={defaultRedirectUrl}>
                    <Image
                        src={logo}
                        alt="ygg-logo"
                        height={40}
                        width={60.142}
                    />
                </Link> */}
                
            </div>
            {!isWeb && (
                <div className={styles['signin-verification-code__mweb-title']}>
                    {/* {isSignUp && <h4>{t('getStarted')}</h4>} */}
                    <h3>{t('enterVerificationCode')}</h3>
                </div>
            )}
            {pageFlow != PAGE_FLOW.CUSTOM_SIGN_IN && (
                <p className={styles['signin-verification-code__info']}>
                    {otpSentStatus == 'sent'
                        ? userInfo.toVerify == FieldToVerify.Email
                            ? t('weHaveSentVerificationCodeToEmail')
                            : t('weHaveSentVerificationCode')
                        : userInfo.toVerify == FieldToVerify.Email
                        ? t('weHaveReSentVerificationCodeToEmail')
                        : t('weHaveReSentVerificationCode')}
                    <span>
                        &nbsp;
                        <br className="mb-hide" />
                    </span>

                    <span
                        className={
                            styles['signin-verification-code__info-emailMobile']
                        }
                        dir="ltr"
                    >
                        {userInfo.toVerify == FieldToVerify.Email
                            ? `${
                                  userInfo.email
                                      ? maskText(userInfo.email, 'email', '*')
                                      : ''
                              }`
                            : `${newPhone}`}
                    </span>
                </p>
            )}

            {pageFlow == PAGE_FLOW.CUSTOM_SIGN_IN && (
                <p className={styles['signin-verification-code__info']}>
                    {otpSentStatus == 'sent'
                        ? t('weHaveSentVerificationCodeToBothEmailPhone')
                        : t('weHaveSentVerificationCodeToBothEmailPhone')}
                    <span>
                        &nbsp;
                        <br className="mb-hide" />
                    </span>

                    <span
                        className={
                            styles['signin-verification-code__info-emailMobile']
                        }
                        dir="ltr"
                    >
                        {userInfo.phone_number}
                    </span>
                    <span>{`${t('andAssociatedEmail')}`}</span>
                    <span
                        className={
                            styles['signin-verification-code__info-emailMobile']
                        }
                        dir="ltr"
                    >
                        {`${maskedEmail}`}
                    </span>
                </p>
            )}
            <VerifyOtpResponse
                platformType={platformType}
                isEmailVerificationFlow={isEmailVerificationFlow}
                timerStarted={timerStarted}
                triggerTimerStart={triggerTimerStart}
                hasPhoneNumber={hasPhoneNumber}
            >
                <IntervalTimer
                    triggerTimerStop={triggerTimerStop}
                    timerStarted={timerStarted}
                />
            </VerifyOtpResponse>
        </div>
    );
};

export default VerifyOTP;
