@import '@styles/variables';
@import '@styles/mixins';

.phone-otp-confirmation {
    &__list {
        list-style: none;
        padding: 0;
        margin: 30px 0 0 0;

        @media only screen and (max-width: $sm) {
            margin: 24px 0 0;
        }

        &-item {
            padding-bottom:0 !important;

            .phone-number-otp {
                margin-top: 10px;

                input {
                    margin-top: 10px;
                    font-family: Poppins;
                    font-size: 16px;
                    font-weight: 500;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    text-align: left;
                    color: $dark-purple;

                    @media only screen and (max-width: $sm) {
                        width: 200px;
                        height: 28px;
                        margin: 11.5px 0 0 76.5px;
                        font-size: 20px;
                        font-weight: 500;
                        line-height: 1.5;
                        letter-spacing: 20px;
                        text-align: center;
                    }

                    @include rtl-styles {
                        margin: 0;
                        text-align: right;

                        @media only screen and (max-width: $sm) {
                                
                                margin: 11.5px 76px 0 76.5px;
                               
                                text-align: center;
                            }
                    }
                }

                input:focus{
                    @include animation('anime 0.01s');
                }
            }
            &__input-field {
                justify-content: space-between;
                display: flex;
                padding-bottom: 32px;
                gap: 16px;

                @include rtl-styles {
                    direction: ltr;
                }
                @media only screen and (max-width: $sm) {
                    gap: 0px;
                    padding-bottom: 0;
                    justify-content: space-between;
                    margin-bottom: 24px;
                }

                input {
                    margin: 0;
                    font-size: 32px;
                    font-weight: 800;
                    line-height: 40px;
                    letter-spacing: -0.16px;
                    caret-color: transparent;

                    @media only screen and (max-width: $sm) {
                    font-size: 24px !important;
                    }

                    &:focus {
                        border: 2px solid $dark-charcoal;
                    }
                }
            }
        }

     
    }

        &__resend-container {
            display: flex;
            justify-content: space-between;
            margin-top: 8px;
    
            @media only screen and (max-width: $sm) {
                flex-direction: column;
                align-items: center;
                padding-top: 0;
                margin-top: 16px;
            }
            
            @include rtl-styles { 
                gap: 10px;
            }
    
            &__resend-otp {
                display: flex;
                margin: 0;
                margin-top: 12px !important;
    
                @include font-size(16);
    
                &__timer-text {
                    color: $dark-charcoal;
                    margin: 0;
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 18px;
                    letter-spacing: -0.14px;
                    font-family: $mona-sans-font-family;

                    @include rtl-styles{
                        font-family: $arabic-font-family;
                    }
                    }
    
                span {
                    color: $semi-dark-purple;
                    margin-left: 3px;
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 18px;
                    letter-spacing: -0.14px;
                    font-family: $mona-sans-font-family;

                    @include rtl-styles { 
                        font-family: $arabic-font-family;
                        margin-left: 0;
                        margin-right: 3px;
                    }
                }
    
                &__resend-sms {
                    color: $semi-dark-purple;
                    cursor: pointer;
                    margin: 0;
                    font-family: $mona-sans-font-family;
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 18px;
                    letter-spacing: -0.14px;

                    @include rtl-styles{
                        font-family: $arabic-font-family;
                    }
                }
                @media only screen and (max-width: $sm) {
                    margin-top: 0 !important;
                }
            }
    
            &__whatsapp {
                display: flex;
                align-items: center;
                cursor: pointer;

                @include rtl-styles { 
                align-items: flex-start;
                }
    
                @media only screen and (max-width: $sm) {
                    padding-top: 24px;
                }
    
                img {
                    margin-right: 8px;
                    height: 20px;

                    @include rtl-styles { 
                        margin-right: 0;
                        margin-left: 8px;
                        margin-top: 6px;
                    }
                }
    
                p {
                    color: $semi-dark-purple;
                    margin: 0;
                    font-family: $mona-sans-font-family;
                    font-weight: 600;
                    line-height: 18px;
                    letter-spacing: -0.14px;

                    @include rtl-styles{
                        font-family: $arabic-font-family;
                    }
    
    
                    @include font-size(14);
            }
        }


    }

        &__resend-container-single {
            justify-content: center;
        }

    &__time-left {
        line-height: 27px;
        text-align: center;
        margin-bottom: 0;
        @include font-size(14);

        span,
        a {
            color: $barney-purple;
            cursor: pointer;
        }

        span {
            display: inline-block;
            width: 45px;
        }
    }

    &__check-spam {
        text-align: center;
        margin: 0;
        color: $black-header;

        @media only screen and (max-width: $sm) {
            font-size: 12px;
            margin: 0 0 40px;
        }
    }
    &__invalid-otp {        
        color: $red-secondary;
        font-size: 14px;
        margin: 0;
        margin-top: -24px;
        margin-bottom: 32px;

        @media only screen and (max-width: $sm) { 
            margin: -8px 0 30px;
        }
    }

    &__devider {
        height: 0.5px;
        background-color: $white;
        margin-top: 32px;
    }
    
    &__help-container {
        padding-top: 32px;

        @media only screen and (max-width: $sm) { 
            padding-top: 0;
        }
    }

    &__verify-btn {
        // margin-bottom: 12px !important;
    }
}

@include keyframes(anime) {
    0% { opacity: 0; }
    100% { opacity: 1; }
  }


