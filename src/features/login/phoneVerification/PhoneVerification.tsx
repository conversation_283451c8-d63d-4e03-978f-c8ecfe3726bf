import Button from '@features/common/button/Button';
import {
    FormControl,
    InputLabel,
    TextField,
    FormHelperText,
    useMediaQuery,
} from '@mui/material';
import { useTranslation } from 'next-i18next';
import { useContext, useEffect, useState } from 'react';
import styles from './PhoneVerification.module.scss';
import {
    generateLoginOTP,
    sendOTP,
    signInWithMobile,
} from '@features/common/signInSignUpAPI';
import { SEVERITY } from '@constants/messageTypes';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import ResponseInfoHOC from '@features/signinSignup/common/responseInfoHOC/ResponseInfoHOC';
import {
    FieldToVerify,
    getPageFlow,
    getUserInfo,
    setCognitoCustomResponse,
    setCountryCode,
    setNumberMobile,
    setPageFlow,
    setResendOtpReq,
    setSignInResponse,
    setUserInfo,
    UserInfo,
} from '../loginFlowSlice';
import useAppRouter from '@features/common/router.context';
import {
    getCountryCallingCode,
    parsePhoneNumber,
    isValidPhoneNumber,
    getCountries,
} from 'react-phone-number-input/input-max';
import en from 'react-phone-number-input/locale/en.json';
import { CountryCode } from 'libphonenumber-js/types';
import {
    CountryPhoneCode,
    getDefaultCountryCode,
} from '@features/common/countryPhoneCode/CountryPhoneCode';
import {
    API_STATUS,
    AUTH_FLOW,
    COGNITO_USER_TYPE,
    PAGE_FLOW,
    PLATFORM_TYPE,
    SIGN_IN_SLUGS,
} from '@constants/common';
import isNumber from '@utils/isNumber';
import cloneDeep from 'lodash/cloneDeep';
import { useForm } from 'react-hook-form';
import { useRef } from 'react';
import useReCaptchaAPI from '@features/common/reCaptcha/ReCaptchaAPI';
import * as Sentry from '@sentry/nextjs';
import {
    executeCaptcha,
    otpGeneratedSuccessfully,
    otpLimitExceededOrCaptchaFailed,
    phoneEmailAlreadyExists,
    emailIsWorkEmail
} from '@utils/signInSignUpHelper';
import getConfig from 'next/config';
import { CaptchaContext } from '@features/common/captcha.context';
import { getIsCaptchaRequired } from '@features/common/commonSlice';
import useAuthAPI from '../authAPI';
import { SignUpResponse } from '../authAPI.interface';
import fetchAPI from '@utils/fetcher';
import Warning from '../emailLogin/warning/warning';
import Image from 'next/image';
import HelpText from '@features/signinSignup/common/helpText/HelpText';
import Link from 'next/link';

const {
    publicRuntimeConfig: { defaultRedirectUrl },
} = getConfig();

const PhoneVerification = ({
    showMessage,
    setOtpScreen,
    showMessageAlt,
    ipCountry,
    blacklistedCountries,
    logo,
    platformType
}: {
    showMessage: (
        message: string,
        severity: SEVERITY,
        heading?: string | null
    ) => void;
    showMessageAlt: any;
    setOtpScreen?: any;
    ipCountry?: CountryCode;
    blacklistedCountries?: [];
    logo: any;
    platformType: any;
}) => {
    // translations
    const { t } = useTranslation('common');

    const { evaluateCaptcha } = useReCaptchaAPI();

    const DEFAULT_COUNTRY = 'AE';

    const captchaContext: any = useContext(CaptchaContext);

    const blackListedCountries: any = blacklistedCountries || [];

    const countriesList = getCountries().filter(
        (country: any) => !blackListedCountries.includes(country)
    );

    const {
        state: { region, locale, activeRegion, regionUrl },
        redirect,
        router,
    } = useAppRouter();

    // taking public image config url
    const {
        publicRuntimeConfig: { imageBaseUrl },
    } = getConfig();

    const pageFlow = useAppSelector(getPageFlow);
    let userInfo = useAppSelector(getUserInfo);
    const isCaptchaRequired = useAppSelector(getIsCaptchaRequired);
    const isSignUp = [
        PAGE_FLOW.EMAIL_SIGN_UP,
        PAGE_FLOW.MOBILE_SIGN_UP,
        PAGE_FLOW.SOCIAL_SIGN_UP_WITHOUT_EMAIL,
        PAGE_FLOW.SOCIAL_SIGN_UP_WITH_EMAIL,
    ].includes(pageFlow);

    const [selectedCountry, setSelectedCountry] = useState<CountryCode>(
        getDefaultCountryCode(
            blackListedCountries,
            DEFAULT_COUNTRY,
            countriesList,
            ipCountry
        )
    );
    const [countryErrorMessage, setCountryErrorMessage] = useState<any>('');
    const [mobileErrorMessage, setMobileErrorMessage] = useState<any>('');
    const [buttonDisabled, setButtonDisabled] = useState(true);
    const { register, handleSubmit, getValues } = useForm();
    const phoneNumberRef = useRef<HTMLInputElement>(null);
    const { userSignUp } = useAuthAPI();
    const [showWarning, setShowWarning] = useState<boolean>(false);
    const [processState, setProcessState] = useState<'inProcess' | 'complete'>(
        'complete'
    );

    const isWeb = useMediaQuery('(min-width:768px)');

    const dispatch = useAppDispatch();

    const [recaptchaLoaded, setRecaptchaLoaded] = useState(false);

    const [formData, setFormData] = useState<any>({});

    useEffect(() => {
        if (captchaContext.tokenGenerated && captchaContext.token) {
            console.log('token is ', captchaContext.token);
            onCaptchaChange();
            captchaContext.function.reset();
        }
    }, [captchaContext.tokenGenerated, captchaContext.token]);

    const onCaptchaChange = async () => {
        const token = captchaContext.token;
        if (token) {
            let countryCode = getCountryCallingCode(selectedCountry);
            let enteredMobile = formData?.mobile;
            let mobileNumber = `+${countryCode}${enteredMobile}`;
            setButtonDisabled(true);

            try {
                if (pageFlow == PAGE_FLOW.NONE) {
                    await handleMobileSignUpSignIn(
                        mobileNumber,
                        token,
                        'cognitoCustomQuery'
                    );
                } else {
                    await handleUpdateMobile(mobileNumber, token);
                }
            } catch (error: any) {
                console.log('Error in process ', error);
                Sentry.captureException(error);
                setButtonDisabled(false);
                setProcessState('complete');
            }
        } else {
            showMessage(
                `No captcha token found`,
                SEVERITY.ERROR,
                t('authError')
            );
        }
    };

    const proceedWithoutCaptcha = async (formData: any) => {
        let countryCode = getCountryCallingCode(selectedCountry);
        let enteredMobile = formData?.mobile;
        let mobileNumber = `+${countryCode}${enteredMobile}`;
        setButtonDisabled(true);

        try {
            if (pageFlow == PAGE_FLOW.NONE) {
                await handleMobileSignUpSignIn(
                    mobileNumber,
                    null,
                    'cognitoCustomQuery'
                );
            } else {
                await handleUpdateMobile(mobileNumber);
            }
        } catch (error: any) {
            console.log('Error in process ', error);
            Sentry.captureException(error);
            setButtonDisabled(false);
            setProcessState('complete');
        }
    };

    const resetErrorMessages = () => {
        setCountryErrorMessage('');
        setMobileErrorMessage('');
    };

    const continueToNextStep = () => {
        if (!!!setOtpScreen) {
            router.push(
                `?slug=${SIGN_IN_SLUGS.VERIFY_OTP}`,
                `${SIGN_IN_SLUGS.VERIFY_OTP}`,
                { locale, shallow: true }
            );
        }
        return false;
    };

    const handleMobileSignUp = async (mobileNumber: any, token: any) => {
        let info = cloneDeep(userInfo);
        info.phone_number = mobileNumber;
        let reqParams = preparePhoneSignUpOtpReqParams(info, token);
        dispatch(setResendOtpReq(reqParams));
        let response: any = await sendOTP(reqParams, locale);
        if (response && response?.STATUS_CODE == API_STATUS.OTP_GENERATED) {
            // ## OTP generated successfully

            // ## check for session in response
            let info = cloneDeep(userInfo);
            info.SessionId = response?.SESSION_KEY;
            info.toVerify = FieldToVerify.Phone;
            dispatch(setUserInfo(info));

            dispatch(setPageFlow(PAGE_FLOW.MOBILE_SIGN_UP));
            return true;
        } else if (
            response &&
            response?.STATUS_CODE ==
                API_STATUS.OTP_GENERATED_WITH_CONTEXT_SWITCH_EMAIL
        ) {
            router.push(
                `?slug=${SIGN_IN_SLUGS.EMAIL_LOGIN}`,
                `${SIGN_IN_SLUGS.EMAIL_LOGIN}`,
                { locale, shallow: true }
            );
            return false;
        } else {
            console.log('Payload to Mobile Sign Up Endpoint ', reqParams);
            Sentry.captureMessage(
                `Unexpected status code ${response?.STATUS_CODE} returned from Mobile Sign up API. Error Message : ${response?.MESSAGE}`
            );
            showMessage(response?.MESSAGE, SEVERITY.ERROR, t('authError'));
            return false;
        }
    };

    const onUpdateMobileSuccess = (
        response: SignUpResponse,
        userInfo: UserInfo,
        reqParams: any
    ) => {
        try {
            const {
                authSignup: {
                    data: { message, statusCode, sessionKey },
                },
                offline,
            } = response;
            if (phoneEmailAlreadyExists(statusCode as API_STATUS)) {
                showMessage(
                    t('mobileAlreadyAssociatedWithAnotherAccount'),
                    SEVERITY.ERROR,
                    t('authError')
                );
                setButtonDisabled(false);
            } else if (
                otpLimitExceededOrCaptchaFailed(statusCode as API_STATUS)
            ) {
                showMessage(message, SEVERITY.ERROR, t('authError'));
                setButtonDisabled(false);
            } else if (otpGeneratedSuccessfully(statusCode as API_STATUS)) {
                // ## check for session in response
                userInfo.SessionId = sessionKey;
                userInfo.toVerify = FieldToVerify.Phone;
                dispatch(setUserInfo(userInfo));

                let req = cloneDeep(reqParams);
                req.SessionId = sessionKey;
                dispatch(setResendOtpReq(req));

                // ## navigate to OTP Entry Screen
                router.push(
                    `?slug=${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    `${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    { locale, shallow: true }
                );
            } else {
                // ## some other BE issue
                showMessage(message, SEVERITY.ERROR, t('authError'));
                setButtonDisabled(false);
            }
            setProcessState('complete');
        } catch (error) {
            console.log('Error occured in onUpdateMobileSuccess ', error);
        }
    };

    const onUpdateMobileError = () => {};

    const socialSignUpFlow = (pageFlow: PAGE_FLOW) => {
        return [
            PAGE_FLOW.SOCIAL_SIGN_UP_WITHOUT_EMAIL,
            PAGE_FLOW.SOCIAL_SIGN_UP_WITH_EMAIL,
        ].includes(pageFlow);
    };

    const prepareSocialSignUpReqParams = (
        userInfo: UserInfo,
        token: string
    ) => {
        let reqParams = {
            phoneNumber: userInfo.phone_number,
            captchaReference: token,
            idpSignature: userInfo.idp_signature,
        };

        if ([COGNITO_USER_TYPE.GOOGLE, COGNITO_USER_TYPE.FACEBOOK, COGNITO_USER_TYPE.APPLE].includes(userInfo.userType)) {
            const socialInfo = {
                UserAttributes: {
                    name: userInfo.UserAttributes?.name,
                    picture: userInfo.UserAttributes?.picture,
                    birthdate: userInfo?.UserAttributes?.birthdate ?? '',
                    gender: userInfo?.UserAttributes?.gender ?? ''
                },
                AuthFlow: AUTH_FLOW.SOCIAL
            };
            reqParams = { ...reqParams, ...socialInfo }
        }
        return reqParams;
    };

    const handleUpdateMobile = async (mobileNumber: any, token: any = null) => {
        try {
            setProcessState('inProcess');
            if (pageFlow == PAGE_FLOW.NONE) {
                dispatch(setPageFlow(PAGE_FLOW.MOBILE_SIGN_UP));
            }

            let info = cloneDeep(userInfo);
            info.phone_number = mobileNumber;
            let reqParams;
            if (socialSignUpFlow(pageFlow)) {
                reqParams = prepareSocialSignUpReqParams(info, token);
            } else {
                reqParams = preparePhoneOtpReqParams(info, token);
            }

            dispatch(setResendOtpReq(reqParams));

            userSignUp(
                reqParams,
                token,
                locale,
                info,
                onUpdateMobileSuccess,
                onUpdateMobileError
            );
        } catch (error) {
            console.log('Error occured in handleUpdateMobile ', error);
        }
    };

    const handleMobileSignUpSignIn = async (
        mobileNumber: any,
        token: any = null,
        cognitoCustomQuery: any
    ) => {
        try {
            let reqParams = cloneDeep(userInfo);
            reqParams.phone_number = mobileNumber;
            let signInResponse = await generateLoginOTP(
                reqParams,
                'phone_number',
                token,
                locale
            );

            if (signInResponse && signInResponse?.offline) {
                showMessage(
                    t('checkYouInternetConnection'),
                    SEVERITY.ERROR,
                    t('authError')
                );
                setButtonDisabled(false);
                setProcessState('complete');
            } else if (signInResponse && signInResponse?.STATUS_CODE) {
                switch (signInResponse?.STATUS_CODE) {
                    case API_STATUS.USER_NOT_FOUND:
                        // ## phone number doesn't exists
                        const token_signUp: any = await evaluateCaptcha(
                            'signin_signup'
                        );
                        let res = await handleMobileSignUp(
                            mobileNumber,
                            token_signUp
                        );
                        if (res) {
                            if (setOtpScreen) {
                                setOtpScreen(true);
                            }
                            continueToNextStep();
                        }

                        setButtonDisabled(false);
                        setProcessState('complete');
                        break;
                    case API_STATUS.OTP_GENERATED:
                    case API_STATUS.OTP_GENERATED_WITH_CONTEXT_SWITCH:
                        if (pageFlow == PAGE_FLOW.NONE) {
                            // ## handle mobile sign in
                            let cognitoCustomResponse =
                                await cognitoCustomQuery;
                            dispatch(
                                setCognitoCustomResponse(cognitoCustomResponse)
                            );
                            reqParams.SessionId = signInResponse?.SESSION_KEY;
                            reqParams.email =
                                signInResponse?.MFA_VALUES?.email_address;
                            dispatch(setSignInResponse(signInResponse));
                            dispatch(setUserInfo(reqParams));
                            dispatch(
                                setResendOtpReq({
                                    type: 'phone_number',
                                    token: token,
                                })
                            );
                            dispatch(setPageFlow(PAGE_FLOW.CUSTOM_SIGN_IN));
                            continueToNextStep();
                        }
                        if (setOtpScreen) {
                            setOtpScreen(true);
                        }
                        setButtonDisabled(false);
                        break;
                    default:
                        Sentry.captureMessage(
                            `Unexpected status code ${signInResponse?.STATUS_CODE} returned from Login API. Error Message : ${signInResponse?.MESSAGE}`
                        );
                        showMessage(
                            signInResponse?.MESSAGE,
                            SEVERITY.ERROR,
                            t('authError')
                        );
                        setButtonDisabled(false);
                        setProcessState('complete');
                        break;
                }
            }
        } catch (error) {
            Sentry.captureException(error);
            setButtonDisabled(false);
            setProcessState('complete');
        }
    };

    const handleFormSubmit = async (formData: any) => {
        setProcessState('inProcess');

        let countryCode = getCountryCallingCode(selectedCountry);
        let enteredMobile = formData?.mobile;
        let mobileNumber = `+${countryCode}${enteredMobile}`;
        const cognitoCustomQuery = signInWithMobile(mobileNumber);

        resetErrorMessages();
        dispatch(setNumberMobile(enteredMobile));
        dispatch(setCountryCode(countryCode));

        if (!validCountrySelection() || !validMobileNumber(enteredMobile)) {
            setProcessState('complete');
            return;
        }

        const userExistsResponse: any = await fetchAPI('/api/ratify', {
            method: 'POST',
            body: JSON.stringify({
                email: userInfo && userInfo.email ? userInfo?.email.trim() : '',
            }),
        });

        const { c: statusCode } = userExistsResponse?.data ?? { c: 0 };

        if (emailIsWorkEmail(statusCode)) {
            setShowWarning(true);
            setProcessState('complete');
            return false;
        }

        // updateMobile indicates the screen to update mobile number to account
        // on sign up via email. When sign up via email, updateMobile is true
        setProcessState('complete');
        setFormData(formData);
        if (isCaptchaRequired) {
            executeCaptcha(captchaContext['elem']);
        } else {
            proceedWithoutCaptcha(formData);
        }
    };

    const preparePhoneOtpReqParams = (userInfo: UserInfo, captcha: any) => {
        return {
            captchaReference: captcha,
            phoneNumber: userInfo.phone_number,
            SessionId: userInfo.SessionId,
        };
    };

    const preparePhoneSignUpOtpReqParams = (
        userInfo: UserInfo,
        captcha: any
    ) => {
        let reqParams: any = cloneDeep(userInfo);
        delete reqParams.email;
        delete reqParams.UserAttributes;
        reqParams['captcha_reference'] = captcha;
        return reqParams;
    };

    /**
     * Method to validate country selection
     */
    const validCountrySelection = () => {
        if (selectedCountry) return true;
        setCountryErrorMessage(t('invalidCountrySelection'));
        return false;
    };

    const validMobileNumber = (enteredMobile: any) => {
        let countryCode = getCountryCallingCode(selectedCountry);
        let mobileNumber = `+${countryCode}${enteredMobile}`;
        if (
            !enteredMobile ||
            !enteredMobile.length ||
            !isValidPhoneNumber(mobileNumber)
        ) {
            setMobileErrorMessage(t('invalidPhoneNumber'));
            return false;
        }
        return true;
    };

    const onError = (error?: any) => {
        console.log('Error in captcha ', error);
    };

    const captchaOnChange = async (token: any) => {
        console.log('respon se is ', token);
        if (token) {
            let countryCode = getCountryCallingCode(selectedCountry);
            let enteredMobile = formData?.mobile;
            let mobileNumber = `+${countryCode}${enteredMobile}`;
            setButtonDisabled(true);

            try {
                if (pageFlow == PAGE_FLOW.NONE) {
                    await handleMobileSignUpSignIn(
                        mobileNumber,
                        token,
                        'cognitoCustomQuery'
                    );
                } else {
                    await handleUpdateMobile(mobileNumber, token);
                }
            } catch (error: any) {
                console.log('Error in process ', error);
                Sentry.captureException(error);
                setButtonDisabled(false);
                setProcessState('complete');
            }
        } else {
            showMessage(
                `No captcha token found`,
                SEVERITY.ERROR,
                t('authError')
            );
        }
    };

    const closePopUp = () => {
        setShowWarning(false);
        router.push('/login/', undefined, { locale });
    };

    return (
        <>
            <div
                className={`phone-verification ${styles['phone-verification']}`}
            >
                {pageFlow != PAGE_FLOW.NONE && (
                    <>
                        <div
                            className={styles['phone-verification__title-cont']}
                        >
                            {isWeb && (
                                <div
                                    className={
                                        styles['phone-verification__title']
                                    }
                                >
                                    {' '}
                                    {/* <h4>{t('getStarted')}</h4>{' '} */}
                                    <h3>{t('enterYourMobileNumber')}</h3>
                                </div>
                            )}

                            {
                                platformType != PLATFORM_TYPE.APP.toLowerCase() ? <Link href={defaultRedirectUrl}>
                                    <Image
                                        src={logo}
                                        alt="ygg-logo"
                                        height={40}
                                        width={60.142}
                                    />
                                </Link> : <Image
                                    src={logo}
                                    alt="ygg-logo"
                                    height={40}
                                    width={60.142}
                                />
                            }
                            
                            
                        </div>
                        {!isWeb && (
                            <div
                                className={
                                    styles['phone-verification__mweb-titles']
                                }
                            >
                                {isSignUp && <h4>{t('getStarted')}</h4>}
                                <h3>{t('enterYourMobileNumber')}</h3>
                            </div>
                        )}
                    </>
                )}

                <form
                    autoComplete="off"
                    onSubmit={handleSubmit(handleFormSubmit)}
                >
                    <ul className={styles['phone-verification__list']}>
                        <li className={styles['phone-verification__list-item']}>
                            <div
                                className={`
                                    ${
                                        styles['phone-verification__input-cont']
                                    } ${
                                    mobileErrorMessage
                                        ? styles[
                                              'phone-verification__input-cont-error'
                                          ]
                                        : ''
                                }
                                `}
                            >
                                <FormControl
                                    variant="standard"
                                    fullWidth={true}
                                >
                                    <InputLabel shrink={true}></InputLabel>
                                    <TextField
                                        {...register('mobile')}
                                        id="mobile"
                                        inputRef={phoneNumberRef}
                                        inputProps={{ inputMode: 'numeric' }}
                                        variant="standard"
                                        className={styles['phone-number']}
                                        placeholder={t('mobileNumber') || ''}
                                        onPaste={(event: any) => {
                                            if (
                                                event.clipboardData
                                                    .getData('Text')
                                                    .match(/[^\d]/)
                                            ) {
                                                event.preventDefault();
                                            }
                                        }}
                                        onKeyPress={(event: any) => {
                                            if (!isNumber(event)) {
                                                event.preventDefault();
                                            }
                                            if (event.key === 'Enter') {
                                                event.preventDefault();
                                                if (!buttonDisabled)
                                                    handleFormSubmit(
                                                        getValues()
                                                    );
                                            }
                                        }}
                                        onInput={(e: any) => {
                                            const re = /^[0-9\b]+$/;
                                            if (!re.test(e.target.value)) {
                                                e.target.value =
                                                    e.target.value.replace(
                                                        /.$/,
                                                        ''
                                                    );
                                            }
                                            if (e.target.value.length > 15) {
                                                e.target.value = e.target.value
                                                    .toString()
                                                    .slice(0, 15);
                                                setMobileErrorMessage(
                                                    t('only15DigitsAllowed')
                                                );
                                            } else {
                                                setMobileErrorMessage('');
                                            }
                                            setButtonDisabled(
                                                e.target.value.length < 7 ||
                                                    e.target.value.length > 15
                                            );
                                        }}
                                    />
                                    <div
                                        className={
                                            styles[
                                                'phone-verification__list-item__selected-country'
                                            ]
                                        }
                                    >
                                        <CountryPhoneCode
                                            setCountryCallback={({
                                                countryCode,
                                            }: any) => {
                                                setSelectedCountry(countryCode);
                                            }}
                                            enableSearch={true}
                                            language={locale}
                                            phoneNumberRef={phoneNumberRef}
                                            ipCountry={ipCountry}
                                            blacklistedCountries={
                                                blacklistedCountries
                                            }
                                        />
                                    </div>
                                    <FormHelperText
                                        error={Boolean(mobileErrorMessage)}
                                        className={
                                            styles[
                                                'phone-verification__form-input-validation'
                                            ]
                                        }
                                    >
                                        {mobileErrorMessage}
                                    </FormHelperText>
                                </FormControl>
                            </div>
                        </li>
                        <li
                            className={`button-container ${styles['phone-verification__list-item']}`}
                        >
                            <Button
                                id="btnSubmit"
                                theme="purple"
                                attribues={{
                                    disabled: buttonDisabled,
                                }}
                                loadingState={processState}
                                className={` submit-button ${
                                    styles['continue-button']
                                }
							${processState === 'inProcess' ? `button-submitted` : ''}`}
                            >
                                {t('continue')}
                            </Button>
                        </li>
                    </ul>
                </form>
                <div
                    className={`${styles['phone-verification__devider']}`}
                ></div>

                <div
                    className={`${styles['phone-verification__help-container']}`}
                >
                    <HelpText />
                </div>
                <Warning open={showWarning} onClose={closePopUp} />
            </div>
        </>
    );
};

export default ResponseInfoHOC(PhoneVerification);

type countryOptions = {
    [key: string]: string;
};
