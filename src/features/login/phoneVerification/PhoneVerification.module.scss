@import '@styles/variables';
@import '@styles/mixins';

.phone-verification {
    &__list {
        list-style: none;
        margin: 0;
        padding-bottom: 32px;
    }

    label {
        font-family: Poppins !important;
        font-size: 14px !important;
        font-weight: normal !important;
        font-stretch: normal;
        font-style: normal;
        line-height: 2.25;
        letter-spacing: 0.12px;
        text-align: left;
        color: $black-header !important;
    }

    &__menu-item {
        padding: 7px;
        display: grid;
        grid-template-columns: 17px auto;
        gap: 10px;
        min-height: 23px;
        font-size: 14px;

        &:hover {
            background-color: $light-purple;
            border-radius: $border-radius-min;
        }

        >div {
            >img {
                width: 15px;
                height: 11px;
                object-fit: contain;
                border-radius: 3px;
            }

            >span {
                @include font-size(14);
            }
        }
    }

    &__list-item:last-child {
        margin-top: 32px;
    }

    &__list-item {
        position: relative;

        &__selected-country {
            bottom: 0;
            display: flex;
            font-family: $mona-sans-font-family;
            font-size: 16px;
            font-weight: 500;
            @include rtl-styles{
                font-family: $arabic-font-family;
            }

            span {
                unicode-bidi: plaintext;
                display: flex;
                align-items: center;
                color: $dark-charcoal;
                font-family: $mona-sans-font-family;
                font-size: 16px;
                font-weight: 700;
                line-height: 24px;
                letter-spacing: -0.16px;
                height: 24px;
                width: 24px;

                @include rtl-styles{
                    font-family: $arabic-font-family;
                }

                img {
                    margin-right: 4px;

                    @include rtl-styles{
                        margin-right: auto;
                        margin-left: 4px;
                    }
                }
            }
        }

    }

    &__form {
        &-input {
            color: $warm-grey;

            &-invalid {
                border: 1px solid $red;
            }

            input {
                @include font-size(14);
            }

            &-active {
                border: 1px solid $barney-purple;
            }

            &-validation {
                position: absolute;
                top: 100% !important;
                left: 0 !important;
                color: $red !important;
                font-family: $default-font-family !important;
                font-optical-sizing: none;
                font-size: 1.2rem !important;
                line-height: 14px;

                @include rtl-styles {
                    right: 0 !important;
                    left: auto !important;
                    font-family: $arabic-font-family;
                }
            }
        }
    }

    &__title-cont {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 40px;

        h4 {
            font-size: 24px;
            font-weight: 800;
            line-height: 32px;
            margin: 0 0 8px;
        }

        h3 {
            margin: 0;
            font-size: 24px;
            font-style: normal;
            font-weight: 800;
            line-height: 32px; 
            background: linear-gradient(90deg, #4983F6 0%, #C175F5 50%, #FBACB7 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        @media only screen and (max-width: $sm) {
            justify-content: center;
            margin-bottom: 32px;
        }

    }

    &__input-cont {
        border-radius: 12px;
        border: 1px solid #D9D9D9;
        background: #fff;
        display: flex;
        height: 70px;
        padding: 10px 8px 10px 16px;
        justify-content: space-between;
        align-items: center;

        >div {
            display: flex;
            flex-direction: row-reverse;
        }

        &-error {
            border: 1px solid #E74848;
        }
    }

    &__devider {
        height: 0.5px;
        background-color: $white;
    }

    &__help-container {
        padding-top: 32px;
    }

    &__mweb-titles {
        h4 {
            color: $dark-charcoal;
            font-family: $default-font-family;
            font-optical-sizing: none;
            font-size: 18px;
            font-weight: 800;
            line-height: 24px;
            letter-spacing: -0.09px;
            margin: 0 0 8px;

            @include rtl-styles{
                font-family: $arabic-font-family;
            }
        }

        h3 {
            color: $dark-charcoal;

            font-family: $default-font-family;
            font-optical-sizing: none;
            font-size: 23px;
            font-weight: 800;
            line-height: 24px;
            margin-bottom: 32px;
            @include rtl-styles{
                font-family: $arabic-font-family;
            }
        }
    }

}

.phone-number label+div {
    min-height: 23px;
    padding-bottom: 10px;
    color: $dark-purple;
}

.phone-number {
    flex: auto !important;

    input {
        color: $dark-purple !important;
        width: 100%;
        height: 23px;
        font-family: $mona-sans-font-family !important;
        font-size: 16px !important;
        font-weight: 700;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;

        @include rtl-styles {
            font-family: $arabic-font-family;
            text-align: right;
            // margin-right: 50px;
        }
    }
}

.country-select {
    margin-top: 20px !important;
}

.country-select,
.phone-number label+div {
    &::before {
        border-bottom-color: $very-light-grey2;
    }

    &:hover::before {
        border-width: 1px;
        border-color: $barney-purple;
    }

    &::after {
        border-color: $barney-purple;
    }
}

.phone-code-select {
    .MuiPaper-rounded.MuiPaper-rounded {
        border-radius: 12px;
        max-height: 300px;
    }
}

@media only screen and (max-width: $sm) {
    .phone-verification {
        &__list-item:last-child {
            margin-top: 16px;
        }
    }
}