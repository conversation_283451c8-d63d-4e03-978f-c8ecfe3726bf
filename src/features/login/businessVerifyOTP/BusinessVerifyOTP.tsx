import React, { useEffect, useRef, useState } from 'react';
import styles from './BusinessVerifyOTP.module.scss';
import { FormControl } from '@mui/material';
import OTPInput from '@features/common/otpInputField/otpInputField';
import Image from 'next/image';
import getConfig from 'next/config';
import HelpText from '@features/signinSignup/common/helpText/HelpText';
import { useTranslation } from 'next-i18next';
import Button from '@features/common/button/Button';
import IntervalTimer from './intervalTimer/IntervalTimer';
import useAppRouter from '@features/common/router.context';
import useAuthAPI from '../authAPI';
import { useRouter } from 'next/router';

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const BusinessVerifyOTP = () => {
    const [otpValue, setOtpValue] = useState('');
    const [buttonDisabled, setButtonDisabled] = useState<boolean>(true);
    const [throttledUpto, setThrottledUpto] = useState(60);
    const { t } = useTranslation('common');
    const verifyButtonRef: any = useRef(null);
    const { generateAtworkOtp } = useAuthAPI();
    const routerNext = useRouter();
    const email = routerNext.query.email as string | undefined;

    const {
        state: { locale, region, activeRegion },
        router,
    } = useAppRouter();

    // otp status
    const [otpStatus, setOtpStatus] = useState<
        'invalid' | 'valid' | 'none' | 'expired'
    >('none');

    const [processState, setProcessState] = useState<'inProcess' | 'complete'>(
        'complete'
    );

    const inputOnChange = (otpCode: any) => {
        setOtpValue(otpCode);
        clearOtpErrorMsg();
        const re = /^[0-9\b]+$/;

        // if value is not blank, then test the regex

        if (otpCode === '' || re.test(otpCode)) {
            setOtpValue(otpCode);
            if (otpCode.length == 6) {
                setButtonDisabled(false);
                // handleConfirmOtp();
            } else {
                setButtonDisabled(true);
            }
        } else {
            setOtpValue('');
        }
    };

    const clearOtpErrorMsg = () => {
        setOtpStatus('none');
    };
    const hasOTPError = otpStatus === 'invalid' || otpStatus === 'expired';

    const handleConfirmOtp = () => {};

    const getErrorMessage = () => {
        if (otpStatus == 'invalid') {
            return t('invalidOTP');
        } else if (otpStatus == 'expired') {
            return t('expiredOTP');
        } else {
            return '';
        }
    };

    const handleResendOtp = async () => {
        setOtpStatus('none');
        setThrottledUpto(60);
        await generateOtp();
    };

    const generateOtp = async () => {
        if (!email) return;
        const variables = {
            input: {
                email: email,
                countryCode: 'AE',
                languageCode: locale,
            },
        };
        const response = await generateAtworkOtp(variables);
        return response;
    };


    return (
        <div className={styles['verify-otp']}>
            <h3>Enter verification code sent to your email</h3>

            <div className={styles['verify-otp__list']}>
                <ul className={styles['verify-otp__list']}>
                    <li className={styles['verify-otp__list-item']}>
                        <FormControl variant="standard" fullWidth={true}>
                            <OTPInput
                                value={otpValue}
                                onChange={inputOnChange}
                                numInputs={6}
                                renderInput={(props) => <input {...props} />}
                                containerStyle={
                                    styles['verify-otp__list-item__input-field']
                                }
                                inputStyle={
                                    hasOTPError
                                        ? 'otp-input-field-error'
                                        : 'otp-input-field'
                                }
                                shouldAutoFocus={true}
                                inputType={'number'}
                            />
                        </FormControl>
                    </li>
                    {hasOTPError && (
                        <p className={` ${styles['verify-otp__invalid-otp']}`}>
                            {getErrorMessage()}
                        </p>
                    )}
                    <li
                        className={`button-container ${styles['verify-otp__list-item']} ${styles['verify-otp__verify-btn']}`}
                    >
                        <Button
                            buttonRef={verifyButtonRef}
                            theme="purple"
                            action={handleConfirmOtp}
                            className={`submit-button ${
                                styles['continue-button']
                            }
								${processState === 'inProcess' ? `button-submitted` : ''}
                                `}
                            attribues={{
                                disabled: buttonDisabled,
                                type: 'button',
                            }}
                        >
                            {t('verify')}
                        </Button>
                    </li>
                </ul>

                <div className={styles['otp-resend']}>
                    {throttledUpto !== 0 ? (
                        <IntervalTimer
                            onComplete={() => {
                                setThrottledUpto(0);
                            }}
                            startingValue={throttledUpto}
                        />
                    ) : (
                        <p
                            className={`${styles['resend-btn']}`}
                            onClick={handleResendOtp}
                        >
                            {t('resendOtp')}
                        </p>
                    )}
                </div>

                <div className={styles['trouble-signin-container']}>
                    <div className={styles['trouble-signin']}>
                        <Image
                            src={`${imageBaseUrl}/icons/info-circles.svg`}
                            alt="Trouble Sign In"
                            width={16}
                            height={16}
                        />
                        <span className={styles['content']}>
                            {t('troubleSignIn')}
                        </span>
                    </div>
                    <HelpText />
                </div>
            </div>
        </div>
    );
};

export default BusinessVerifyOTP;
