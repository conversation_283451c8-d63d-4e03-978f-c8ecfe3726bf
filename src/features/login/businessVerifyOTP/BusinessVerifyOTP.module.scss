@import '@styles/variables';
@import '@styles/mixins';

.verify-otp {
    h3 {
        margin: 24px 0;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px;
        letter-spacing: -0.16px;
    }
    &__list {
        list-style: none;
        padding: 0;
        margin-bottom: 8px;

        &-item {
            padding-bottom: 0 !important;

            .phone-number-otp {
                margin-top: 10px;

                input {
                    margin-top: 10px;
                    font-size: 16px;
                    font-weight: 500;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    text-align: left;
                    color: $dark-purple;

                    @include rtl-styles {
                        margin: 0;
                        text-align: right;
                    }
                }

                input:focus {
                    @include animation('anime 0.01s');
                }
            }
            &__input-field {
                justify-content: space-between;
                display: flex;
                padding-bottom: 32px;
                gap: 16px;

                @include rtl-styles {
                    direction: ltr;
                }

                input {
                    margin: 0;
                    font-size: 32px;
                    font-weight: 800;
                    line-height: 40px;
                    letter-spacing: -0.16px;
                    caret-color: transparent;

                    &:focus {
                        border: 2px solid $dark-charcoal;
                    }
                }
            }
        }
    }

    &__resend-container {
        display: flex;
        justify-content: space-between;
        margin-top: 8px;

        @include rtl-styles {
            gap: 10px;
        }

        &__resend-otp {
            display: flex;
            margin: 0;
            // margin-top: 12px !important;

            @include font-size(16);

            &__timer-text {
                color: $dark-purple;
                margin: 0;
                font-size: 12px;
                font-weight: 600;
                line-height: 16px;
                letter-spacing: -0.12px;
                font-family: $mona-sans-font-family;

                @include rtl-styles {
                    font-family: $arabic-font-family;
                }
            }

            span {
                color: #000;
                margin-left: 3px;
                font-size: 12px;
                font-weight: 600;
                line-height: 16px;
                letter-spacing: -0.12px;
                font-family: $mona-sans-font-family;

                @include rtl-styles {
                    font-family: $arabic-font-family;
                    margin-left: 0;
                    margin-right: 3px;
                }
            }

            &__resend-sms {
                color: $dark-purple;
                cursor: pointer;
                margin: 0;
                font-family: $mona-sans-font-family;
                font-size: 14px;
                font-weight: 600;
                line-height: 18px;
                letter-spacing: -0.14px;

                @include rtl-styles {
                    font-family: $arabic-font-family;
                }
            }
        }

        &__whatsapp {
            display: flex;
            align-items: center;
            cursor: pointer;

            @include rtl-styles {
                align-items: flex-start;
            }

            img {
                margin-right: 8px;
                height: 20px;

                @include rtl-styles {
                    margin-right: 0;
                    margin-left: 8px;
                    margin-top: 6px;
                }
            }

            p {
                color: $semi-dark-purple;
                margin: 0;
                font-family: $mona-sans-font-family;
                font-weight: 600;
                line-height: 18px;
                letter-spacing: -0.14px;

                @include rtl-styles {
                    font-family: $arabic-font-family;
                }

                @include font-size(14);
            }
        }
    }

    &__resend-container-single {
        justify-content: center;
    }

    &__time-left {
        line-height: 27px;
        text-align: center;
        margin-bottom: 0;
        @include font-size(14);

        span,
        a {
            color: $barney-purple;
            cursor: pointer;
        }

        span {
            display: inline-block;
            width: 45px;
        }
    }

    &__check-spam {
        text-align: center;
        margin: 0;
        color: $black-header;
    }
    &__invalid-otp {
        color: $red-secondary;
        font-size: 14px;
        margin: 0;
        margin-top: -24px;
        margin-bottom: 32px;
    }

    &__devider {
        height: 0.5px;
        background-color: $white;
        margin-top: 32px;
    }

    &__help-container {
        padding-top: 24px;
    }

    &__verify-btn {
        // margin-bottom: 12px !important;
    }
}

@include keyframes(anime) {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@media only screen and (max-width: $sm) {
    .trouble-signin-container {
        flex-direction: column;

        .trouble-signin {
            justify-content: center !important;
        }
    }
}

@media only screen and (max-height: 650px) {
    .trouble-signin-container {
        margin-top: 0px !important;
    }
}
.trouble-signin-container {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;

    .trouble-signin {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        border-radius: 120px;
        height: 18px;
        align-items: center;
        width: 100%;

        // @include rtl-styles{
        //     width: 280px;
        // }

        img {
            margin-right: 4px;
            height: 16px;
            width: 16px;
            object-fit: cover;
            @include rtl-styles {
                margin-left: 4px;
                margin-right: unset;
            }
        }

        .content {
            font-family: 'Mona Sans';
            font-size: 12px;
            font-weight: 500;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: -0.12px;
            line-height: 18px; /* 150% */
            text-align: left;
            color: $dark-purple;
            cursor: pointer;

            @include rtl-styles {
                font-family: $arabic-font-family !important;
                text-align: right;
            }
        }
    }
}

.otp-resend{
    display: flex;
    justify-content: center;
    margin: 0;

    p{
        margin: 0;
        cursor: pointer;
    }
}