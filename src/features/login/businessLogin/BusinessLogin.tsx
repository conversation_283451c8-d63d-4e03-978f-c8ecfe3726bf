import React from 'react';
import styles from './BusinessLogin.module.scss';
import useAppRouter from '@features/common/router.context';
import { SIGN_IN_SLUGS } from '@constants/common';
import { useTranslation } from 'next-i18next';

const BusinessLogin = () => {
    const {
        state: { locale },
        router,
    } = useAppRouter();

    const { t } = useTranslation('common');
    const handleRedirection = () => {
        router.push(
            `?slug=${SIGN_IN_SLUGS.BUSINESS_LOGIN}`,
            `${SIGN_IN_SLUGS.BUSINESS_LOGIN}`,
            { locale, shallow: true }
        );
    };
    return (
        <div className={styles['business-login']}>
            <div className={styles['title-container']}>
                <div className={styles['title-container__line']}></div>
                <span>
                    <h3>{t('businessUser')}</h3>
                </span>
            </div>
            <button onClick={handleRedirection}>
                {t('logintoYourAccount')}
            </button>
        </div>
    );
};

export default BusinessLogin;
