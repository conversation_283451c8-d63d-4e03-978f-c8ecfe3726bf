@import '@styles/variables';
@import '@styles/mixins';

.business-login {
    border-radius: 24px;
    border: 1px solid rgba(153, 198, 255, 0.2);
    background: #fff;
    padding: 32px;
    width: 100%;
    margin-top: 24px;
    margin-bottom: 100px;

    .title-container {
        position: relative;
        text-align: center;
        height: 32px;
        display: flex;
        align-items: center;
        margin-bottom: 24px;

        &__line {
            height: 1px;
            width: 100%;
            background: #f5f5f5;
        }
        span {
            z-index: 2;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 190px;
            background-color: #fff;

            h3 {
                font-family: $default-font-family;
                font-optical-sizing: none;
                font-size: 24px;
                font-style: normal;
                font-weight: 800;
                line-height: 32px;
                margin: 0;
                background: linear-gradient(90deg, #0071ff 0%, #99c6ff 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }
    }
    button {
        cursor: pointer;
        width: 100%;
        height: 66px;
        padding: 8px 8px 8px 16px;
        background-color: #fff;
        border-radius: 12px;
        border: 3px solid #99c6ff;
        font-family: $mona-sans-font-family;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px; /* 150% */
        letter-spacing: -0.16px;

        @include rtl-styles {
            font-family: $arabic-font-family;
        }
    }
}
