import React, { useRef, useState, useEffect } from 'react';
import styles from '../businessEmail/BusinessEmail.module.scss';
import { useTranslation } from 'next-i18next';
import Image from 'next/image';
import {
    Checkbox,
    FormControl,
    FormControlLabel,
    FormHelperText,
    TextField,
} from '@mui/material';
import Button from '@features/common/button/Button';
import { PLATFORM_TYPE, SIGN_IN_SLUGS } from '@constants/common';
import HelpText from '@features/signinSignup/common/helpText/HelpText';
import getConfig from 'next/config';
import useAppRouter from '@features/common/router.context';
import BusinessVerifyOTP from '../businessVerifyOTP/BusinessVerifyOTP';

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const RewardsLogin = () => {
    const continueBtnRef: any = useRef();
    const { t } = useTranslation('common');

    const {
        state: { locale },
        router,
    } = useAppRouter();

    const [active, setActive] = useState<boolean>(false);

    const [processState, setProcessState] = useState<'inProcess' | 'complete'>(
        'complete'
    );
    const [emailErrorMessage, setEmailErrorMessage] = useState('');
    const [buttonName, setButtonName] = useState<any>(t('edit'));

    const handleFBLogin = () => {
        try {
            // setShowFbWarning(true);
        } catch (error) {}
    };

    const handleSubmit = () => {
        router.push(
            `?slug=${SIGN_IN_SLUGS.BUSINESS_LOGIN}`,
            `${SIGN_IN_SLUGS.BUSINESS_LOGIN}`,
            { locale, shallow: true }
        );
    };

    return (
        <div className={styles['signin-email']}>
            <div className={styles['header']}>
                <img src={`${imageBaseUrl}/icons/atwork-logo.svg`} alt="" />
                <h5>Enter OTP</h5>
            </div>

            <div
                className={`${styles['email-input-button']} ${
                    emailErrorMessage ? 'error-outline' : ''
                }`}
            >
                <div className={`${styles['email-input-button__input']}`}>
                    <Image
                        width={24}
                        height={24}
                        alt="email"
                        src={`${imageBaseUrl}/icons/sms.svg`}
                    />
                    <FormControl variant="standard" fullWidth={true}>
                        <TextField
                            autoFocus={true}
                            name="email"
                            id="email"
                            type="text"
                            variant="standard"
                            placeholder="<EMAIL>"
                            inputProps={{ maxLength: 150 }}
                            disabled
                        />
                    </FormControl>
                </div>
                <div
                    className={`button-container-email ${styles['button-container-email']}`}
                >
                    <Button
                        id="btnSubmit"
                        theme="purple"
                        attribues={{ ref: continueBtnRef }}
                        action={handleSubmit}
                        loadingState={processState}
                        className={`submit-button 
${processState === 'inProcess' ? `button-submitted` : ''} ${
                            styles['email-input-button__button']
                        }`}
                    >
                        {buttonName}
                    </Button>
                </div>
            </div>

            <BusinessVerifyOTP />
        </div>
    );
};

export default RewardsLogin;
