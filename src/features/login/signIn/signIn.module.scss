@import '@styles/variables';
@import '@styles/mixins';

.signin-mobile {
    display: flex;
    flex-direction: column;
}

.separator {
    color: $barney-purple;
    text-align: center;
    position: relative;
    margin-top: 24px;
    @include font-size(16);

    span {
        display: inline-block;
        padding: 0 10px;
        background-color: $white;
        position: relative;
        z-index: 2;
    }

    &::before {
        content: '';
        height: 1px;
        background-color: $cool-grey;
        position: absolute;
        left: 0;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
    }
}

.third-party-login-methods {
    display: flex;
    gap: 24px;
    position: relative;
    margin-top: 16px;
    margin-bottom: 24px;
    justify-content: center;

    &__social{
        display: flex;
        gap: 24px;

        @media only screen and (max-width: $sm) {
            gap: 16px;
            margin-bottom: 40px;
            }
       
    }

    a {
        width: 70px;
        height: 70px;
        padding: 0px 16px;
        border-radius: 12px;
        background: #F5F5F5;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        &:hover {
            img {
                transform: scale(1.1);
                transform-origin: center center;
                transition: transform ease 150ms;
            }
        }

        @media only screen and (max-width: $sm) {
            width: 50px;
            height: 50px;
            padding: 5px;
            border-radius: 8px;
        }
        
    }
    @media only screen and (max-width: $sm) {
        gap: 16px;
        }
}

@media only screen and (max-width: $sm) {

    .third-party-login-methods {
        
        &--mweb{
            flex-direction: column;
        align-items: center;
        gap: 20px;
        }
        &__social{
            display: flex;
            
        }
    }

    .continue-as-guest-container{
        width: 100%;
    }
    
        

    .signin-mobile {
        h3 {
            margin: 0 0 24px;
        }
    }

    .separator {
        font-size: 14px;
        font-weight: 500;
    }
}

.third-party-login-methods--mweb {
    display: flex;
    justify-content: space-evenly;
    margin-top: 20px;
    position: relative;
    margin-bottom: 24px;

    a {
        width: 72.6px;
        height: 70.9px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: white;
        border-radius: 12px;
        cursor: pointer;

        &:hover {
            img {
                transform: scale(1.1);
                transform-origin: center center;
                transition: transform ease 150ms;
            }
        }
        &.apple {
            
            img {
                width: 24.7px;
                height: 30.6px;
            }
        }
        img {
            width: 30.6px;
            height: 30.6px;
        }
    }
}

@media only screen and (max-width: $sm) {
    .signin-mobile {
        h3 {
            margin: 0 0 24px;
        }
    }

    .separator {
        font-size: 14px;
        font-weight: 500;
    }
}

.create-account {
    height: 50px;
    flex-grow: 0;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin: 16px 0.3px 0 0;
    padding: 24px;
    border-radius: 12px;
    background-color: #fff;
    a {
        cursor: pointer;
    }
}

.Not-registered-Create-an-account {
    width: 278px;
    height: 24px;
    flex-grow: 0;
    font-family: Poppins;
    font-size: 16px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.5;
    letter-spacing: normal;
    text-align: center;
    color: #b800c4;
    @include rtl-styles {
        font-family: $arabic-font-family;
        font-weight: normal;
    }
}

.Not-registered-Create-an-account .text-style-1 {
    font-weight: normal;
    color: var(--warm-grey);
    @include rtl-styles {
        font-family: $arabic-font-family;
        font-weight: normal;
    }
}

.or-use-div{
    margin-top: 16px;
    padding: 8px 16px;
    border-radius: 50px;
    height: 40px;
    display: flex;
    justify-content: center;
    position: relative;

    

    .or-use {
        color: var(--Black-Primary---Black, #0E0F0C);
        text-align: center;
        /* Body/B2 - Bold */
        font-family: "Mona Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 18px; /* 150% */
        letter-spacing: -0.14px;
        font-style: normal;
        background: $white;
        z-index: 2;
        width: 160px;
        padding: 0 16px;

        @media only screen and (max-width: $sm) {
            font-size: 14px;
         }
        

        @include rtl-styles {
            font-family: $arabic-font-family;
            font-weight: normal;
        }
    }

    &__line {
        width: 100%;
        height: .5px;
        background-color: #F5F5F5;
        position: absolute;
        top: 17px;
    }

}

.continue-as-guest-container{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0px 0 0px;
    padding: 16px;
    cursor: pointer;
    flex: 1;
    border-radius: 12px;
    background: #F5F5F5;

    .icon-wrapper {
        transition: all 0.4s ease;

        @include rtl-styles{
            transform: rotate(180deg);
        }
    }

    

    &:hover {
        // span{
        //     color: $white;
        // }
        
        // background-color: $black;
        // transition: background 0.5s ease, color 0.5s ease;
        .icon-wrapper {
          transform: translateX(4px);
          transition: all 0.4s ease;

          @include rtl-styles{
            transform: rotate(180deg) translateX(4px);
          }
        }
      }

    @media only screen and (max-width: $sm) {
        // background: rgba(255, 255, 255, 0.80);
        height: 50px;
    }
    

}

@media only screen and (max-width: $sm) {
    .trouble-signin-container{
        flex-direction: column;

        .trouble-signin{
            justify-content: center !important;
        }
    }
}

@media only screen and (max-height: 650px){
    .trouble-signin-container{
        margin-top: 0px !important;
    }
}

.trouble-signin-container{
    
    display: flex;
    justify-content: space-between;
    // margin-top: 32px;

    .trouble-signin{
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        border-radius: 120px;
        height: 18px;
        align-items: center;
        width: 100%;

        // @include rtl-styles{
        //     width: 280px;
        // }

        img{
            margin-right: 4px;
            height: 16px;
            width: 16px;
            object-fit: cover;
            @include rtl-styles{
                margin-left: 4px;
                margin-right: unset;
            }
        }

        .content{
            font-family: "Mona Sans";
            font-size: 12px;
            font-weight: 500;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: -0.12px;
            line-height: 18px; /* 150% */
            text-align: left;
            color: $dark-purple;
            cursor: pointer;

            @include rtl-styles {
                font-family: $arabic-font-family !important;
                text-align: right;
            }
        }
    }
}


.continue-as-guest{
    color: #0E0F0C;
    text-align: right;
    /* Other/Text - Button */
    font-family: "Mona Sans";
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: var(--Font-Line-height-lg, 24px); /* 150% */
    letter-spacing: -0.16px;

    @include rtl-styles{
        font-family: $arabic-font-family;
    }

}

.disabled{
    opacity: 0.3;
    cursor: default !important;
}
.info-icon{
    height: 20px !important;
    width: 20px !important;
}

.info-icon-container{
    cursor: pointer;
    position: absolute;
    top: 0px;
    right: -15px;
    border-radius: 40px;
    background-color: #f2f5f8;
    height: 30px;
    width: 30px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    @include rtl-styles{
        right: -16px;
        top: -1px;
    }

    @media (max-width: ($sm)) {
        top: -29px;
        right: -25px;

        @include rtl-styles{
            top: -29px;
            right: -25px;
        }
    }
}

.fb-container{
    position: relative;
    cursor: default;
}

