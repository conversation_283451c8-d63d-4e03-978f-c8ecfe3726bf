import { useTranslation } from 'next-i18next';
import getConfig from 'next/config';
import styles from './signIn.module.scss';
import { CognitoHostedUIIdentityProvider } from '@aws-amplify/auth';
import * as CognitoAPI from '@features/common/signInSignUpAPI';
import { useEffect, useState } from 'react';
import Loader from '@features/common/loader/Loader';
import useAppRouter from '@features/common/router.context';
import { SEVERITY } from '@constants/messageTypes';
import {
    COGNITO_USER_TYPE,
    PAGE_FLOW,
    PLATFORM_TYPE,
    SIGN_IN_SLUGS,
} from '@constants/common';
import ResponseInfoHOC from '@features/signinSignup/common/responseInfoHOC/ResponseInfoHOC';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import {
    getPageFlow,
    getPrefillInfo,
    getProvider,
    setDefaultState,
    setPageFlow,
    setPrefillInfo,
} from '../loginFlowSlice';
import VerifyOTP from '../verifyOTP/VerifyOTP';
import Image from 'next/image';
import * as Sentry from '@sentry/nextjs';
import EmailLogin from '../emailLogin/EmailLogin';
import Confirm from '@features/common/confirm/Confirm';
import { updatePrefillStatus } from '@features/common/signInSignUpAPI';
import cloneDeep from 'lodash/cloneDeep';
import { UserType } from '../loginFlowSlice';
import { useRouter } from 'next/router';
import FbWarning from './fbWarning/fbWarning';
import { reduceEachTrailingCommentRange } from 'typescript';
import TitleHeader from '../titleHeader/TitleHeader';
import HelpText from '@features/signinSignup/common/helpText/HelpText';

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const SignIn = ({
    showMessage,
    platformType,
    isOtpScreen,
    setOtpScreen,
    prefillInfo,
    guestSessionExists,
    isGuestEnabled,
    recaptchaLoaded,
    reCaptchaRef,
    logo
}: {
    showMessage: (
        message: string,
        severity: SEVERITY,
        heading?: string | null
    ) => void;
    platformType: string;
    isOtpScreen: boolean;
    setOtpScreen: any;
    prefillInfo: any;
    guestSessionExists: boolean;
    isGuestEnabled: boolean;
    recaptchaLoaded: boolean;
    reCaptchaRef: any;
    logo: any;
}) => {
    // translations

    const pageFlow = useAppSelector(getPageFlow);

    const {
        state: { locale },
        router,
    } = useAppRouter();

    // #. Get the query  info from the router
    const { query } = useRouter();
    const isGuest = query?.guest == 'true';
    const isSpecialBrand = query?.['special-brand'] == 'true';

    const { t } = useTranslation('common');
    const dispatch = useAppDispatch();

    const [showLoader, setShowLoader] = useState<boolean>(false);
    const [showEmailConfirmation, setShowEmailConfirmation] = useState(false);
    const [confirmStatus, setConfirmStatus] = useState<any>(null);
    const [prefillMessage, setPrefillMessage] = useState('');
    const [prefilShow, setPrefilShow] = useState(prefillInfo.prefillRequired);
    const [prefillConfirmTitle, setPrefillConfirmTitle] = useState('');
    const [openConfirm, setOpenConfirm] = useState<boolean>(false);
    const [showFbWarning, setShowFbWarning] = useState<boolean>(false);

    useEffect(() => {
        if (prefillInfo) {
            if (prefillInfo.prefillUserType == UserType.AppleUser) {
                setPrefillConfirmTitle(t('appleAuth') || '');
            } else if (
                prefillInfo.prefillUserType == UserType.ConventionalUser
            ) {
                setPrefillConfirmTitle(t('existingEmailAddressFound') || '');
            }
        }
    }, [prefillInfo]);

    const federatedSignIn = async (provider: any, authMethod: string) => {
        try {
            const authSession = localStorage.getItem('authSession') || '';

            setShowLoader(true);
            setTimeout(() => {
                setShowLoader(false);
            }, 2000);
            localStorage.setItem('pageFlow', pageFlow);
            localStorage.setItem('authMethod', authMethod);
            let response = await CognitoAPI.federatedSignIn(provider);
        } catch (error) {
            Sentry.captureException(error);
            console.log('Error occured in method federatedSignIn ', error);
        }
    };

    const handleOnConfirm = async (confirm: boolean) => {
        try {
            setShowLoader(true);
            await updatePrefillStatus(prefillInfo.authCode);
            let prefillData = cloneDeep(prefillInfo);
            prefillData.prefillRequired = false;
            setPrefillInfo((prevState: any) => ({
                ...prevState,
                prefillRequired: false,
            }));
            setPrefilShow(false);
            setShowEmailConfirmation(false);
            setConfirmStatus(confirm);
            setShowLoader(false);
        } catch (error) {
            console.log('Error in handleOnConfirm ', error);
            setShowLoader(false);
        }
    };

    const handleContinueAsGuest = () => {
        try {
            if (isSpecialBrand == true) {
                setOpenConfirm(true);
                return;
            }
            dispatch(setPageFlow(PAGE_FLOW.GUEST));
            router.push(
                `?slug=${SIGN_IN_SLUGS.ADD_INFO}`,
                `${SIGN_IN_SLUGS.ADD_INFO}`,
                { locale, shallow: true }
            );
        } catch (error) {}
    };

    const handleFBLogin = () => {
        try{
            setShowFbWarning(true);
        } catch(error){

        }
    }

    return (
        <>
            {isOtpScreen ? (
                <VerifyOTP logo={logo} />
            ) : (
                <div className={styles['signin-mobile']}>
                    {/* <h3>{t('loginOrSignUp')}</h3> */}
                    <TitleHeader title={t('welcome')} subTitle={t('loginOrSignUp')} logo={logo} platformType={platformType} />
                    <EmailLogin
                        platformType={platformType}
                        setShowEmailConfirmation={setShowEmailConfirmation}
                        confirmStatus={confirmStatus}
                        setPrefillMessage={setPrefillMessage}
                        federatedSignIn={federatedSignIn}
                        prefillInfo={prefillInfo}
                        prefilShow={prefilShow}
                        recaptchaLoaded={recaptchaLoaded}
                        reCaptchaRef={reCaptchaRef}
                    />
                    <div className={styles['or-use-div']}>
                    <div className={styles['or-use-div__line']}>
                        </div>
                        <span className={styles['or-use']}>
                            {t('orContinueWith')}
                        </span>

                    </div>
                    <div
                        className={styles['third-party-login-methods']}
                    >
                        <div className={styles['third-party-login-methods__social']}>
                        <a
                            onClick={() =>
                                federatedSignIn(
                                    {
                                        provider:
                                            CognitoHostedUIIdentityProvider.Google,
                                    },
                                    'Google'
                                )
                            }
                        >
                            <Image
                                src={`${imageBaseUrl}/icons/google.svg`}
                                alt="Google"
                                width={40}
                                height={40}
                            />
                        </a>
                        <a
                            className={styles['apple']}
                            onClick={() =>
                                federatedSignIn(
                                    {
                                        provider:
                                            CognitoHostedUIIdentityProvider.Apple,
                                    },
                                    'Apple'
                                )
                            }
                        >
                            <Image
                                src={`${imageBaseUrl}/icons/apple.svg`}
                                alt="Apple"
                                width={40}
                                height={40}
                            />
                        </a>
                        </div>
                        
                        

                        {platformType !== PLATFORM_TYPE.APP.toLowerCase() &&
                        isGuestEnabled &&
                        !guestSessionExists && !isGuest && (
                            <div
                                onClick={handleContinueAsGuest}
                                className={
                                    styles['continue-as-guest-container']
                                }
                            >
                                <span className={styles['continue-as-guest']}>
                                    {t('guestuser')}
                                </span>
                                <Image className={styles['icon-wrapper']} width={32} height={32} src={`${imageBaseUrl}/images/arrow-right.svg`} alt='guest user' />
                            </div>
                        )}
                        
                    </div>
                    {/* <div className={'white-divider'}></div> */}
                    
                        <div className={styles['trouble-signin-container']}>
                            <div className={styles['trouble-signin']}>
                                <Image
                                    src={`${imageBaseUrl}/icons/info-circles.svg`}
                                    alt="Trouble Sign In"
                                    width={16}
                                    height={16}
                                />
                                <span className={styles['content']}  onClick={handleFBLogin}>
                                    {t('troubleSignIn')}
                                </span>
                            </div>
                            {platformType !== PLATFORM_TYPE.APP.toLowerCase() && (
                                <HelpText />
                            )}

                        </div>

                    

                    {showLoader && <Loader />}
                </div>
            )}
            <Confirm
                open={showEmailConfirmation}
                onClose={handleOnConfirm}
                message={prefillMessage}
                title={prefillConfirmTitle}
                className='existing-email-dialog'
            ></Confirm>
            <Confirm
                className="login-require-confirm"
                title={t('loginRequired') || ''}
                message={t('loginGuestUserMessageSpecialBrands')}
                open={openConfirm}
                onClose={() => setOpenConfirm(false)}
                confirmText={t('ok') || ''}
                showCancel={false}
            />
            <FbWarning open={showFbWarning} platformType={platformType} onClose={setShowFbWarning} />
        </>
    );
};

export default ResponseInfoHOC(SignIn);
