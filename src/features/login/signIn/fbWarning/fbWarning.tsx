import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Dialog from '@mui/material/Dialog';
import { useTranslation } from 'next-i18next';
import styles from './fbWarning.module.scss';
import Button from '../../../common/button/Button';
import getConfig from 'next/config';
import { PLATFORM_TYPE } from '@constants/common';
import { SxProps } from '@mui/material';

interface FbWarningDialogProps {
    open: boolean;
    onClose: any;
    platformType: string;
}

const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

/**
 * @method Confirm
 * @description A common component usding as a confirmation dialog
 * @param param0
 * @returns
 */
const FbWarning = ({ open, onClose, platformType }: FbWarningDialogProps) => {
    // #. Get translations
    const { t } = useTranslation('common');

    const onCloseClick = (isConfirm: boolean = false) => {
        onClose && onClose(isConfirm);
    };

    const handleBackDropClick = () => {
        onClose && onClose(false);
    };

    const sx: SxProps = {
        '& .MuiDialog-container': {
            alignItems: 'flex-end',
        },
    };

    return (
        <Dialog
            open={open}
            className={`fb-warning-dialog ${styles['fb-warning-dialog']}`}
            onClose={handleBackDropClick}
            sx={platformType !== PLATFORM_TYPE.WEB.toLowerCase() ? sx : {}}
        >
            <div
                className={`fb-warning-dialog-container ${styles['fb-warning-dialog-container']}`}
            >
                <div className={`${styles['fb-warning-dialog-close-btn']}`}>
                    <img
                        src={`${imageBaseUrl}/icons/close-circle.svg`}
                        onClick={() => onCloseClick()}
                    />
                </div>
                <span className={`${styles['fb-warning-dialog-title']}`}>
                    {t('importantNotice')}
                </span>

                <span className={`${styles['fb-warning-dialog-login-create']}`}>
                    {t('facebookLogin')}
                </span>

                <span className={`${styles['fb-warning-dialog-login-create']}`}>
                    {t('ifYouAreRegistered')}
                </span>

                <span className={`${styles['fb-warning-dialog-help']}`}>
                    {t('inCaseYouNeedHelp')}
                </span>

                <div className={`${styles['fb-warning-dialog-devider']}`}></div>

                <span className={`${styles['fb-warning-dialog-help-center']}`}>
                    {t('helpcenter')}
                </span>

                <div
                    className={`${styles['fb-warning-dialog-country-phone-container']} ${styles['contact-list']}`}
                >
                    <div
                        className={`${styles['fb-warning-dialog-country-phone-container__items']}`}
                    >
                        <span
                            className={`${styles['fb-warning-dialog-country-phone-container__items--item']}`}
                        >
                            <a href="tel:600 54 2222">
                                <img src={`${imageBaseUrl}/icons/call-2.svg`} />
                            </a>
                            <span
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__country']}`}
                            >
                                UAE
                            </span>
                            <span
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__phone']}`}
                            >
                                &nbsp;600 54 2222
                            </span>
                            &nbsp;&nbsp;
                            <div
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__right-cont']}`}
                            >
                                <span
                                    className={`${styles['fb-warning-dialog-country-phone-container__items--item__everyday']}`}
                                >
                                    {t('everyday')}
                                </span>{' '}
                                9AM - 10PM
                            </div>
                        </span>
                    </div>
                    <div
                        className={`${styles['fb-warning-dialog-country-phone-container__items']}`}
                    >
                        <span
                            className={`${styles['fb-warning-dialog-country-phone-container__items--item']}`}
                        >
                            <a href="tel:+966920000175">
                                <img src={`${imageBaseUrl}/icons/call-2.svg`} />
                            </a>
                            <span
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__country']}`}
                            >
                                KSA
                            </span>
                            <span
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__phone']}`}
                            >
                                &nbsp;+966 9200 00175
                            </span>
                            &nbsp;&nbsp;
                            <div
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__right-cont']}`}
                            >
                                <span
                                    className={`${styles['fb-warning-dialog-country-phone-container__items--item__everyday']}`}
                                >
                                    {t('everyday')}
                                </span>{' '}
                                9AM - 9PM
                            </div>
                        </span>
                    </div>
                    <div
                        className={`${styles['fb-warning-dialog-country-phone-container__items']}`}
                    >
                        <span
                            className={`${styles['fb-warning-dialog-country-phone-container__items--item']}`}
                        >
                            <a href="tel:+96522204519">
                                <img src={`${imageBaseUrl}/icons/call-2.svg`} />
                            </a>
                            <span
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__country']}`}
                            >
                                Kuwait
                            </span>
                            <span
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__phone']}`}
                            >
                                &nbsp;+965 22204519
                            </span>
                            &nbsp;&nbsp;
                            <div
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__right-cont']}`}
                            >
                                <span
                                    className={`${styles['fb-warning-dialog-country-phone-container__items--item__everyday']}`}
                                >
                                    {t('everyday')}
                                </span>{' '}
                                9AM - 9PM
                            </div>
                        </span>
                    </div>
                </div>
                <div
                    className={`${styles['fb-warning-dialog-country-phone-container']} ${styles['help-email']}`}
                >
                    <div
                        className={`${styles['fb-warning-dialog-country-phone-container__items']} ${styles['fb-warning-dialog-country-phone-container__items--email']}`}
                    >
                        <div
                            className={`${styles['fb-warning-dialog-country-phone-container__items--item']} ${styles['email']}`}
                        >
                            <a href="mailto:<EMAIL>" className={styles['mail-icon']}>
                                <img
                                    src={`${imageBaseUrl}/icons/sms.svg`}
                                    className={
                                        styles[
                                            'fb-warning-dialog-country-phone-container__items--item__email-icon'
                                        ]
                                    }
                                />
                            </a>
                            <span
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__emailus']}`}
                            ></span>

                            <span
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__email']}`}
                            ></span>
                            <div
                                className={`${styles['fb-warning-dialog-country-phone-container__items--item__email-id']}`}
                            >
                                &nbsp;<EMAIL>
                            </div>
                        </div>
                    </div>
                </div>

                {platformType !== PLATFORM_TYPE.WEB.toLowerCase() && (
                    <DialogActions
                        className={`fb-warning-dialog-buttons ${styles['fb-warning-dialog-buttons']}`}
                    >
                        <Button
                           action={() => onCloseClick(false)}
                            theme="purple"
                            // borderTheme="purple"
                            className={`fb-warning-dialog-button-close ${styles['fb-warning-dialog-button-close']}`}
                        >
                            {t('okay')}
                        </Button>
                    </DialogActions>
                )}
            </div>
        </Dialog>
    );
};

export default FbWarning;
