@import '@styles/variables';
@import '/src/styles/mixins';

.fb-warning-dialog {
    &-close-btn {
        position: absolute;
        top: 24px;
        right: 24px;
        cursor: pointer;

        @include rtl-styles{
            left: 24px;
            right: unset;
        }

        @media (max-width: ($sm)) {
            display: none;
        }
    }
    &-container {
        width: 537px;
        height: auto;
        padding: 24px;
        display: flex;
        flex-direction: column;

        @media (max-width: ($sm)) {
            width: 100vw;
            margin-bottom: 70px;
            height: calc(100vh - 100px);
        }
    }

    &-title {
        @media (max-width: ($sm)) {
            flex-grow: 0;
            flex: 1;
        }

        color: $dark-charcoal;
        font-size: 24px;
        font-weight: 800;
        line-height: 32px;

        @include rtl-styles {
            text-align: right;
            font-family: $arabic-font-family;
        }
    }

    &-login-create {
        @media (max-width: ($sm)) {
            flex-grow: 0;
            flex: 1;
        }

        color: $dark-charcoal;
        font-family: $mona-sans-font-family;
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        letter-spacing: -0.14px;
        margin-top: 16px;

        @media (max-width: ($sm)) {
            font-size: 14px;
            margin-top: 16px;
        }

        @include rtl-styles {
            font-family: $arabic-font-family;
            text-align: right;
        }
    }

    &-help {
        width: max-content;
        @media (max-width: ($sm)) {
            flex-grow: 0;
            flex: 1;
            width: 100%;
        }

        margin-top: 16px;
        color: $dark-charcoal;
        font-family: $mona-sans-font-family;
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        letter-spacing: -0.14px;
        margin-top: 16px;

        @include rtl-styles {
            text-align: right;
            font-family: $arabic-font-family;

        }

        @media (max-width: ($sm)) {
            font-size: 14px;
            margin-bottom: 0;
            height: 31px;
        }
    }

    &-devider {
        width: 100%;
        height: .5px;
        background-color: #D9D9D9;
        margin: 10px 0;
    }

    &-help-center {
        @media (max-width: ($sm)) {
            flex-grow: 0;
            flex: 1;
            margin-bottom: 0;
        }

        color: $dark-charcoal;
        font-size: 24px;
        font-weight: 800;
        line-height: 32px;
        margin-bottom: 8px;

        @include rtl-styles {
            text-align: right;
            font-family: $arabic-font-family;

        }
    }

    &-country-phone-container {
        display: flex;
        flex-direction: column;
        margin-top: 8px;
        padding: 16px;
        gap: 16px;
        border-radius: 12px;
        background: #F5F5F5;



        @media (max-width: ($sm)) {
            flex-grow: 0;
            flex: 1;
        }

        @media (max-width: ($sm)) {
            margin-top: 10px;
            padding: 8px;
        }

        &__items {
            display: flex;
            flex-direction: row;
            flex: 1;
            align-items: anchor-center;

            @media (max-width: ($sm)) {
                height: 24px;
            }

            &--item {
                position: relative;
                color: #868785;
                font-weight: 500;
                font-family: $mona-sans-font-family;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                font-style: normal;
                line-height: 2;
                letter-spacing: normal;
                text-align: left;
                flex-grow: 1;
                height: 18px;

                @include rtl-styles {
                    font-family: $arabic-font-family;
                    text-align: right;
                    margin-right: 25px;
                }

                a {
                    position: absolute;
                    top: 3px;
                    left: -2px;

                    @include rtl-styles {
                        right: -30px;
                    }
                }

                img {
                    width: 16px;
                    height: 16px;
                    align-self: center;
                    flex-grow: 0;
                    margin: 2px 0.5px 0px 0.2px;
                    object-fit: contain;

                }

                &__country {
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 2;
                    color: $dark-purple;
                    margin-left: 30px;

                    @include rtl-styles {
                        display: inline-block;
                        margin-left: 10px;
                    }
                }

                &__emailus {
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 2;
                    color: $dark-purple;
                    margin-left: 30px;

                    @include rtl-styles {
                        display: inline-block;
                        margin-left: 0px;
                        margin-top: -4px;
                        font-family: $arabic-font-family;

                    }
                }

                &__email {
                    color: $barney-purple;
                    font-family: $mona-sans-font-family;
                    font-size: 14px;
                    font-weight: 600;
                    line-height: 18px;
                    letter-spacing: -0.14px;

                    &-icon {
                        margin-top: 2px !important;
                    }

                    &-id {
                        color: $dark-charcoal;
                        font-weight: 600;
                    }

                    @include rtl-styles {
                        font-family: $arabic-font-family;
                        display: inline-block;
                    }
                }

                &__phone {
                    color: $dark-charcoal;
                    font-size: 14px;
                    line-height: 2;

                    @include rtl-styles {
                        display: inline-block;
                        direction: ltr;
                    }
                }

                &__right-cont {

                    position: absolute;
                    right: 0;
                    top: 3px;

                    @include rtl-styles{
                        left: 0;
                        right: unset;
                    }

                    &__everyday {
                        @include rtl-styles {
                            font-family: $arabic-font-family;
                        }
                    }
                }
            }

            &--email {
                @media (max-width: ($sm)) {
                    margin-bottom: 0;
                }
            }
        }


    }

    &-buttons {
        padding-left: 0px !important;
        padding-right: 0px !important;
        margin-top: 40px;

        @media (max-width: ($sm)) {
            margin-top: 5px;
        }

        button {
            padding: 5px 15px;
        }
    }

    &-button-close {
        border-radius: 12px !important;
        height: 50px;
        width: 100%;
        background-color: $dark-charcoal;

        span {
            color: #fff;
            font-size: 16px;
            font-weight: 500;
        }

        @media only screen and (max-width: $sm) {
            width: 1005;
        }
    }

    &-button-confirm {
        border-radius: 8px !important;
        height: 50px;
        width: 182px;
        background-color: $semi-dark-purple;

        span {
            font-family: Poppins;
            font-size: 16px;
            font-weight: 500;
        }

        @include rtl-styles {
            margin-right: 8px;
        }

        @media only screen and (max-width: $sm) {
            width: 125px;
        }
    }

    &-button-fullsize {
        width: 100% !important;
    }

    .email {
        font-size: 16px !important;
        color: $dark-purple;
        display: flex;
        align-items: center;
    }
}

.help-email{
    height: 50px !important;
}

.contact-list{
    height: 118px !important;
}
.mail-icon{
    top: -4px !important;
}