export interface GenerateLoginOTPResponse {
    authLogin : {
        data : {
            statusCode : Number,
            message : string,
            sessionKey : string,
            mfaValues : {
                phoneNumber : string
            }
        },
        errors : []
    },
    offline : boolean
}

export interface AuthSetTokenResponse {
    authSetTokens: {
        data: {
            message?: string,
            accessToken: string,
            idToken?: string,
            authSignature: string,
            devicePlatform?: string
        },
        errors?: []
    }
}

export interface VerifyOTPResponse {
    offline : boolean,
    authLogin : {
        data : {
            statusCode : Number,
            message : string,
            sessionKey : string,
            mfaValues : {
                phoneNumber : string
            },
            authenticationResult : string
        },
        errors : []
    }
}

export interface CustomChallengeResponse {
    signInUserSession : {
        refreshToken : {
            token : string
        }
    },
    username : string
}

export interface SetRefreshTokenViaAPIResponse {
    data : {
        AccessToken : string,
        AuthSignature : string
    }
}

export interface SignUpResponse {
    authSignup : {
        data : {
            statusCode : Number,
            message : string,
            sessionKey : string,
            mfaValues : {
                phoneNumber : string
            }
        },
        errors : []
    },
    offline : boolean,
}

export interface VerifySignUpOTPResponse {
    offline : boolean,
    authSignup : {
        data : {
            statusCode : Number,
            message : string,
            sessionKey : string,
            mfaValues : {
                phoneNumber : string
            },
            authenticationResult : {
                accessToken : string
                idToken : string,
                refreshToken : string
            },
            extraAttributes : {
                authSignature : string
            }
        },
        errors : []
    }
}

export interface UserDetails {
    userDetails : {
        username: string,
        email: string,
        birthdate: string,
        name: string,
        emailVerified: any,
        phoneNumber: any,
        phoneNumberVerified: any,
        gender: any,
        picture: string,
        sub: any,
        apiTimeSpan: any,
        apiCallInterval: any,
        giftCount: any,
        hasGiftToSync: any,
        retryWallet: any
        secondaryIdentities: any
    }
}