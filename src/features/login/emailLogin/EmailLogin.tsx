import Button from '@features/common/button/Button';
import {
    FormControl,
    FormHelperText,
    InputLabel,
    TextField,
} from '@mui/material';
import { useTranslation } from 'next-i18next';
import { useContext, useEffect, useRef, useState } from 'react';
import styles from './EmailLogin.module.scss';
import { SEVERITY } from '@constants/messageTypes';
import {
    authSessionValid,
    signInWithMobile,
} from '@features/common/signInSignUpAPI';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import { emailAddressIsValid } from '@utils/emailValidation';
import { useForm } from 'react-hook-form';
import { CognitoHostedUIIdentityProvider } from '@aws-amplify/auth';

import {
    FieldToVerify,
    getPageFlow,
    getUserInfo,
    setCognitoCustomResponse,
    setPageFlow,
    setResendOtpReq,
    setUserInfo,
    UserType,
} from '../loginFlowSlice';
import ResponseInfoHOC from '@features/signinSignup/common/responseInfoHOC/ResponseInfoHOC';
import useAppRouter from '@features/common/router.context';
import {
    API_STATUS,
    PAGE_FLOW,
    SIGN_IN_SLUGS,
} from '@constants/common';
import { cloneDeep } from 'lodash';
import * as Sentry from '@sentry/nextjs';
import {
    checkUserExists,
    emailIsWorkEmail,
    executeCaptcha,
    getCustomSchemaErrorUrl,
    otpGeneratedSuccesfully,
    otpLimitExceededOrCaptchaFailed,
    platformIsMobileAppWithValidAuthSession,
} from '@utils/signInSignUpHelper';
import {
    CaptchaContext,
} from '@features/common/captcha.context';
import { getIsCaptchaRequired } from '@features/common/commonSlice';
import fetchAPI from '@utils/fetcher';
import useAuthAPI from '../authAPI';
import { GenerateLoginOTPResponse } from '../authAPI.interface';
import { GraphQLError } from '@interfaces/common.inteface';
import Warning from './warning/warning';
import getConfig from 'next/config';
import Confirm from '@features/common/confirm/Confirm';
import Image from 'next/image';



const {
    publicRuntimeConfig: { defaultRedirectUrl, imageBaseUrl },
} = getConfig();

const EmailLogin = ({
    showMessage,
    platformType,
    setShowEmailConfirmation,
    setPrefillMessage,
    confirmStatus,
    federatedSignIn,
    prefillInfo,
    prefilShow,
}: {
    state: any;
    showMessage: (
        message: string,
        severity: SEVERITY,
        heading?: string | null,
        online?: boolean | true,
        isHtml?: boolean | false
    ) => void;
    platformType?: any;
    setShowEmailConfirmation: (value: boolean) => void;
    setPrefillMessage: (value: string) => void;
    confirmStatus?: any;
    federatedSignIn: (value: any) => void;
    prefillInfo?: any;
    prefilShow?: any;
}) => {

    const captchaContext: any = useContext(CaptchaContext);
    const continueBtnRef: any = useRef();
    const { t } = useTranslation('common');
    const { generateLoginOTP } = useAuthAPI();
    const { register, handleSubmit, setValue } = useForm();
    const pageFlow = useAppSelector(getPageFlow);
    const userInfo = useAppSelector(getUserInfo);
    const dispatch = useAppDispatch();
    const [formData, setFormData] = useState<any>({});
    const [showRedirectPrompt, setShowRedirectPrompt] =
        useState<boolean>(false);
    const {
        state: { locale },
        router,
    } = useAppRouter();
    const [processState, setProcessState] = useState<'inProcess' | 'complete'>(
        'complete'
    );
    const [emailErrorMessage, setEmailErrorMessage] = useState('');
    const [buttonName, setButtonName] = useState<any>(t('continue'));
    // button screen
    const [showPrefill, setShowPrefill] = useState<boolean>(false);
    const [showWarning, setShowWarning] = useState<boolean>(false);

    const [appRedirectUrl, setAppRedirectUrl] = useState('');


    /**
     * Method to clear error messages
     */
    const clearErrorMessages = () => {
        setEmailErrorMessage('');
    };

    // #. Get captcha config from redux
    const isCaptchaRequired = useAppSelector(getIsCaptchaRequired);

    useEffect(() => {
        if (captchaContext.tokenGenerated && captchaContext.token) {
            onCaptchaChange();
            captchaContext.function.reset();
        }
    }, [captchaContext.tokenGenerated, captchaContext.token]);

    useEffect(() => {
        if (confirmStatus != null) {
            if (confirmStatus) {
                if (prefillInfo.prefillUserType == UserType.AppleUser) {
                    federatedSignIn({
                        provider: CognitoHostedUIIdentityProvider.Apple,
                    });
                } else if (
                    prefillInfo.prefillUserType == UserType.ConventionalUser
                ) {
                    setValue('email', prefillInfo.previousEmail);
                }
            }
        }
    }, [confirmStatus]);

    const onCaptchaChange = async () => {
        setProcessState('inProcess');
        if (captchaContext.token) {
            doSignIn(formData, captchaContext.token);
        } else {
            showErrorMsg(`No captcha token found`);
            setProcessState('complete');
        }

    };

    const onGenerateLoginOTPSuccess = async (response: GenerateLoginOTPResponse, token: string, cognitoCustomQuery: any, params: any) => {
        try {
            const { authLogin: { data, errors } }: { authLogin: any } = response;
            if (data == null) {
                if (errors && errors.length > 0) {
                    if (errors[0]['code'] == API_STATUS.REQUEST_BLOCKED) {
                        showMessage(
                            t('accountBlockedVisitHelpCenter'),
                            SEVERITY.ERROR,
                            t('authError'),
                            true,
                            true
                        );
                    } else {
                        showErrorMsg(errors[0]?.['message'] || t('somethingWrong'));
                    }
                }
                enableContinueButton();
                setProcessState('complete');
                return;
            }

            const { message, statusCode, sessionKey, mfaValues: { phoneNumber } } = data;

            if (otpLimitExceededOrCaptchaFailed(statusCode as API_STATUS)) {
                showErrorMsg(message);
                enableContinueButton();
                setProcessState('complete');
            } else if (otpGeneratedSuccesfully(statusCode as API_STATUS)) {
                let cognitoCustomResponse = await cognitoCustomQuery;
                dispatch(setCognitoCustomResponse(cognitoCustomResponse));
                dispatch(setPageFlow(PAGE_FLOW.CUSTOM_SIGN_IN));
                let info = cloneDeep(userInfo);
                info.toVerify = FieldToVerify.Email;
                info.SessionId = sessionKey;
                info.email = params?.email;
                info.phone_number = phoneNumber;
                console.log('user info ', info);
                dispatch(setUserInfo(info));
                dispatch(
                    setResendOtpReq({
                        type: 'email',
                        token: token,
                    })
                );
                router.push(
                    `?slug=${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    `${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    { locale, shallow: true }
                );
            } else {
                let res = response;
                if (typeof res === "string") {
                    res = JSON.parse(res);
                };
                // ##. TODO :: Check this scenario
                if (statusCode == API_STATUS.REQUEST_BLOCKED) {
                    showMessage(
                        t('accountBlockedVisitHelpCenter'),
                        SEVERITY.ERROR,
                        t('authError'),
                        true,
                        true
                    );
                } else {
                    Sentry.captureMessage(`Error in onGenerateLoginOTPSuccess ${message}`);
                    showErrorMsg(message);
                }
                enableContinueButton();
                setProcessState('complete');
            }
        } catch (error) {
            console.log('Error occured in handleGenerateLoginOTPSuccess ', error);
            console.log('params is ', params);
            Sentry.captureMessage('Error in handleGenerateLoginOTPSuccess');
            Sentry.captureException(error);
            showSomethingWentWrongError();
        }

    }

    const onGenerateLoginOTPError = ({ graphQLErrors: [{ message }] = [{ message: t('somethingWrong') }] }: GraphQLError) => {
        Sentry.captureMessage(`Error in onGenerateLoginOTPError ${message}`);
        showErrorMsg(message);
        setProcessState("complete");
        enableContinueButton();
    }

    const doSignIn = (formData: any, token?: string) => {
        let cognitoCustomQuery = signInWithMobile(formData.email.trim());
        let reqParams = cloneDeep(userInfo);
        reqParams.email = formData?.email.trim();
        generateLoginOTP(reqParams, 'email', token || '', locale, cognitoCustomQuery, onGenerateLoginOTPSuccess, onGenerateLoginOTPError);
    };

    const doSignUp = (formData: any) => {
        // ## user not found hence considering it as sign up flow
        dispatch(setPageFlow(PAGE_FLOW.EMAIL_SIGN_UP));

        // ## let's keep the email in redux store for later use
        let info = cloneDeep(userInfo);
        info.email = formData?.email.trim();
        dispatch(setUserInfo(info));

        // ## move to add info screen to collect more info
        router.push(
            `?slug=${SIGN_IN_SLUGS.ADD_INFO}`,
            `${SIGN_IN_SLUGS.ADD_INFO}`,
            { locale, shallow: true }
        );
    };

    /**
     * Form submit
     * @param e : form event
     * @returns  boolean
     */
    const validateForm = async (formData: any) => {

        disableContinueButton();
        setProcessState('inProcess');

        const captchaBackgroundClickHandler = () => {
            setProcessState('complete');
        };

        const domObserver = new MutationObserver(() => {
            const iframe = document.querySelector(
                'iframe[src^="https://www.google.com/recaptcha"][src*="bframe"]'
            );

            if (iframe) {
                domObserver.disconnect();

                const captchaBackground =
                    iframe.parentNode?.parentNode?.firstChild;
                captchaBackground?.addEventListener(
                    'click',
                    captchaBackgroundClickHandler
                );
            }
        });

        domObserver.observe(document.documentElement || document.body, {
            childList: true,
            subtree: true,
        });

        clearErrorMessages();
        localStorage.setItem('authMethod', 'Email');

        try {
            // ## check if entered email is valid
            if (!emailAddressIsValid(formData.email.trim())) {
                setEmailErrorMessage(t('invalidEmailMessage') || '');
                setProcessState('complete');
                enableContinueButton();
                return;
            }

            formData['email'] = formData?.email.toLowerCase();

            //setProcessState('complete');

            // ## Checking if auth session exists
            const authSession =
                router?.query?.auth_session ||
                localStorage.getItem('authSession') ||
                '';
            if (platformIsMobileAppWithValidAuthSession(platformType, authSession)) {
                // ## check for valid auth session in case of APP platform

                const validAuthSession = await authSessionValid();

                if (!validAuthSession) {
                    const url = getCustomSchemaErrorUrl('SESSION_EXPIRED');
                    window.location.href = url;
                    return;
                }
            }

            if (
                [PAGE_FLOW.SIGN_IN, PAGE_FLOW.NONE].includes(pageFlow) &&
                prefillInfo &&
                prefilShow
            ) {
                if (
                    formData.email &&
                    prefillInfo.previousEmail &&
                    formData.email.trim().toLowerCase() !=
                    prefillInfo.previousEmail.trim().toLowerCase()
                ) {
                    if (prefillInfo.prefillUserType == UserType.AppleUser) {
                        setPrefillMessage(t('continueWithApple'));
                    } else if (
                        prefillInfo.prefillUserType == UserType.ConventionalUser
                    ) {
                        setPrefillMessage(
                            t('continueWithPrevious', {
                                email: prefillInfo.previousEmail,
                            })
                        );
                    }

                    setShowEmailConfirmation(true);
                    setProcessState('complete');
                    enableContinueButton();
                    return;
                }
            }
            //setProcessState('complete');
            setFormData(formData);

            const userExistsResponse: any = await fetchAPI('/api/ratify', {
                method: 'POST',
                body: JSON.stringify({
                    email: formData?.email.trim() || '',
                }),
            });

            const { c: statusCode } = userExistsResponse?.data ?? { c: 0 };

            if (emailIsWorkEmail(statusCode)) {
                setShowWarning(true);
                setProcessState('complete');
                enableContinueButton();
                return false;
            }

            if (![API_STATUS.ACCOUNT_ALREADY_EXISTS, API_STATUS.USER_DOES_NOT_EXIST].includes(userExistsResponse?.data.c)) {
                Sentry.captureMessage(`Invalid response from /api/ratify API ${userExistsResponse?.data.c}`);
                showSomethingWentWrongError();
                return false;
            }

            const userExists = checkUserExists(userExistsResponse?.data);

            if (userExists && isCaptchaRequired) {
                executeCaptcha(captchaContext['elem']);
            } else if (!isCaptchaRequired && userExists) {
                doSignIn(formData)
            } else if (!userExists) {
                doSignUp(formData);
            } else {
                showSomethingWentWrongError();
            }
        } catch (error) {
            console.log('Error occured is ', error);
            Sentry.captureMessage('Error in Email Login Submit button ');
            console.log('Error in submit method :: ', error);
            setProcessState('complete');
            enableContinueButton();
        }
    };

    /**
     * Method to disable continue button
     */
    const disableContinueButton = () => {
        if (continueBtnRef.current) {
            continueBtnRef.current.setAttribute("disabled", "disabled");
        }
    }

    /**
     * Method to enable Continue button
     */
    const enableContinueButton = () => {
        if (continueBtnRef.current) {
            continueBtnRef.current.removeAttribute("disabled");
        }
    }

    /**
     * Method to show Something went wrong error message
     */
    const showSomethingWentWrongError = () => {
        showMessage(
            t('somethingWrong'),
            SEVERITY.ERROR,
            t('authError')
        );
    }

    /**
     * Method to show Error Message
     * @param message 
     */
    const showErrorMsg = (message: string) => {
        showMessage(
            message,
            SEVERITY.ERROR,
            t('authError')
        );
    };

    const handleOnConfirm = () => {
        window.location.href = appRedirectUrl;
        setShowRedirectPrompt(false);
        return false;
    };

    const closePopUp = () => {
        setShowWarning(false);
    }

    return (
        <>
            <div className={styles['signin-email']}>
                <form
                    onSubmit={handleSubmit(validateForm)}
                    className={'email-login'}
                >
                    <div className={`${styles['email-input-button']} ${emailErrorMessage ? 'error-outline' : ''}`}>

                        <div className={`${styles['email-input-button__input']}`}>
                            <Image width={24} height={24} alt='email' src={`${imageBaseUrl}/icons/sms.svg`} />
                            <FormControl variant="standard" fullWidth={true}>

                                <TextField
                                    {...register('email')}
                                    autoFocus={true}
                                    name="email"
                                    id="email"
                                    type="text"
                                    variant="standard"
                                    placeholder="<EMAIL>"
                                    inputProps={{ maxLength: 150 }}
                                />

                            </FormControl>

                        </div>


                        <div className={`button-container-email ${styles['button-container-email']}`}>
                            <Button
                                id="btnSubmit"
                                theme="purple"
                                attribues={{ ref: continueBtnRef }}
                                loadingState={processState}
                                className={`submit-button 
						${processState === 'inProcess' ? `button-submitted` : ''} ${styles['email-input-button__button']}`}
                            >
                                {buttonName}
                            </Button>
                        </div>


                    </div>

                    <div className='button-container-email-mweb'>
                            <Button
                                id="btnSubmit"
                                theme="purple"
                                attribues={{ ref: continueBtnRef }}
                                loadingState={processState}
                                className={`width-100 submit-button 
						${processState === 'inProcess' ? `button-submitted` : ''} ${styles['email-input-button__button']}`}
                            >
                                {buttonName}
                            </Button>
                        </div>
                    <FormHelperText
                        error={Boolean(emailErrorMessage)}
                        className={
                            styles[
                            'phone-verification__form-input-validation'
                            ]
                        }
                    >
                        {emailErrorMessage}
                    </FormHelperText>

                </form>
            </div>

            <Confirm
                icon={`${imageBaseUrl}/icons/redirect-arrow.png`}
                open={showRedirectPrompt}
                onClose={handleOnConfirm}
                message={t('youWillBeRedirectedToYougotagift')}
                title={t('redirectingToYougotagift') || ''}
                showCancel={false}
                fullsizeButton={true}
            ></Confirm>


            <Warning open={showWarning} onClose={closePopUp} />

        </>
    )
}

export default ResponseInfoHOC(EmailLogin);
