import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Dialog from '@mui/material/Dialog';
import { useTranslation } from 'next-i18next';
import styles from './warning.module.scss';
import Button from '@features/common/button/Button';

interface WarningDialogProps {
    open: boolean;
    onClose: (isConfirmed: boolean) => void;
}

const Warning = ({
    open,
    onClose
}: WarningDialogProps) => {
    // #. Get translations
    const { t } = useTranslation('common');

    /**
     * @method onConfirm
     */
    const onConfirm = (isConfirm: boolean) => {
        onClose && onClose(isConfirm);
    };

    return (
        <div className='warning'>
            <Dialog
            open={open}
            className={`warning ${styles['warning']}`}
        >
            <div
                className={`warning-container ${styles['warning-container']}`}
            >
                <div
                    className={`warning-section ${styles['warning-section']}`}
                >
                        <DialogTitle
                            className={`warning-title ${styles['warning-title']}`}
                        >
                            {t('loginUsingPersonalEmail')}
                        </DialogTitle>
                    <DialogContent
                        className={`warning-content ${styles['warning-content']}`}
                    >
                        {t('emailAssociatedWithPersonal')}
                    </DialogContent>
                </div>
                <DialogActions
                    className={`warning-buttons ${styles['warning-buttons']}`}
                >
                    <Button
                        action={() => onConfirm(true)}
                        theme="purple"
                        className={`warning-button-confirm ${
                            styles['warning-button-confirm']
                         } ${styles['warning-button-fullsize']}`}
                    >
                        {t('okay')}
                    </Button>
                </DialogActions>
            </div>
        </Dialog>
        </div>
        
    );
};

export default Warning;
