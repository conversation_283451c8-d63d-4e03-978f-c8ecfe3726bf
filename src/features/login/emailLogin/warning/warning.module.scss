@import '@styles/variables';
@import '/src/styles/mixins';
.warning {
    &-container {
        padding: 24px 20px 24px 20px;
        width: 600px;

        @media (max-width: ($sm)) {
            width: unset;
        }
    }

    &-icon {
        width: 16px;
        height: 16px;
    }

    &-content {
        border-radius: 6px;
        padding: 0px !important;
        margin-bottom: 16px !important;
        font-size: 16px !important;
        font-family: 'Poppins';
        color: #545454;

        @include rtl-styles{
            font-family: $arabic-font-family;
        }

        @media (max-width: ($sm)) {
            font-size: 14px !important;
        }
    }
    &-title {
        font-size: 20px !important;
        font-family: Poppins !important;
        font-weight: 600 !important;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: #3a1a5f;
        margin-bottom: 16px !important;
        @include rtl-styles {
            text-align: right;
        }
        @media (max-width: ($sm)) {
            line-height: 15px !important;
        }
    }

    &-buttons {
        padding: 0px !important;
        margin-top: 40px !important;
        button {
            padding: 5px 15px;
        }

        @media (max-width: ($sm)) {
            margin-top: 24px !important;
        }
    }
    &-button-cancel {
        border-radius: 8px !important;
        height: 50px;
        width: 182px;
        border-color: $black-header;
        span {
            color: $black-header;
            font-family: Poppins;
            font-size: 16px;
            font-weight: 500;
        }
         @media only screen and (max-width: $sm) {
            width: 125px;
        }
    }
    &-button-confirm {
        border-radius: 8px !important;
        height: 50px;
        width: 182px;
        background-color: $semi-dark-purple;
        span {
            font-family: Poppins;
            font-size: 16px;
            font-weight: 500;
        }
        @include rtl-styles {
            margin-right: 8px;
        }
        @media only screen and (max-width: $sm) {
            width: 125px;
        }
    }

    &-button-fullsize{
        width: 100% !important;
    }
}