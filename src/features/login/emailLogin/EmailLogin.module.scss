@import '@styles/variables';
@import '@styles/mixins';

.password-icon {
    position: absolute;
    right: 0;
    bottom: 0;
}

.forgot-password {
    @include font-size(12);
    cursor: pointer;
}

.decrease-spacing {
    padding-bottom: 10px !important;
}

.signin-email h3 {
    margin: 0 0 66px;

    @media only screen and (max-width: $sm) {
        margin: 0 0 24px;
    }
}

.signin-email {
    input {
        font-family: $mona-sans-font-family !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px; /* 150% */
        letter-spacing: -0.16px;
        margin: 0;

        @media only screen and (max-width: $sm) {
            font-size: 14px;
        }

        @include rtl-styles {
            font-family: $arabic-font-family !important;
        }
    }
    li { 
        padding-bottom: 23px !important;
    }   
}

.email-input-button{
    display: flex;
    padding: 8px 8px 8px 16px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 12px;
    border: 3px solid #FFD7EF;
    background: var(--White-White---Primary, #FFF);

    &__input{
        width: 300px !important;
        height: 50px !important;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }

    &__button{
        width: 144px;
        height: 50px;
    }

    @media only screen and (max-width: $sm) {
     height: 50px;
    }
}

.button-container-email{
    width: 144px;
}

