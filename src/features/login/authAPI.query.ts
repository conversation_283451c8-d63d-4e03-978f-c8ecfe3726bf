import { gql } from '@apollo/client';

export const AUTH_LOGIN = gql`
    mutation AuthLogin($input: AuthLoginInput!) {
        authLogin(input: $input) {
            data {
                sessionKey
                statusCode
                message
                authenticationResult
                mfaValues {
                    phoneNumber
                    emailAddress
                }
            }
            errors {
                field
                message
                code
            }
        }
    }
`;

export const AUTH_SIGNUP = gql`
    mutation AuthSignup($input: AuthSignUpInput!) {
        authSignup(input: $input) {
            data {
                sessionKey
                statusCode
                message
                authenticationResult {
                    accessToken
                    idToken
                    refreshToken
                }
                extraAttributes {
                    authSignature
                    devicePlatform
                }
            }
            errors {
                field
                message
                code
            }
        }
    }
`;

export const AUTH_SET_TOKEN = gql`
    mutation AuthSetTokens($input: AuthSetTokenInput!) {
        authSetTokens(input: $input) {
            data {
                message
                accessToken
                idToken
                authSignature
                devicePlatform
            }
            errors {
                field
                message
                code
            }
        }
    }
`;

export const AUTH_USER_DETAILS = gql`
    query UserDetails($input: AuthAccessTokenInput) {
        userDetails(input: $input) {
            username
            email
            birthdate
            name
            emailVerified
            phoneNumber
            phoneNumberVerified
            gender
            picture
            sub
            apiTimeSpan
            apiCallInterval
            giftCount
            hasGiftToSync
            retryWallet
            secondaryIdentities {
                username
                email
            }
        }
    }
`;

export const BUSINESS_LOGIN = gql`
    mutation ValidateBusinessUserExists(
        $input: UserValidateByEmailMutationInput!
    ) {
        validateBusinessUserExists(input: $input) {
            data {
                status
                message
                isFirstTimeUser
                redirectUrl
            }
            errors {
                field
                message
                code
            }
        }
    }
`;

export const GENERATE_OTP_QUERY = gql`
  mutation LoginGenerateOtp($input: GenerateOTPMutationInput!) {
    loginGenerateOtp(input: $input) {
      data {
        status
        message
        authSession
      }
      errors {
        field
        message
        code
      }
    }
  }
`;
