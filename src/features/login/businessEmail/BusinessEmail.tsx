import React, { useRef, useState } from 'react';
import styles from './BusinessEmail.module.scss';
import Image from 'next/image';
import { FormControl, FormHelperText, TextField } from '@mui/material';
import Button from '@features/common/button/Button';
import getConfig from 'next/config';
import { useTranslation } from 'next-i18next';
import HelpText from '@features/signinSignup/common/helpText/HelpText';
import {
    BUSINESS_STATUS_CODES,
    PLATFORM_TYPE,
    SIGN_IN_SLUGS,
} from '@constants/common';
import useAppRouter from '@features/common/router.context';
import FbWarning from '../signIn/fbWarning/fbWarning';
import { emailAddressIsValid } from '@utils/emailValidation';
import { useForm } from 'react-hook-form';
import useAuthAPI from '../authAPI';
import { atWorkBaseUrl } from '@constants/apiEndPoints';
import { getCookie } from '@utils/getAndsetCookie';

// taking public image config url
const {
    publicRuntimeConfig: { imageBaseUrl, solutionsHubURL },
} = getConfig();

const BusinessEmail = ({ platformType }: any) => {
    const continueBtnRef: any = useRef();
    const { t } = useTranslation('common');
    const [showFbWarning, setShowFbWarning] = useState<boolean>(false);
    const { register, handleSubmit } = useForm();
    const { validateUserExists } = useAuthAPI();

    // Get locale and region from cookie with default value
    const localeRegionCookie = getCookie('LOCALE_REGION') || 'en-ae';
    const {
        state: { locale },
        router,
    } = useAppRouter();
    const { generateAtworkOtp } = useAuthAPI();

    const [processState, setProcessState] = useState<'inProcess' | 'complete'>(
        'complete'
    );
    const [emailErrorMessage, setEmailErrorMessage] = useState('');
    const [buttonName, setButtonName] = useState<any>(t('continue'));

    const handleFBLogin = () => {
        try {
            setShowFbWarning(true);
        } catch (error) {}
    };

    /**
     * Method to disable continue button
     */
    const disableContinueButton = () => {
        if (continueBtnRef.current) {
            continueBtnRef.current.setAttribute('disabled', 'disabled');
        }
    };

    /**
     * Method to enable Continue button
     */
    const enableContinueButton = () => {
        if (continueBtnRef.current) {
            continueBtnRef.current.removeAttribute('disabled');
        }
    };

    const validateForm = async (formData: any) => {
        const trimmedEmail = formData?.email?.trim();
        disableContinueButton();
        setProcessState('inProcess');

        // Validate email format
        if (!emailAddressIsValid(trimmedEmail)) {
            setEmailErrorMessage(t('invalidEmailMessage') || '');
            setProcessState('complete');
            enableContinueButton();
            return;
        }

        try {
            const response = await validateUserExists(trimmedEmail);
            if (response?.errors && response?.errors?.length > 0) {
                setEmailErrorMessage(response?.errors[0].message);
                return;
            }
            const { status, redirectUrl } = response.data;

            // Handle different user status cases
            const statusRedirectMap: any = {
                [BUSINESS_STATUS_CODES.REWARDS_USER]: redirectUrl,
                [BUSINESS_STATUS_CODES.AT_WORK_USER]: 96408,
                [BUSINESS_STATUS_CODES.NOT_ANY_USER]: `${solutionsHubURL}?inquire-now=inquire-now`,
            };

            const targetUrl = statusRedirectMap[status];
            if (targetUrl === BUSINESS_STATUS_CODES.AT_WORK_USER) {
                const response = await generateAtworkOtp({
                    input: {
                        email: trimmedEmail,
                        countryCode: 'AE',
                        languageCode: locale,
                    },
                });

                if (
                    response?.loginGenerateOtp?.errors &&
                    response.loginGenerateOtp.errors.length > 0
                ) {
                    setEmailErrorMessage(
                        response.loginGenerateOtp.errors[0].message
                    );
                } else if (response?.loginGenerateOtp?.data) {
                    const { authSession } = response.loginGenerateOtp.data;
                    router.push(
                        `?slug=${SIGN_IN_SLUGS.REWARDS_LOGIN}`,
                        `${
                            SIGN_IN_SLUGS.REWARDS_LOGIN
                        }?email=${encodeURIComponent(
                            trimmedEmail
                        )}&authSession=${encodeURIComponent(authSession)}`,
                        { locale, shallow: true }
                    );
                }
            } else {
                router.push(targetUrl);
            }
        } catch (error) {
            console.error('Error validating user:', error);
        } finally {
            setProcessState('complete');
            enableContinueButton();
        }
    };

    return (
        <div className={styles['signin-email']}>
            <h4>{t('enterBusinessEmail')}</h4>
            <form
                onSubmit={handleSubmit(validateForm)}
                className={'email-login'}
            >
                <div
                    className={`${styles['email-input-button']} ${
                        emailErrorMessage ? 'error-outline' : ''
                    }`}
                >
                    <div className={`${styles['email-input-button__input']}`}>
                        <Image
                            width={24}
                            height={24}
                            alt="email"
                            src={`${imageBaseUrl}/icons/sms.svg`}
                        />
                        <FormControl variant="standard" fullWidth={true}>
                            <TextField
                                {...register('email')}
                                autoFocus={true}
                                name="email"
                                id="email"
                                type="text"
                                variant="standard"
                                placeholder="<EMAIL>"
                                inputProps={{ maxLength: 150 }}
                            />
                        </FormControl>
                    </div>

                    <div
                        className={`button-container-email ${styles['button-container-email']}`}
                    >
                        <Button
                            id="btnSubmit"
                            theme="purple"
                            attribues={{ ref: continueBtnRef }}
                            loadingState={processState}
                            className={`submit-button 
${processState === 'inProcess' ? `button-submitted` : ''} ${
                                styles['email-input-button__button']
                            }`}
                        >
                            {buttonName}
                        </Button>
                    </div>
                </div>
                <div className="button-container-email-mweb">
                    <Button
                        id="btnSubmit"
                        theme="purple"
                        attribues={{ ref: continueBtnRef }}
                        loadingState={processState}
                        className={`width-100 submit-button 
						${processState === 'inProcess' ? `button-submitted` : ''} ${
                            styles['email-input-button__button']
                        }`}
                    >
                        {buttonName}
                    </Button>
                </div>
                <FormHelperText
                    error={Boolean(emailErrorMessage)}
                    className={
                        styles['phone-verification__form-input-validation']
                    }
                >
                    {emailErrorMessage}
                </FormHelperText>
            </form>
            <div className={styles['trouble-signin-container']}>
                <div className={styles['trouble-signin']}>
                    <Image
                        src={`${imageBaseUrl}/icons/info-circles.svg`}
                        alt="Trouble Sign In"
                        width={16}
                        height={16}
                    />
                    <span className={styles['content']} onClick={handleFBLogin}>
                        {t('troubleSignIn')}
                    </span>
                </div>
                <HelpText />
            </div>
            <FbWarning
                open={showFbWarning}
                platformType={platformType}
                onClose={setShowFbWarning}
            />
        </div>
    );
};

export default BusinessEmail;
