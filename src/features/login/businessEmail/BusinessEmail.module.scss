@import '@styles/variables';
@import '@styles/mixins';

.password-icon {
    position: absolute;
    right: 0;
    bottom: 0;
}

.forgot-password {
    @include font-size(12);
    cursor: pointer;
}

.decrease-spacing {
    padding-bottom: 10px !important;
}

.signin-email h3 {
    margin: 0 0 66px;

    @media only screen and (max-width: $sm) {
        margin: 0 0 24px;
    }
}

.signin-email {
    h4 {
        font-size: 24px;
        font-style: normal;
        font-weight: 800;
        line-height: 32px;
        font-optical-sizing: none;
        background: linear-gradient(90deg, #0071ff 0%, #99c6ff 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
        margin: 0 0 32px 0;
    }
    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 32px;

        img {
            width: 90px;
            height: 42px;
            aspect-ratio: 67.79/40;
        }

        h5 {
            font-size: 24px;
            font-style: normal;
            font-weight: 800;
            line-height: 32px;
            background: linear-gradient(90deg, #0071FF 0%, #99C6FF 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 0;
        }
    }

    input {
        font-family: $mona-sans-font-family !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px; /* 150% */
        letter-spacing: -0.16px;
        margin: 0;

        @include rtl-styles {
            font-family: $arabic-font-family !important;
        }
    }
    li {
        // padding-bottom: 23px !important;
    }
}

.email-input-button {
    display: flex;
    padding: 8px 8px 8px 16px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 12px;
    border: 3px solid #99c6ff;
    background: var(--White-White---Primary, #fff);

    &__input {
        width: 300px !important;
        height: 50px !important;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }

    &__button {
        width: 144px;
        height: 50px;
    }

    @media only screen and (max-width: $sm) {
        height: 50px;
    }
}

.password-field {
    h5 {
        font-family: $mona-sans-font-family;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: var(--Font-Line-height-lg, 24px); /* 150% */
        letter-spacing: -0.16px;
        margin: 24px 0;

        @include rtl-styles {
            font-family: $arabic-font-family;
        }
    }

    &-remember {
        display: flex;
        align-items: center;
        margin: 8px 0 32px 0;
        justify-content: space-between;

        span {
            font-family: $mona-sans-font-family;
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
            line-height: 16px; /* 133.333% */
            letter-spacing: -0.12px;

            @include rtl-styles {
                font-family: $arabic-font-family;
            }
        }

        &__left {
            display: flex;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            .checkbox {
                height: 16px;
                width: 16px;
                border: 1px solid $dark-purple;
                border-radius: 4px;
                img {
                    display: block;
                }
            }

            .checkbox-empty {
                height: 16px;
                width: 16px;

                img {
                    display: block;
                }
            }
        }
    }
}

.password-input-button {
    display: flex;
    padding: 10px 16px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 12px;
    border: 3px solid #99c6ff;
    background: var(--White-White---Primary, #fff);
    border: 1px solid #d9d9d9;
    &__input {
        width: 100% !important;
        height: 44px !important;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }

    &__button {
        width: 144px;
        height: 50px;
    }

    @media only screen and (max-width: $sm) {
        height: 50px;
    }
}

.button-container-email {
    width: 144px;
}

@media only screen and (max-width: $sm) {
    .trouble-signin-container {
        flex-direction: column;

        .trouble-signin {
            justify-content: center !important;
        }
    }
}

@media only screen and (max-height: 650px) {
    .trouble-signin-container {
        margin-top: 0px !important;
    }
}
.trouble-signin-container {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;

    .trouble-signin {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        border-radius: 120px;
        height: 18px;
        align-items: center;
        width: 100%;

        // @include rtl-styles{
        //     width: 280px;
        // }

        img {
            margin-right: 4px;
            height: 16px;
            width: 16px;
            object-fit: cover;
            @include rtl-styles {
                margin-left: 4px;
                margin-right: unset;
            }
        }

        .content {
            font-family: 'Mona Sans';
            font-size: 12px;
            font-weight: 500;
            font-stretch: normal;
            font-style: normal;
            letter-spacing: -0.12px;
            line-height: 18px; /* 150% */
            text-align: left;
            color: $dark-purple;
            cursor: pointer;

            @include rtl-styles {
                font-family: $arabic-font-family !important;
                text-align: right;
            }
        }
    }
}
