@import '@styles/variables';
@import '@styles/mixins';

.name-select-menu {
    padding: 5px;
    
    @media screen and (max-width : $sm) {
        left: 75px !important;
    }

    li {
        @include font-size(16);
    }
}

.bind-form-element {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    gap: 8px;

    &__icon{
        position: absolute;
        top: 18px;
        left: 26px;

        @include rtl-styles{
            right: 55px;
        }
    }
    

    @include rtl-styles {
        
    }

    @include font-size(16);

    &__select {
        width: 39px;

        @include rtl-styles{
            width: 65px;
        }

        [role='button'] {
            
            padding: 4px 0;
            position: relative;
            padding-right: 0 !important;
            font-weight: 700;
            color: $dark-charcoal;

            @include rtl-styles {
                padding-right: 0;
                padding-left: 24px;
            }

            // &::after {
            //     content: '|';
            //     position: absolute;
            //     right: 10px;
            //     font-weight: $thin;
            //     @include font-size(16);

            //     @include rtl-styles {
            //         right: auto;
            //         left: 10px;
            //     }
            // }
        }

        svg {
            width: 0px;
            top: calc(50% + 1px);
            right: 15px;

            @include rtl-styles {
                right: auto;
                left: 15px;
            }
        }

        
    }
}
.guest-name-element {
    display: grid;
}


.salutation-dropdown {
    width: 100%;

    @include rtl-styles{
        width: 80px;
    }
    @include font-size-important(16);

    color: $dark-purple !important;

    input {
        margin: 0;
    }

    &:focus{
        background-color: white !important;
    }
}

.mobile-date-picker {
    & * {
        @include font-size(12);
    }

    h4 {
        @include font-size(16);
    }
}

.email-info {
    color: $warm-grey;
    font-weight: $regular;
    @include font-size(10);

    svg {
        position: relative;
        top: 4px;
        margin-left: 5px;
        width: 16px;
        height: 16px;

        path {
            color: $barney-purple;
        }
    }
}

.create-password {
    position: absolute;
    right: 0;
    bottom: 0;

    @include rtl-styles {
        right: auto;
        left: 0;
    }
}

.signup-add-info {
   

    input {
        color: $dark-charcoal !important;
        font-weight: 700;
    }

    label {
        font-family: Poppins !important;
        font-size: 14px !important;
        font-weight: normal !important;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: 0.12px;
        text-align: left;
        color: $black-header;
    }
    ul{
        display: flex;
        flex-direction: column;
        gap: 16px;

        @media only screen and (max-width: $sm) {
             gap: 10px;
        }
    }
    li{
        display: flex;
        height: 70px;
        padding: 10px 8px 10px 16px;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        border-radius: 12px;
        border: 1px solid var(--grey-grey-stroke-drag-handle, #D9D9D9);
        background: var(--White-White---Primary, #FFF);

        @media only screen and (max-width: $sm) {
            height: 50px;
        }
    }
}

.datepicker {
    fieldset {
        border-width: 0 !important;
        border-bottom-width: 1px !important;
        border-bottom-color: $light-grey !important;
        border-radius: 0;
    }

    input {
        padding: 5px 6px;
        @include font-size(16);
    }

    select {
        padding: 5px 6px;
        @include font-size(16);
    }
}

.password-mask {
    width: 24px;
    height: 24px;
    margin: 10px 0 0 10px;
    object-fit: contain;
    cursor: pointer;
}

.respond-message {
    position: absolute;
    top: -110px;
    display: grid;
    grid-template-columns: 20px auto;
    gap: 10px;
    margin-bottom: 30px;
    width: 512px;
    height: 83px;
    padding: 16px 82px 16px 16px;
    border-radius: 12px;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 10%);
    border: solid 1px $barney-purple;
    background-color: $white;
    @include font-size(12);

    p {
        width: 358px;
        height: 17px;
        margin: 5px 0 0 16px;
        font-family: Poppins;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: $black-header;
    }

    svg {
        width: 40px;
        height: 40px;
    }

    h5 {
        width: 100%;
        height: 23px;
        margin: 0 177px 5px 16px;
        font-family: Poppins;
        font-size: 16px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: normal;
        text-align: left;
        color: $dark-purple;
    }

    &--error {
        background-color: $white;

        svg {
            path {
                color: $error-text;
            }
        }
    }

    &--success {
        background-color: $white;

        svg {
            path {
                color: $barney-purple;
            }
        }
    }

    &--info {
        background-color: $very-light-purple;

        svg {
            path {
                color: $barney-purple;
            }
        }
    }
}

.help-text-container {
    padding-bottom: 20px;
}

.agreement {
    padding: 20px 0;

    .-continue-to-agree {
        height: 17px;
        font-family: Poppins;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        letter-spacing: 0.12px;
        text-align: center;
        color: $warm-grey;
    }



    .-continue-to-agree .text-style-1 {
        color: $warm-grey;
    }

    .-continue-to-agree .text-style-2 {
        > a {
            font-weight: 600;
            color: $black-header;
        }
    }
}

.disable-click {
    pointer-events: none;
}

.btnAgree{
    display: flex;
    width: 528px;
    height: 50px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    border-radius: 8px;
    background: $dark-charcoal;

    span{
        color: $white;
        text-align: right;
        /* Other/Text - Button */
        font-family: "Mona Sans";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: var(--Font-Line-height-lg, 24px); /* 150% */
        letter-spacing: -0.16px;

        @include rtl-styles{
            font-family: $arabic-font-family;
        }
    }
}

.input-with-icon{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
}

.help-text-wrapper{
    display: flex;
    justify-content: center;
}

.flex-1{
    flex: 1;
}

.btn-container{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 32px;
    margin-bottom: 32px;

    @media only screen and (max-width: $sm) {
    margin-top: 6px;
    }
}