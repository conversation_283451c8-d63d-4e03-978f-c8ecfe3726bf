import {
    FormControl,
    FormHelperText,
    InputLabel,
    MenuItem,
    Select,
    SelectChangeEvent,
    TextField,
} from '@mui/material';
import { Fragment, useContext, useEffect, useRef, useState } from 'react';
import styles from './AddInfo.module.scss';
import { useTranslation } from 'next-i18next';
import { useForm } from 'react-hook-form';
import {
    generateLoginOTP,
    sendOTP,
    signInWithMobile,
} from '@features/common/signInSignUpAPI';
import hasSpecial<PERSON>haracter from '@utils/hasSpecialCharacter';
import ResponseInfoHOC from '@features/signinSignup/common/responseInfoHOC/ResponseInfoHOC';
import useAppRouter from '@features/common/router.context';
import { SEVERITY } from '@constants/messageTypes';
import {
    FieldToVerify,
    getPageFlow,
    getRdir,
    getUserInfo,
    setCognitoCustomResponse,
    setPageFlow,
    setResendOtpReq,
    setUserInfo,
    UserInfo,
} from '../loginFlowSlice';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import { emailAddressIsValid, isGuestEmailValid } from '@utils/emailValidation';
import {
    PLATFORM_TYPE,
    SIGN_IN_SLUGS,
    PAGE_FLOW,
    COGNITO_USER_TYPE,
    AUTH_FLOW,
    API_STATUS,
    GRAPHQL_STATUS_CODE,
    PAGEURLS,
    EMAIL_EXISTS
} from '@constants/common';
import Button from '@features/common/button/Button';
import cloneDeep from 'lodash/cloneDeep';
import getConfig from 'next/config';
import useReCaptchaAPI from '@features/common/reCaptcha/ReCaptchaAPI';
import dateMonthIsValid from '@utils/dateMonthIsValid';
import * as Sentry from '@sentry/nextjs';
import Confirm from '@features/common/confirm/Confirm';
import { ApolloError, useMutation } from '@apollo/client';
import { GUEST_LOGIN } from '@features/common/common.query';
import isYouGotaGiftDomain from '@utils/isYouGotaGiftDomain';
import { getIpAddress, getIsCaptchaRequired } from '@features/common/commonSlice';
import fetchAPI from '@utils/fetcher';
import encrypt from '@utils/encrypt';
import { checkUserExists, executeCaptcha, otpGeneratedSuccessfully, phoneEmailAlreadyExists, prepareSocialSignUpReqParams } from '@utils/signInSignUpHelper';
import { emailIsWorkEmail } from '@utils/signInSignUpHelper';
import { CaptchaContext } from '@features/common/captcha.context';
import { loginGuestGA } from 'lib/gtm';
import { cleverTapService } from '@features/common/clevertap/clevertap.services';
import useAuthAPI from '../authAPI';
import { SignUpResponse } from '../authAPI.interface';
import { GraphQLError } from '@interfaces/common.inteface';
import Warning from '../emailLogin/warning/warning';
import TitleHeader from '../titleHeader/TitleHeader';
import HelpText from '@features/signinSignup/common/helpText/HelpText';
import Image from 'next/image';


const {
    publicRuntimeConfig: {
        defaultRedirectUrl,
        defaultMwebRedirectUrl,
        imageBaseUrl
    },
} = getConfig();

const AddInfo = ({
    showMessage,
    platformType,
    logo
}: {
    showMessage: (
        message: string,
        severity: SEVERITY,
        heading?: string | null,
        online?: boolean | true,
        isHtml?: boolean | false
    ) => void;
    platformType: any;
    logo: any;
}) => {
    const { t } = useTranslation('common');
    const captchaContext: any = useContext(CaptchaContext);
    const [dobErrorMessage, setDobErrorMessage] = useState('');
    const [nameErrorMessage, setNameErrorMessage] = useState('');
    const [emailErrorMessage, setEmailErrorMessage] = useState('');
    const [store, setStore] = useState('ae');
    const [showGuestLoginWarning, setShowGuestLoginWarning] = useState(false);
    const ip = useAppSelector(getIpAddress);
    const isCaptchaRequired = useAppSelector(getIsCaptchaRequired);
    const { userSignUp } = useAuthAPI();


    const {
        state: { locale },
        router
    } = useAppRouter();

    useEffect(() => {
        const storeParam = router.query.store;
        if (storeParam && storeParam.length) {
            const store = storeParam.split('-')[1];
            setStore(store);
        }
    }, [router.query]);

    const [handleGuestSignIn] = useMutation(GUEST_LOGIN, {
        context: {
            clientName: 'guestLogin',
            headers: {
                'app-platform': platformType,
                'access-locale': `ST${localStorage.getItem('selectedStore')?.toUpperCase() || 'AE'
                    }`,
                'yg-ip-address': ip,
            },
            credentials: 'include',
        },
        onCompleted: (response: any) => {
            onAfterGuestSignIn(true, response);
        },
        onError: (error: ApolloError) => {
            onAfterGuestSignIn(false, error);
        },
    });

    const afterSignInRedirect = () => {
        let lrDir = localStorage.getItem('rdir');
        if (rdir && rdir.length && isYouGotaGiftDomain(rdir)) {
            router.push(rdir, undefined, { locale });
        } else if (lrDir && lrDir.length && isYouGotaGiftDomain(lrDir)) {
            router.push(lrDir, undefined, { locale });
        } else {
            router.push({ pathname: defaultRedirectUrl });
        }
    };

    const dispatch = useAppDispatch();

    const rdir = useAppSelector(getRdir);

    const storeUserInfo = useAppSelector(getUserInfo);

    const pageFlow = useAppSelector(getPageFlow);

    const { register, handleSubmit, setValue, getValues } = useForm();

    const [processState, setProcessState] = useState<'inProcess' | 'complete'>(
        'complete'
    );

    const [userFormData, setUserFormData] = useState('');

    const [signInConfirmClick, setSignInConfirmClick] = useState(false);

    const continueButtonRef: any = useRef();
    const [showWarning, setShowWarning] = useState<boolean>(false);


    useEffect(() => {
        if (captchaContext.tokenGenerated && captchaContext.token) {
            onCaptchaChange();
            captchaContext.function.reset();
        }
    }, [captchaContext.tokenGenerated, captchaContext.token]);

    useEffect(() => {
        if (signInConfirmClick && pageFlow === PAGE_FLOW.CUSTOM_SIGN_IN) {
            if (isCaptchaRequired) {
                executeCaptcha(captchaContext['elem']);
            } else {
                proceedWithoutCaptcha(userFormData)
            }
        }
    }, [signInConfirmClick, pageFlow])


    const handleSignUp = async (userInfo: any, token: any = null) => {
        // ## Based on the flow handle the Sign up
        switch (pageFlow) {
            case PAGE_FLOW.MOBILE_SIGN_UP:
                await handleMobileSignUp(userInfo, token);
                break;
            case PAGE_FLOW.EMAIL_SIGN_UP:
                await handleEmailSignUp(userInfo, token);
                break;
            case PAGE_FLOW.SOCIAL_SIGN_UP_WITH_EMAIL:
            case PAGE_FLOW.SOCIAL_SIGN_UP_WITHOUT_EMAIL:
                await handleSocialSignUp(userInfo, token);
                break;
            case PAGE_FLOW.CUSTOM_SIGN_IN:
                await handleEmailOTPSignIn(token);
                break;
        }
    };

    const onCaptchaChange = async () => {
        console.log('recieved captcha token ...', captchaContext?.token);
        setProcessState('inProcess');
        //captchaContext.elem.current.reset();
        // ## format the add info form data
        let userInfo = formatFormData(userFormData);
        console.log('user info ', userInfo);

        // Replace PAGE_FLOW_VALUE with the actual value you want to use
        await handleSignUp(userInfo, captchaContext.token);

        // ## stop showing the loading indicator
        setProcessState('complete');
    };

    const proceedWithoutCaptcha = async (userFormData?: any) => {
        setProcessState('inProcess');
        // ## format the add info form data
        let userInfo = formatFormData(userFormData);

        // Replace PAGE_FLOW_VALUE with the actual value you want to use
        await handleSignUp(userInfo);
    }

    useEffect(() => {
        setValue('birthdate', null);
        if (storeUserInfo && storeUserInfo?.email) {
            setValue('email', storeUserInfo?.email);
            setValue('name', storeUserInfo?.UserAttributes?.name);
            if (storeUserInfo?.UserAttributes?.birthdate) {
                setValue('birthdate', storeUserInfo?.UserAttributes?.birthdate);
            }
        }
    }, []);

    // date picker value
    const [mobileDatePickerValue, setMobileDatePickerValue] =
        useState<Date | null>(new Date());

    const [salutation, setSalutation] = useState('mr');

    const clearErroMsgs = () => {
        setNameErrorMessage('');
        setEmailErrorMessage('');
    };

    const getUnusualAcitvityUrl = () => {
        return `${defaultRedirectUrl}/${locale}-${store}${PAGEURLS.SUSPICIOUS_ACTIVITY}`
    }

    const onAfterGuestSignIn = (status: any, response: any) => {
        // #. Check for server error on email validation
        switch (response?.guestLogin?.login?.status) {
            case GRAPHQL_STATUS_CODE.INVALID_EMAIL:
                setEmailErrorMessage(t('invalidEmailMessage') || '');
                setProcessState('complete');
                return;
            case GRAPHQL_STATUS_CODE.INVALID_NAME:
                setNameErrorMessage(t('invalidNameMessage') || '');
                setProcessState('complete');
                return;
            case GRAPHQL_STATUS_CODE.EMAIL_EXISTS:
                setEmailErrorMessage(t('emailAlreadyExistsMessage') || '');
                setProcessState('complete');
                return;
            case GRAPHQL_STATUS_CODE.UNUSUAL_ACTIVITY:
                const redirectUrl = getUnusualAcitvityUrl();
                window.location.href = redirectUrl;
                return;
        }

        let userInfo = formatFormData(userFormData);
        if (status) {
            // #. GTM event for Guest Login
            loginGuestGA()

            // #. Clevertap profile creation for Guest User
            cleverTapService.pushClevertapProfile(
                userInfo?.email || '',
                userInfo?.UserAttributes?.name || '',
                locale
            );

            // #. Clevertap event for Guest Login
            cleverTapService.pushGuestLogin();
            afterSignInRedirect();
        } else {
            console.log('Error occured in Guest Logout ', response)
            Sentry.captureMessage('Error occured in handleGuestLogout ', response);
        }
    };

    const validateFormFields = async ({ name, email }: any) => {
        clearErroMsgs();
        if (!name || !name.trim().length || name.trim().length < 2 || name.trim().length > 26) {
            setNameErrorMessage(t('minMaxLengthForNameRequired') || '');
            return false;
        }
        if (hasSpecialCharacter(name.trim())) {
            setNameErrorMessage(t('specialCharactersNotAllowedInName') || '');
            return false;
        }

        if (dobErrorMessage && dobErrorMessage.length) {
            return false;
        }

        // #. Do special validation for Guest email
        if (pageFlow == PAGE_FLOW.GUEST) {
            if (!isGuestEmailValid(email.trim())) {
                setEmailErrorMessage(t('invalidEmailMessage') || '');
                return false;
            } else {
                if (!emailAddressIsValid(email.trim())) {
                    setEmailErrorMessage(t('invalidEmailMessage') || '');
                    return false;
                }
            }
        }
        return true;
    };

    const handleMobileSignUp = async (userInfo: UserInfo, token: string) => {
        try {
            dispatch(setUserInfo(userInfo));
            let reqParams = prepareMobileSignUpReqParams(userInfo, token);
            dispatch(setResendOtpReq(reqParams));
            let response = await sendOTP(reqParams, locale);

            if (
                response &&
                response?.STATUS_CODE == API_STATUS.ALREADY_EXISTS
            ) {
                showMessage(
                    t('emailAlreadyExistsWithAnotherUser'),
                    SEVERITY.INFO,
                    t('emailAlreadyExists')
                );
            } else {
                // ## check for session in response
                userInfo.SessionId = response?.SESSION_KEY;
                userInfo.toVerify = FieldToVerify.Email;
                dispatch(setUserInfo(userInfo));

                // ## navigate to OTP Entry Screen
                router.push(
                    `?slug=${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    `${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    { locale, shallow: true }
                );
            }
        } catch (error) {
            Sentry.captureMessage(`Error occured in handleEmailSignUp ${error}`);
            console.log('Error occured in handleEmailSignUp ', error);
            throw new Error('Error occured during sign up');
        }
    };

    const prepareEmailSignUpReqParams = (userInfo: UserInfo, token: string) => {
        return {
            'email': userInfo.email,
            'captchaReference': token,
            'UserAttributes': {
                'name': userInfo?.UserAttributes?.name ?? '',
                'birthdate' : userInfo?.UserAttributes?.birthdate ?? '',
                'gender' : userInfo?.UserAttributes?.gender ?? ''
            }
        };
    };

    const prepareMobileSignUpReqParams = (userInfo: UserInfo, captcha: any) => {
        let reqParams: any = cloneDeep(userInfo);
        delete reqParams.phone_number;
        reqParams['captcha_reference'] = captcha;
        return reqParams;
    };

    /**
     * Method to show Something went wrong error message
    */
    const showSomethingWentWrongError = () => {
        showMessage(
            t('somethingWrong'),
            SEVERITY.ERROR,
            t('authError')
        );
    }

    const onEmailSignUpSuccess = (response: SignUpResponse, userInfo: UserInfo) => {
        try {
            const { authSignup: { data, errors = [] }, offline }: any = response;

            if (data == null) {
                if (errors && errors.length > 0) {
                    showErrorMsg(errors[0]?.['message'] || t('somethingWrong'));
                    return;
                } else {
                    showErrorMsg(t('somethingWrong'));
                    return;
                }
            }

            const { message = '', statusCode = '', sessionKey = '' } = data;

            if (offline) {
                showMessage(
                    t('checkYouInternetConnection'),
                    SEVERITY.ERROR,
                    t('authError')
                );
            } else if (phoneEmailAlreadyExists(statusCode as API_STATUS)) {
                // ## email already exists
                showMessage(
                    t('emailAlreadyExistsWithAnotherUser'),
                    SEVERITY.INFO,
                    t('emailAlreadyExists')
                );
            } else if (otpGeneratedSuccessfully(statusCode as API_STATUS)) {
                // ## check for session in response
                userInfo.SessionId = sessionKey;
                userInfo.toVerify = FieldToVerify.Email;
                dispatch(setUserInfo(userInfo));

                // ## navigate to OTP Entry Screen
                router.push(
                    `?slug=${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    `${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    { locale, shallow: true }
                );
            } else if (emailIsWorkEmail(statusCode as API_STATUS)) {
                setShowWarning(true);
            } else {
                console.log('user info is ', userInfo);
                Sentry.captureMessage(
                    `Unexpected status code ${statusCode} returned from Email Sign up API. Error Message : ${message}`
                );
                let res = response;
                if (typeof res === "string") {
                    res = JSON.parse(res);
                };

                if (statusCode == API_STATUS.REQUEST_BLOCKED) {
                    showMessage(
                        t('accountBlockedVisitHelpCenter'),
                        SEVERITY.ERROR,
                        t('authError'),
                        true,
                        true
                    );
                } else {
                    showErrorMsg(message);
                }

                enabledContinueButton();

            }
        } catch (error) {
            Sentry.captureMessage(`Error occured in onEmailSignUpSuccess ${error}`);
            console.log('Error occured in onEmailSignUpSuccess ', error);
            showSomethingWentWrongError();
        }
    }

    /**
     * Method to show Error Message
     * @param message 
     */
    const showErrorMsg = (message: string) => {
        showMessage(
            message,
            SEVERITY.ERROR,
            t('authError')
        );
    }

    const onEmailSignUpError = ({ graphQLErrors: [{ message }] = [{ message: t('somethingWrong') }] }: GraphQLError) => {
        Sentry.captureMessage(`Error occured in onEmailSignUpError ${message}`);
        showErrorMsg(message);
    }

    const handleEmailSignUp = async (userInfo: UserInfo, token: string) => {
        try {
            dispatch(setUserInfo(userInfo));
            let reqParams = prepareEmailSignUpReqParams(userInfo, token);
            dispatch(setResendOtpReq(reqParams));
            userSignUp(reqParams, token, locale, userInfo, onEmailSignUpSuccess, onEmailSignUpError);
        } catch (error) {
            Sentry.captureMessage(`Error occured in handleEmailSignUp ${error}`);
            console.log('Error occured in handleEmailSignUp ', error);
            throw new Error('Error occured during sign up');
        }
    };

    const handleSocialSignUp = async (userInfo: UserInfo, token: string) => {
        try {
            console.log('inside handleSocialSignUp ', userInfo);
            const userExistsResponse: any = await fetchAPI('/api/ratify', {
                method: 'POST',
                body: JSON.stringify({
                    email: userInfo && userInfo.email ? userInfo?.email.trim() : '',
                }),
            });

            const { c: statusCode } = userExistsResponse?.data ?? { c: 0 };

            if (emailIsWorkEmail(statusCode)) {
                setShowWarning(true);
                setProcessState('complete');
                enabledContinueButton();
                return false;
            }

            dispatch(setUserInfo(userInfo));
            let reqParams;
            if (userInfo.email && userInfo.emailVerified) {
                // ## navigate to OTP Entry Screen
                console.log('Navigating to add mobile screen');
                Sentry.captureMessage("User sign up moved to add mobile");
                router.push(
                    `?slug=${SIGN_IN_SLUGS.ADD_MOBILE}`,
                    `${SIGN_IN_SLUGS.ADD_MOBILE}`,
                    { locale, shallow: true }
                );
            } else {
                reqParams = prepareSocialSignUpReqParams(userInfo, token);
                dispatch(setResendOtpReq(reqParams));
                userSignUp(reqParams, token, locale, userInfo, onEmailSignUpSuccess, onEmailSignUpError);
            }
        } catch (error) {
            Sentry.captureMessage(`Error occured in handleEmailSignUp ${error}`);
            console.log('Error occured in handleEmailSignUp ', error);
            throw new Error('Error occured during sign up');
        }
    };

    const handleGuestFlow = async (userInfo: UserInfo) => {
        try {
            const userExistsResponse: any = await fetchAPI('/api/ratify', {
                method: 'POST',
                body: JSON.stringify({
                    email: userInfo && userInfo.email ? userInfo?.email.trim() : '',
                }),
            });

            const { c: statusCode } = userExistsResponse?.data ?? { c: 0 };

            if (emailIsWorkEmail(statusCode)) {
                setShowWarning(true);
                setProcessState('complete');
                enabledContinueButton();
                return false;
            }


            if (![API_STATUS.ACCOUNT_ALREADY_EXISTS, API_STATUS.USER_DOES_NOT_EXIST].includes(userExistsResponse?.data.c)) {
                Sentry.captureMessage(`Invalid response from /api/ratify API ${userExistsResponse?.data.c}`);
                showMessage(
                    t('somethingWrong'),
                    SEVERITY.ERROR,
                    t('authError')
                );
                return false;
            }

            const userExists = checkUserExists(userExistsResponse?.data);

            if (userExists) {
                setShowGuestLoginWarning(true);
                setProcessState('complete');
                enabledContinueButton();
            } else if (!userExists) {
                const encryptedData = await fetchAPI('/api/guestInfo', {
                    method: 'POST',
                    body: JSON.stringify({
                        name: userInfo?.UserAttributes?.name || '',
                        email: userInfo?.email || '',
                    }),
                });

                await handleGuestSignIn({
                    variables: {
                        data: encryptedData?.data,
                    },
                });
            } else {
                showMessage(
                    t('somethingWrong'),
                    SEVERITY.ERROR,
                    t('authError')
                );
            }
        } catch (error) {
            Sentry.captureMessage(`Error in handleGuestFlow ${error}`);
            setProcessState('complete');
            enabledContinueButton();
            console.log('Error ', error);
        }
    };

    const getGender = () => {
        return salutation == 'mr' ? 'Male' : 'Female';
    };

    const getFormattedBirthDate = (birthdate: any) => {
        if (birthdate) {
            return `0000/${birthdate}`;
        }
        return '';
    };

    const validateDOB = (event: any) => {
        event.target.value = event.target.value.replace(/[^0-9\/]/g, '');
        let value = event.target.value;
        if (!value || !value.length) {
            setDobErrorMessage('');
            return;
        }
        if (dateMonthIsValid(value)) {
            setDobErrorMessage('');
        } else {
            setDobErrorMessage(t('enterValidDob') || '');
        }
    };

    const formatFormData = (formData: any) => {
        let localFormData = cloneDeep(formData);

        let gender = getGender();
        let formattedBirthDate = getFormattedBirthDate(formData?.birthdate);

        let userInfo: UserInfo = cloneDeep(storeUserInfo);
        if (pageFlow != PAGE_FLOW.EMAIL_SIGN_UP) {
            userInfo.email = formData.email.trim().toLowerCase();
        }
        delete localFormData.email;
        userInfo.UserAttributes = cloneDeep(localFormData);

        if (userInfo && userInfo.UserAttributes) {
            userInfo.UserAttributes.gender = gender;
            userInfo.UserAttributes.birthdate = formattedBirthDate;
            userInfo.UserAttributes.name = userInfo.UserAttributes.name.trim();
            if (
                storeUserInfo.UserAttributes?.picture &&
                storeUserInfo.UserAttributes?.picture.length
            ) {
                userInfo.UserAttributes.picture =
                    storeUserInfo.UserAttributes.picture;
            }
        }
        return userInfo;
    };

    const disabledContinueButton = () => {
        if (continueButtonRef.current) {
            continueButtonRef.current.setAttribute("disabled", "disabled");
        }
    }

    const enabledContinueButton = () => {
        if (continueButtonRef.current) {
            continueButtonRef.current.removeAttribute("disabled");
        }
    }

    const agreeAndContinue = async (
        formData: any = { birthdate: '', gender: '' }
    ) => {
        try {
            disabledContinueButton();
            // ## show loading indicator
            setProcessState('inProcess');

            const captchaBackgroundClickHandler = () => {
                console.log('BG click');
                setProcessState('complete');
            };

            const domObserver = new MutationObserver(() => {
                const iframe = document.querySelector(
                    'iframe[src^="https://www.google.com/recaptcha"][src*="bframe"]'
                );
                console.log('iframe is ', iframe);
                if (iframe) {
                    domObserver.disconnect();

                    const captchaBackground =
                        iframe.parentNode?.parentNode?.firstChild;
                    captchaBackground?.addEventListener(
                        'click',
                        captchaBackgroundClickHandler
                    );
                }
            });

            domObserver.observe(document.documentElement || document.body, {
                childList: true,
                subtree: true,
            });

            console.log('Form data ', formData);

            // ## validate the form field data
            const formValid = await validateFormFields(formData);

            if (!formValid) {
                // ## if form validation fails stop the loading animation and return
                setProcessState('complete');
                enabledContinueButton();
                return;
            }
            // ## proceed if client side form validation is OK

            setUserFormData(formData);
            if (pageFlow == PAGE_FLOW.GUEST) {
                setProcessState('inProcess');
                let userInfo = formatFormData(formData);
                await handleGuestFlow(userInfo);
            } else if (isCaptchaRequired) {
                console.log('Captcha required , executing captcha .....');
                executeCaptcha(captchaContext['elem']);
            } else {
                setProcessState('inProcess');
                await proceedWithoutCaptcha(formData)
            }
        } catch (error: any) {
            console.log('error occured in agreeAndContinue :: ', error);
            Sentry.captureMessage(`Error in agreeAndContinue ${error}`);
            if (
                /InvalidPasswordException: provided.password.cannot.be.used.for.security.reasons/gi.test(
                    String(error)
                )
            ) {
                showMessage(
                    t('passwordCannotBeUsed'),
                    SEVERITY.ERROR,
                    t('authError')
                );
            } else {
                showMessage(
                    error ? error.toString() : '',
                    SEVERITY.ERROR,
                    t('error')
                );
            }
            setProcessState('complete');
            enabledContinueButton();
        }
    };

    /**
     * Salutation select menu change
     * @param event : select change
     */
    const handleChange = (event: SelectChangeEvent) => {
        setSalutation(event.target.value as string);
    };

    /**
     * Support both arabic and english text in name field
     * Prevent numeric values, special char in onPaste and onChange
     */
    const handleArabicEnglishText = (event: any) => {
        console.log('event ', event);
        event.target.value = event.target.value.replace(
            /[&\/\#,+()$~%.'":@^*?!<>{}\d-\=_]/g,
            ''
        );
        setValue('name', event.target.value);
        if (event.target.value.trim().length > 1) {
            setNameErrorMessage('');
        } else setNameErrorMessage(t('minMaxLengthForNameRequired') || '');

        if (hasSpecialCharacter(event.target.value.trim())) {
            setNameErrorMessage(t('specialCharactersNotAllowedInName') || '');
        }
    };

    const disableEmailField = () => {
        return [
            PAGE_FLOW.SOCIAL_SIGN_UP_WITH_EMAIL,
            PAGE_FLOW.EMAIL_SIGN_UP,
        ].includes(pageFlow);
    };

    const dobKeyDownEvent = (event: any) => {
        if (event.keyCode == 8) return;
        let value = event.target.value;
        if (value.length == 2 && event.keyCode != 191) {
            event.target.value = `${value}/`;
        }
    };

    const handleEmailOTPSignIn = async (token: string) => {
        try {
            const email = getValues()['email'].toLowerCase();
            setProcessState('inProcess');

            let cognitoCustomQuery = signInWithMobile(email.trim());

            let reqParams = cloneDeep(storeUserInfo);
            reqParams.email = email.trim();

            let signInResponse = await generateLoginOTP(
                reqParams,
                'email',
                token,
                locale
            );

            if (
                signInResponse &&
                signInResponse?.STATUS_CODE == API_STATUS.OTP_GENERATED
            ) {
                // ## handle email OTP sign in process
                let cognitoCustomResponse = await cognitoCustomQuery;

                dispatch(setCognitoCustomResponse(cognitoCustomResponse));
                dispatch(setPageFlow(PAGE_FLOW.CUSTOM_SIGN_IN));
                let info = cloneDeep(storeUserInfo);
                info.toVerify = FieldToVerify.Email;
                info.SessionId = signInResponse.SESSION_KEY;
                info.email = email;
                info.phone_number = signInResponse?.MFA_VALUES?.phone_number;
                dispatch(setUserInfo(info));
                dispatch(
                    setResendOtpReq({
                        type: 'email',
                        token: token,
                    })
                );
                router.push(
                    `?slug=${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    `${SIGN_IN_SLUGS.VERIFY_OTP}`,
                    { locale, shallow: true }
                );
            } else {
                // ## limit exceeded or Captcha failed
                let res = signInResponse;
                if (typeof res === "string") {
                    res = JSON.parse(res);
                };
                showMessage(
                    res?.MESSAGE,
                    SEVERITY.ERROR,
                    t('authError')
                );
            }
            setProcessState('complete');
        } catch (error) {
            Sentry.captureMessage(`Error in handleEmailOTPSignIn ${error}`);
            console.log('Error occured in handleEmailOTPSignIn ', error);
        }
    };

    const handleOnConfirm = async (confirm: boolean) => {
        try {
            setShowGuestLoginWarning(false);
            if (confirm) {
                dispatch(setPageFlow(PAGE_FLOW.CUSTOM_SIGN_IN));
                setSignInConfirmClick(true)
            }
        } catch (error) { }
    };

    const closePopUp = () => {
        setShowWarning(false);
        router.push('/login/', undefined, { locale });
    }

    const getEmailPlaceHolderText = () => {
        return pageFlow == PAGE_FLOW.GUEST ? t('enterPersonalEmail') : "<EMAIL>";
    }

    return (
        <>
            <div className={`add-info ${styles['signup-add-info']}`}>
                {/* <h3>{t('addYourInfo')}</h3> */}
                <TitleHeader title={t('getStarted')} subTitle={t('addYourInfo')} logo={logo} platformType={platformType} />
                <form onSubmit={handleSubmit(agreeAndContinue)}>
                    <ul>
                        <li className={`${nameErrorMessage ? 'error-outline' : ''}`}>
                            <FormControl variant="standard" fullWidth={true}>
                                <div
                                    className={` ${pageFlow !== PAGE_FLOW.GUEST
                                        ? styles['bind-form-element']
                                        : styles['guest-name-element']
                                        }`}
                                >
                                    {pageFlow != PAGE_FLOW.GUEST && (
                                        <div
                                            className={
                                                styles[
                                                'bind-form-element__select'
                                                ]
                                            }
                                        >
                                            <Image height={16} width={16} className={styles['bind-form-element__icon']} alt='arrow' src={`${imageBaseUrl}/images/arrow-down.svg`} />
                                            <Select
                                                id="salutation"
                                                value={salutation}
                                                onChange={handleChange}
                                                className={
                                                    styles[
                                                    'salutation-dropdown'
                                                    ]
                                                }
                                                MenuProps={{
                                                    classes: {
                                                        paper: `paper-signin-dropdown ${styles['name-select-menu']}`,
                                                    },
                                                }}
                                            >
                                                <MenuItem value="mr">
                                                    {t('mr')}
                                                </MenuItem>
                                                <MenuItem value="ms">
                                                    {t('ms')}
                                                </MenuItem>
                                            </Select>
                                        </div>
                                    )}
                                    <TextField
                                        {...register('name')}
                                        autoFocus={true}
                                        id="name"
                                        name="name"
                                        type="text"
                                        variant="standard"
                                        placeholder={t('name') || ''}
                                        onChange={handleArabicEnglishText}
                                        className={styles['flex-1']}
                                        inputProps={{
                                            maxLength: 26,
                                        }}
                                    />
                                </div>
                            </FormControl>

                        </li>
                        {
                            nameErrorMessage && nameErrorMessage.length && <FormHelperText
                                error={Boolean(nameErrorMessage)}
                                className={
                                    styles[
                                    'phone-verification__form-input-validation'
                                    ]
                                }
                            >
                                {nameErrorMessage}
                            </FormHelperText>
                        }

                        <li className={`${emailErrorMessage ? 'error-outline' : ''}`}>

                            <FormControl variant="standard" fullWidth={true}>
                                {/* <InputLabel shrink={true} id="email">
                                    {t('emailText')} (
                                    {t('pleaseEnterYourPersonalEmail')})
                                </InputLabel> */}
                                <div className={styles['input-with-icon']}>
                                    <Image width={24} height={24} alt='email' src={`${imageBaseUrl}/icons/sms.svg`} />
                                    <TextField
                                        id="email"
                                        type="text"
                                        variant="standard"
                                        className={styles['flex-1']}
                                        placeholder={getEmailPlaceHolderText()}
                                        {...register('email')}
                                        onChange={(event) => {
                                            setEmailErrorMessage('');
                                            setValue('email', event.target.value);
                                        }}
                                        name="email"
                                        disabled={disableEmailField()}
                                        inputProps={{ maxLength: 150 }}
                                    />
                                </div>
                            </FormControl>
                            
                        </li>
                        {
                            emailErrorMessage && emailErrorMessage.length && 
                            <FormHelperText
                            error={Boolean(emailErrorMessage)}
                            className={
                                styles[
                                'phone-verification__form-input-validation'
                                ]
                            }
                        >
                            {emailErrorMessage}
                        </FormHelperText>
                        }
                       

                        {pageFlow != PAGE_FLOW.GUEST &&
                            pageFlow != PAGE_FLOW.CUSTOM_SIGN_IN && (
                                <Fragment>
                                    <li className={`${styles['datepicker']} ${dobErrorMessage ? 'error-outline' : ''}`}>
                                    <FormControl
                                        variant="standard"
                                        fullWidth={true}
                                    >
                                        {/* <InputLabel shrink={true} id="birthday">
                                            {t('birthdayOptional')}
                                        </InputLabel> */}
                                        <div className={styles['input-with-icon']}>
                                            <Image width={24} height={24} alt='email' src={`${imageBaseUrl}/icons/calendar.svg`} />

                                            <TextField
                                                id="birthdate"
                                                type="text"
                                                variant="standard"
                                                placeholder="Birthday(Optional)"
                                                {...register('birthdate')}
                                                onChange={validateDOB}
                                                onKeyDown={dobKeyDownEvent}
                                                name="birthdate"
                                                inputProps={{
                                                    maxLength: 5,
                                                    inputmode: 'numeric',
                                                }}
                                            />
                                        </div>

                                    </FormControl>
                                    
                                </li>
                                
                                <FormHelperText
                                error={Boolean(dobErrorMessage)}
                                className={
                                    styles[
                                    'phone-verification__form-input-validation'
                                    ]
                                }
                            >
                                {dobErrorMessage}
                            </FormHelperText>
                                </Fragment>
                            )
                        }


                    </ul>

                    <div className={`button-container ${styles['btn-container']}`}>
                        <Button
                            attribues={{ ref: continueButtonRef }}
                            theme="purple"
                            className={`submit-button ${styles['btnAgree']} ${processState === 'inProcess'
                                ? `button-submitted`
                                : ''
                                }`}
                            loadingState={processState}
                        >
                            {pageFlow == PAGE_FLOW.GUEST
                                ? t('continueAsGuest')
                                : t('agreeAndContinue')}
                        </Button>
                    </div>

                    <div className={'white-divider'}></div>



                    {platformType !== PLATFORM_TYPE.APP.toLowerCase() && (
                        <div className={styles['help-text-wrapper']}>
                            <HelpText />
                        </div>

                    )}
                </form>
                
                {platformType === PLATFORM_TYPE.APP.toLowerCase() ? (
                    <div className={styles['help-text-container']}>
                        <div className={styles.agreement}>
                            <span className={styles['-continue-to-agree']}>
                                {t('byContinuingYouAgreeToThe')}&nbsp;
                                <span className={styles['text-style-1']}></span>
                                <span className={styles['text-style-2']}>
                                    <a
                                        href={`${platformType === PLATFORM_TYPE.MWEB
                                            ? defaultMwebRedirectUrl
                                            : defaultRedirectUrl
                                            }/${locale}-sa/terms-of-use`}
                                    >
                                        {t('termsOfUse')}{' '}
                                    </a>
                                </span>
                                <span className={styles['text-style-3']}></span>
                                {t('andText')}
                                <span className={styles['text-style-4']}></span>
                                <span className={styles['text-style-2']}>
                                    <a
                                        href={`${platformType === PLATFORM_TYPE.MWEB
                                            ? defaultMwebRedirectUrl
                                            : defaultRedirectUrl
                                            }/${locale}-sa/privacy-policy`}
                                    >
                                        &nbsp;{t('privacyPolicy')}
                                    </a>
                                </span>
                            </span>
                        </div>
                    </div>
                ) : (
                    <></>
                )}
                
            </div>
            <Confirm
                open={showGuestLoginWarning}
                onClose={handleOnConfirm}
                message={t('guestUserWithEmailFound')}
                title={t('accountAlreadyExists') || ''}
                confirmText={t('proceed') || ''}
            ></Confirm>
            <Warning open={showWarning} onClose={closePopUp} />

        </>
    );
};

export default ResponseInfoHOC(AddInfo);
