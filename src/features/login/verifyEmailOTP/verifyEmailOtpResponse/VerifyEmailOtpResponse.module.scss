@import '@styles/variables';
@import '@styles/mixins';

.email-otp-confirmation {
    &__list {
        list-style: none;
        padding: 0;
        margin: 66px 0 25px;

        @media only screen and (max-width: $sm) {
            margin: 24px 0;
        }

        &-item {
            padding-bottom: 30px;

            .email-number-otp {
                margin-top: 10px;

                input {
                    width: 70px;
                    height: 23px;
                    margin: 10px 448px 10px 0;
                    font-family: Poppins;
                    font-size: 16px;
                    font-weight: 500;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: normal;
                    letter-spacing: normal;
                    text-align: left;
                    color: $dark-purple;

                    @media only screen and (max-width: $sm) {
                        margin: 10px 0;
                    }

                    @include rtl-styles {
                        margin: 0;
                        text-align: right;
                    }
                }
            }
        }
    }

    &__time-left {
        line-height: 27px;
        text-align: center;
        @include font-size(14);

        @media only screen and (max-width: $sm) {
            margin: 20px 0 40px;
        }

        span,
        a {
            color: $barney-purple;
            cursor: pointer;
        }

        span {
            display: inline-block;
            width: 45px;
        }
    }
}
