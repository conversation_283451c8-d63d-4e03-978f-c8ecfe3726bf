import React, { useEffect, useState } from 'react';
import { FormControl, InputLabel, TextField } from '@mui/material';
import styles from './VerifyEmailOtpResponse.module.scss';
import { useTranslation } from 'next-i18next';
import HelpText from '@features/signinSignup/common/helpText/HelpText';
import Button from '@features/common/button/Button';
import * as CognitoAPI from '@features/common/signInSignUpAPI';
import isNumber from '@utils/isNumber';
import { useAppSelector } from '@redux/hooks';
import { getRdir } from '@features/login/loginFlowSlice';
import useAppRouter from '@features/common/router.context';
import getConfig from 'next/config';

interface VerifyOtpResponseProps {
    accessToken?: string;
    userInfo?: any;
    referenceId?: any;
    sentStatus?: any;
}

const VerifyOtpResponse = ({
    accessToken,
    userInfo,
    sentStatus,
    ...props
}: VerifyOtpResponseProps) => {
    // Translation
    const { t } = useTranslation('common');

    const {
        publicRuntimeConfig: { defaultRedirectUrl },
    } = getConfig();

    const {
        state: { locale, regionUrl },
        router,
    } = useAppRouter();

    // Otp value
    const [otpValue, setOtpValue] = useState('');
    // reference id
    const [referenceId, setReferenceId] = useState(props.referenceId);
    // button status
    const [buttonDisabled, setButtonDisabled] = useState<boolean>(true);
    const [processState, setProcessState] = useState<'inProcess' | 'complete'>(
        'complete'
    );

    const rdir = useAppSelector(getRdir);

    // counter status
    const [startCounter, setStartCounter] = useState(true);

    // otp status
    const [otpStatus, setOtpStatus] = useState<
        'invalid' | 'valid' | 'none' | 'expired'
    >('none');

    // timer set
    const [time, setTime] = useState(0);

    const generateOTP = async () => {
        if (accessToken && userInfo?.email) {
            let response = await CognitoAPI.emailVerification(
                userInfo?.email,
                accessToken
            );
            if (response && response.status == '24200') {
                setReferenceId(response.reference_id);
            }
        }
    };
    // counting time
    const RESET_INTERVAL_S = 60; // 120s = 2m * 60s/m

    // total timer if any change in timer change here
    useEffect(() => {
        if (time >= RESET_INTERVAL_S) {
            setStartCounter(false);
            setTime(0);
        }
        if (time == 0) {
            setButtonDisabled(true);
        }
    }, [time]);

    /**
     * Formatting time
     * @param time : Time
     * @returns : Formatted remaining time
     */
    const formatTime = (time: number) =>
        `${String(Math.floor(time / 60)).padStart(2, '0')}:${String(
            time % 60
        ).padStart(2, '0')}`;

    const Timer = ({ time }: { time: number }) => {
        const timeRemain = RESET_INTERVAL_S - (time % RESET_INTERVAL_S);

        return <span>{formatTime(timeRemain)}</span>;
    };

    /**
     * Button Status on input text change
     */
    const inputOnChange = (e: any) => {
        let otp = e.target.value;
        clearOtpErrorMsg();
        const re = /^[0-9\b]+$/;

        // if value is not blank, then test the regex

        if (otp === '' || re.test(otp)) {
            setOtpValue(otp);
            if (otp.length == 6 && time > 0) {
                setButtonDisabled(false);
            } else {
                setButtonDisabled(true);
            }
        } else {
            e.target.value = '';
        }
    };

    const clearOtpErrorMsg = () => {
        setOtpStatus('none');
    };

    /**
     *
     */
    const handleVerifyEmailOtp = async () => {
        try {
            setButtonDisabled(true);
            setProcessState('inProcess');
            clearOtpErrorMsg();
            if (accessToken && referenceId && otpValue) {
                const payload = {
                    otp: otpValue,
                    reference_id: referenceId,
                };
                let response = await CognitoAPI.emailVerificationOtp(
                    payload,
                    accessToken
                );
                if (response && response.status == '24002') {
                    let lrDir = localStorage.getItem('rdir');
                    if (rdir && rdir.length) {
                        router.push(rdir, undefined, { locale });
                    } else if (lrDir) {
                        router.push(lrDir, undefined, { locale });
                    } else {
                        router.push({
                            pathname: defaultRedirectUrl,
                        });
                    }
                } else {
                    if (time == 0) {
                        setOtpStatus('expired');
                    } else {
                        setOtpStatus('invalid');
                    }
                    setProcessState('complete');
                    setButtonDisabled(false);
                }
            }
        } catch (error) {
            console.log('Error occured in handleVerifyEmailOtp :: ', error);
            setProcessState('complete');
            setButtonDisabled(false);
        }
    };

    /**
     * Method to handle send OTP again
     */
    const sendAgain = async () => {
        generateOTP();
        sentStatus('resent');
        setStartCounter(true);
        setTime(0);
    };

    /**
     * Time interval
     * @param props : time
     * @returns : timer
     */
    const IntervalTimer = (props: any) => {
        const { time, setTime } = props;
        useEffect(() => {
            const timerId = setInterval(() => {
                setTime((t: any) => t + 1);
            }, 1000);
            return () => clearInterval(timerId);
        }, [setTime]);

        return <Timer time={time} />;
    };

    /**
     * Method to get Error message as per the OTP status
     * @returns
     */
    const getErrorMessage = () => {
        if (otpStatus == 'invalid') {
            return t('invalidOTP');
        } else if (otpStatus == 'expired') {
            return t('expiredOTP');
        } else {
            return '';
        }
    };

    return (
        <div className={styles['email-otp-confirmation__list']}>
            <ul className={styles['email-otp-confirmation__list']}>
                <li className={styles['email-otp-confirmation__list-item']}>
                    <FormControl variant="standard" fullWidth={true}>
                        <InputLabel shrink={true} id="otp">
                            {t('verificationCodeLabel')}
                        </InputLabel>
                        <TextField
                            id="otp"
                            variant="standard"
                            className={styles['email-number-otp']}
                            inputProps={{ inputMode: 'numeric' }}
                            onInput={(e: any) => {
                                e.target.value = e.target.value
                                    .toString()
                                    .slice(0, 6);
                            }}
                            onChange={inputOnChange}
                            onKeyPress={(event: any) => {
                                if (!isNumber(event)) {
                                    event.preventDefault();
                                }
                                if (event.key === 'Enter') {
                                    event.preventDefault();
                                    if (!buttonDisabled) handleVerifyEmailOtp();
                                }
                            }}
                            error={
                                otpStatus == 'expired' || otpStatus == 'invalid'
                            }
                            helperText={getErrorMessage()}
                        />
                    </FormControl>
                </li>
                <li
                    className={`button-container ${styles['email-otp-confirmation__list-item']}`}
                >
                    <Button
                        theme="purple"
                        action={handleVerifyEmailOtp}
                        className={` submit-button ${styles['continue-button']}
								${processState === 'inProcess' ? `button-submitted` : ''}
							`}
                        attribues={{
                            disabled: buttonDisabled,
                            type: 'button',
                        }}
                    >
                        {t('continue')}
                    </Button>
                </li>
            </ul>

            <p className={styles['email-otp-confirmation__time-left']}>
                {startCounter ? (
                    <>
                        {t('timeLeftToEnterTheCodeReceived')} -{' '}
                        <IntervalTimer time={time} setTime={setTime} />
                    </>
                ) : (
                    <>
                        {t('didntReceiveTheOTP')}?{' '}
                        <a onClick={sendAgain}>{t('sendAgain')}</a>
                    </>
                )}
            </p>
            <HelpText />
        </div>
    );
};

export default VerifyOtpResponse;
