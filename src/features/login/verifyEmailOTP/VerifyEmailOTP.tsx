import { useState } from 'react';
import VerifyEmailOtpResponse from './verifyEmailOtpResponse/VerifyEmailOtpResponse';
import styles from './VerifyEmailOTP.module.scss';
import maskText from 'utils/maskText';
import { useTranslation } from 'next-i18next';

interface VerifyEmailOTPProps {
    accessToken?: string;
    userInfo?: any;
    referenceId?: any;
}

const VerifyEmailOTP = ({
    accessToken,
    userInfo,
    referenceId,
}: VerifyEmailOTPProps) => {
    // Translation
    const { t } = useTranslation('common');

    // otp sent status
    const [otpSentStatus, setOtpSentStatus] = useState<'sent' | 'resent'>(
        'sent'
    );

    /**
     * Method to handle static content according to otp send and resend status
     */
    const sentStatus = (status: any) => {
        setOtpSentStatus(status);
    };

    //mask email
    const maskedEmail = maskText(userInfo?.email, 'email') || '';

    return (
        <div className={styles['email-verification-code']}>
            <h3>{t('enterVerificationCode')}</h3>
            <p className={styles['email-verification-code__info']}>
                {otpSentStatus == 'sent'
                    ? t('weHaveSentVerificationCodeToEmail')
                    : t('weHaveReSentVerificationCodeToEmail')}
                <span>
                    &nbsp;
                    <br className="mb-hide" />
                </span>

                <span dir="ltr">{maskedEmail}</span>
            </p>

            <VerifyEmailOtpResponse
                accessToken={accessToken}
                referenceId={referenceId}
                userInfo={userInfo}
                sentStatus={sentStatus}
            />
        </div>
    );
};

export default VerifyEmailOTP;
