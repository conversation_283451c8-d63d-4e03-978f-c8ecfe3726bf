import getDirection from '@utils/getDirection';
import { useState, useEffect, Fragment } from 'react';
import styles from './loginLayout.module.scss';
import { useTranslation } from 'next-i18next';
import getConfig from 'next/config';
import { useAppDispatch } from '@redux/hooks';
import { setRdir, setPlatform } from '../loginFlowSlice';
import Link from 'next/link';
import useAppRouter from '@features/common/router.context';
import { PLATFORM_TYPE, SIGN_IN_SLUGS } from '@constants/common';
import Image from 'next/image';
import { useRouter } from 'next/router';
import useMediaQuery from '@mui/material/useMediaQuery';
import VerifyOTP from '../verifyOTP/VerifyOTP';
import AddInfo from '../addInfo/AddInfo';
import EmailLogin from '../emailLogin/EmailLogin';
import SignIn from '../signIn/signIn';
import dynamic from 'next/dynamic';
import HelpText from '@features/signinSignup/common/helpText/HelpText';
import Head from 'next/head';
import { getStoreName } from '@utils/getStoreName';
import * as Sentry from '@sentry/nextjs';
import ReCAPTCHA from 'react-google-recaptcha';
import { CountryCode } from 'libphonenumber-js/types';
import ThemeSeter from '@features/common/themeSetter/ThemeSetter';
import PhoneVerification from '../phoneVerification/PhoneVerification';
import { headerInterface, CaptchaConfig } from '@interfaces/common.inteface';
import BusinessLogin from '../businessLogin/BusinessLogin';
import BusinessEmail from '../businessEmail/BusinessEmail';
import RewardsLogin from '../rewardsLogin/RewardsLogin';
import RewardsVerifyOTP from '../rewardsVerifyOTP/RewardsVerifyOTP';
import RewardsVerifyOtpResponse from '../rewardsVerifyOTP/rewardsVerifyOtpResponse/RewardsVerifyOtpResponse';

const Loader = dynamic(() => import('@features/common/loader/Loader'));

const VerifyEmailOTP = dynamic(
    () => import('../verifyEmailOTP/VerifyEmailOTP'),
    {
        loading: () => <Loader />,
        ssr: false,
    }
);

// taking public image config url
const {
    publicRuntimeConfig: {
        imageBaseUrl,
        defaultRedirectUrl,
        defaultMwebRedirectUrl,
        businessLoginUrl,
    },
} = getConfig();

/**
 * @component BackgroundImagePreload
 * @description Preloads the background image for the login page
 * @returns {JSX.Element}
 */
const BackgroundImagePreload = () => {
    return (
        <Head>
            <link
                rel="preload"
                href={`${imageBaseUrl}/images/loginBg.png`}
                as="image"
                type="image/png"
            />
              <link
                rel="preload"
                href={`${imageBaseUrl}/images/loginBgMweb.png`}
                as="image"
                type="image/png"
            />
        </Head>
    );
};

const LoginLayout = ({
    headerData,
    slug,
    info,
    redirectUrl,
    accessToken,
    userInfo,
    referenceId,
    prefillInfo,
    guestSessionExists,
    isGuestEnabled,
    ipCountry,
    blacklistedCountries,
    webauthnEnabled,
}: {
    headerData?: headerInterface;
    slug: string;
    info: any;
    redirectUrl: string;
    accessToken?: string;
    referenceId?: string;
    userInfo?: any;
    prefillInfo?: any;
    guestSessionExists: boolean;
    isGuestEnabled: boolean;
    ipCountry?: CountryCode;
    blacklistedCountries?: [];
    webauthnEnabled?: any;
}) => {
    // Translations
    const { t } = useTranslation('common');

    const titleStripContent = Array(36).fill([t('welldone'), t('egiftcards'), t('goodjob'), t('teachersdays'), t('groupgifting'), t('sendatip'), t('bulkgifts'), t('extravalue'), t('offers'), t('promocodes'), t('anniversary'), t('wedding')]).flat();


    const {
        state: { region, locale },
        redirect,
        router,
    } = useAppRouter();

    const closeIcon = `${imageBaseUrl}/icons/close.svg`;
    const logoImage = `${imageBaseUrl}/images/ygg-logo.svg`;
    const businessLogin = `${imageBaseUrl}/images/subtract.svg`;
    const businessLoginAr = `${imageBaseUrl}/images/sub-arabic.svg`;


    
    // #. Logo
    const logoData = headerData?.headers?.edges[1]?.node?.logo || logoImage;


    // #. Get the query  info from the router
    const { query } = useRouter();

    let platform: any = query.platform || localStorage.getItem('platform');
    let deviceType: any =
        query.device_type || localStorage.getItem('deviceType');
    let authSession: any =
        query.auth_session || localStorage.getItem('authSession');

    let store: any = query.store;
    const isWeb = useMediaQuery('(min-width:768px)');

    // direction
    const direction = getDirection(locale);

    const [currentRegionCode, setCurrentRegionCode] = useState<string>(region);

    const [showLoader, setShowLoader] = useState<boolean>(false);

    const [email, setEmail] = useState<string>('');

    const [platformType, setPlatformType] = useState<any>('');

    const [rdirLocal, setRdirLocal] = useState<any>('');

    const [isOtpScreen, setOtpScreen] = useState<boolean>(false);

    const isMainLoginScreen = [SIGN_IN_SLUGS.MOBILE_LOGIN, ''].includes(slug);

    const dispatch = useAppDispatch();
    useEffect(() => {
        store && getStoreName(store);
    }, [store]);

    useEffect(() => {
        if (info && info.email) setEmail(info.email);
    }, [info]);

    useEffect(() => {
        if (redirectUrl && redirectUrl.length) {
            dispatch(setRdir(redirectUrl));
            localStorage.setItem('rdir', redirectUrl);
        }
    }, [redirectUrl]);

    /**
     * Switching language
     * please keep commented switch language method for next phase
     */
    const switchLanguage = () => {
        setOtpScreen(false);
        setShowLoader(true);
        const switchLanguage = router?.locale === 'en' ? 'ar' : 'en';
        let loadedRegionCode = region ? region : currentRegionCode;
        pushURL(loadedRegionCode, switchLanguage);
    };

    /**
     * url redirection with param
     * @param region : Selected Region
     * @param path : redirection path ( current path)
     * @param locale : locale
     */
    const pushURL = (region: string, locale: string | undefined) => {
        redirect(region, locale);
        router.events.on('routeChangeComplete', () => {
            router.reload();
        });
    };

    // #. Handle platform type from local storage
    useEffect(() => {
        platform = query.platform || localStorage.getItem('platform');
        deviceType = query.device_type || localStorage.getItem('deviceType');
        authSession = query.auth_session || localStorage.getItem('authSession');

        if (slug == '') {
            let platformByWidth = PLATFORM_TYPE.WEB.toLowerCase();
            if (platform && platform === PLATFORM_TYPE.APP.toLowerCase()) {
                dispatch(setPlatform(platform));
                localStorage.setItem('platform', platform);
                if (
                    (deviceType && deviceType.length && deviceType != 'null') ||
                    deviceType != null
                ) {
                    localStorage.setItem('deviceType', deviceType);
                }
                if (
                    authSession &&
                    authSession.length &&
                    authSession != 'null' &&
                    authSession != null
                ) {
                    localStorage.setItem('authSession', authSession);
                } else {
                    localStorage.removeItem('authSession');
                }
            } else {
                if (!isWeb) {
                    platformByWidth = PLATFORM_TYPE.MWEB.toLowerCase();
                }
                dispatch(setPlatform(platformByWidth));
                localStorage.setItem('platform', platformByWidth);
            }
            const localStoragePlatform = localStorage.getItem('platform');
            const localStorageRdir = localStorage.getItem('rdir');
            setRdirLocal(localStorageRdir);
            setPlatformType(localStoragePlatform);
        } else if (slug == 'add-info' || slug == 'add-mobile') {
            const localStoragePlatform = localStorage.getItem('platform');
            const localStorageRdir = localStorage.getItem('rdir');
            dispatch(setPlatform(localStoragePlatform));
            setRdirLocal(localStorageRdir);
            setPlatformType(localStoragePlatform);
        }
    }, [slug]);

    /**
     * Method to handle business login
     * @returns
     */
    const handleBusinessLogin = () => {
        window.location.href = `${businessLoginUrl}/${locale}/account/login`;
        return false;
    };

    const backgroundImage = !isWeb ? `${imageBaseUrl}/images/loginBgMweb.png` : `${imageBaseUrl}/images/loginBg.png`;

    return (
        <>
            <BackgroundImagePreload />
            <style jsx>{`
                iframe[id='launcher'] {
                    display: none !important;
                }
                .background-image {
                background-image: url(${backgroundImage});
                background-repeat: no-repeat;
                background-position: center;
                background-size: cover;
                }
                
            `}</style>
            {![PLATFORM_TYPE.APP.toLowerCase(), PLATFORM_TYPE.MWEB.toLowerCase()].includes(platformType) &&
                [SIGN_IN_SLUGS.MOBILE_LOGIN, ''].includes(slug) && (
                    <div
                        className={styles['language-switch']}
                        onClick={switchLanguage}
                    >
                        <span className={styles['language-switch__text']}>
                            {t('switchLanguage')}
                        </span>
                        <Image
                            src={`${imageBaseUrl}/images/global.svg`}
                            alt="language switch"
                            height={24}
                            width={24}
                        />
                    </div>
                )}
            <div
                className={`background-image ${styles['main']} ${isMainLoginScreen && styles['main-login']
                    }`}
            >
                {/* <div className={styles['bg-circle-top-left']}></div> */}
                {/* <div className={styles['bg-circle-bottom-right']}></div> */}
                {/* <div className={styles['bg-title-strip']}>
                    {titleStripContent &&
                        titleStripContent.length > 0 &&
                        titleStripContent.map((title) => {
                            return (
                                <>

                                    <span
                                        className={
                                            styles['bg-title-strip__title']
                                        }
                                    >
                                        {title}
                                    </span>
                                    <span
                                        className={
                                            styles[
                                            'bg-title-strip__title-separator'
                                            ]
                                        }
                                    >
                                        .
                                    </span>


                                </>
                            );
                        })}
                </div> */}


                <div className={styles['container']}>
                    
                    <div className={styles['content']}>

                {slug == SIGN_IN_SLUGS.BUSINESS_LOGIN &&
                    <div className={styles["back-container"]}>
                    <img src={`${imageBaseUrl}/icons/arrow-left.svg`} alt="" onClick={router.back}/>
                    <span>{t("backtoLogin")}</span>                            
                </div> }
                            

                    {/* {![PLATFORM_TYPE.APP, PLATFORM_TYPE.MWEB].includes(
                        platformType.toUpperCase()
                    ) &&
                        slug == '' && (
                            <div
                                className={styles['business-login']}
                                onClick={handleBusinessLogin}
                            >
                                <div className={styles['business-login__content']}>
                                    <img src={locale == "en" ? businessLogin : businessLoginAr} />

                                    <span>{t('businessLogin')}</span>
                                    <img
                                        src={`${imageBaseUrl}/images/vector.svg`}
                                        className={styles['arrow']}
                                    ></img>
                                </div>




                            </div>
                        )} */}
                    {
                        platformType !== PLATFORM_TYPE.APP.toLowerCase() &&
                            platformType === PLATFORM_TYPE.MWEB.toLowerCase() ? (
                            <div className={styles['top-container']}>
                                <Link
                                    href={
                                        rdirLocal
                                            ? rdirLocal
                                            : defaultMwebRedirectUrl
                                    }
                                >
                                    <Image
                                        src={closeIcon}
                                        width={16}
                                        height={16}
                                        alt="close"
                                    />
                                </Link>
                            </div>
                        ) : (
                            ''
                        )
                    }

{platformType == PLATFORM_TYPE.MWEB.toLowerCase() &&
                [SIGN_IN_SLUGS.MOBILE_LOGIN, ''].includes(slug) && (
                    <div
                        className={styles['language-switch']}
                        onClick={switchLanguage}
                    >
                        <span className={styles['language-switch__text']}>
                            {t('switchLanguage')}
                        </span>
                        <Image
                            src={`${imageBaseUrl}/images/global.svg`}
                            alt="language switch"
                            height={24}
                            width={24}
                        />
                    </div>
                )}
                        <div
                            className={`signin-signup ${styles['signin-signup']}`}
                        >
                            <div className={styles['content-panel']}>
                                {[SIGN_IN_SLUGS.MOBILE_LOGIN, ''].includes(
                                    slug
                                ) && (
                                        <SignIn
                                            platformType={platformType}
                                            setOtpScreen={setOtpScreen}
                                            isOtpScreen={isOtpScreen}
                                            prefillInfo={prefillInfo}
                                            guestSessionExists={guestSessionExists}
                                            isGuestEnabled={isGuestEnabled}
                                            logo={logoData}
                                        />
                                       
                                    
                                    )}
                                {slug == SIGN_IN_SLUGS.VERIFY_OTP && (
                                    <VerifyOTP platformType={platformType} logo={logoData} />
                                )}
                                {slug == SIGN_IN_SLUGS.EMAIL_LOGIN &&
                                    prefillInfo && (
                                        <EmailLogin
                                            platformType={platformType}
                                            prefillInfo={prefillInfo}
                                        />
                                    )}
                                {slug == SIGN_IN_SLUGS.ADD_INFO && (
                                    <AddInfo
                                        email={email}
                                        platformType={platformType}
                                        logo={logoData}
                                    />
                                )}
                                {slug == SIGN_IN_SLUGS.ADD_MOBILE && (
                                    <PhoneVerification
                                        ipCountry={ipCountry}
                                        blacklistedCountries={
                                            blacklistedCountries
                                        }
                                        platformType={platformType}
                                        logo={logoData}
                                    />
                                )}

                                {slug === SIGN_IN_SLUGS.EMAIL_VERIFICATION && (
                                    <VerifyEmailOTP
                                        userInfo={userInfo}
                                        accessToken={accessToken}
                                        referenceId={referenceId}
                                    />
                                )}

                                {slug == SIGN_IN_SLUGS.BUSINESS_LOGIN && (
                                 
                                    <BusinessEmail platformType={platformType}/>
                                 )}   

                                 {slug == SIGN_IN_SLUGS.REWARDS_LOGIN && (
                                 
                                 
                                <RewardsLogin/>
                              )}             
                            </div>    
                        </div>
                    </div>

                    {[SIGN_IN_SLUGS.MOBILE_LOGIN, ''].includes(slug) && platformType === PLATFORM_TYPE.WEB.toLowerCase() && (
                        <BusinessLogin/>
                    )}


                    {/* {platformType === PLATFORM_TYPE.WEB.toLowerCase() && (
                        <div className={styles.footer}>
                            <span className={styles['-continue-to-agree']}>
                                {t('byContinuingYouAgreeToThe')}&nbsp;
                                <span className={styles['text-style-1']}></span>
                                <span className={styles['text-style-2']}>
                                    <a
                                        href={`${platformType === PLATFORM_TYPE.MWEB
                                            ? defaultMwebRedirectUrl
                                            : defaultRedirectUrl
                                            }/${store || 'en-ae'}/terms-of-use`}
                                    >
                                        {t('termsOfUse')}{' '}
                                    </a>
                                </span>
                                <span className={styles['text-style-3']}></span>
                                {t('andText')}
                                <span className={styles['text-style-4']}></span>
                                <span className={styles['text-style-2']}>
                                    <a
                                        href={`${platformType === PLATFORM_TYPE.MWEB
                                            ? defaultMwebRedirectUrl
                                            : defaultRedirectUrl
                                            }/${store || 'en-ae'}/privacy-policy`}
                                    >
                                        &nbsp;{t('privacyPolicy')}
                                    </a>
                                </span>
                            </span>

                            <span
                                className={`${styles['-continue-to-agree']} ${styles['show-in-mweb']}`}
                            >
                                <span>{t('readMoreSmallCase')}</span>
                                <span className={styles['about-us']}>
                                    <a
                                        href={`${defaultMwebRedirectUrl}/${store
                                            ? store
                                            : router?.locale === 'en'
                                                ? 'en-ae'
                                                : 'ar-sa'
                                            }/about-us`}
                                    >
                                        &nbsp;{t('aboutUs')}
                                    </a>
                                </span>
                            </span>
                        </div>
                    )} */}
                    {/* {platformType !== PLATFORM_TYPE.APP.toLowerCase() && (
                        <HelpText />
                    )} */}
                </div>
            </div>

            {showLoader && <Loader />}
            {platformType === PLATFORM_TYPE.WEB.toLowerCase() && <ThemeSeter />}
        </>
    );
};

export default LoginLayout;
