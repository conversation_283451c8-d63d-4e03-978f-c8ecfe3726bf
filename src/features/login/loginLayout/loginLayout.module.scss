@import '@styles/variables';
@import '@styles/mixins';

// top button alignment
%top-button-alignment {
    position: absolute;
    top: 48px;
    cursor: pointer;
}

.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 60px;
    width: 608px;
    margin-top: 16px;

    @media only screen and (max-width: $sm) {
        width: unset;
    }

    .-continue-to-agree {
        font-family: "Mona Sans";
        font-size: 12px;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: 18px;
        letter-spacing: 0.12px;
        text-align: center;
        color: $warm-grey;
        z-index: 10;

        @include rtl-styles{
            font-family: $arabic-font-family;
        }
    }

    .show-in-mweb {
        display: none;

        .about-us {
            color: $barney-purple;
        }

        @media only screen and (max-width: $sm) {
            display: block;
        }
    }

    .-continue-to-agree .text-style-1 {
        color: $warm-grey;
    }

    .-continue-to-agree .text-style-2 {
        color: $barney-purple;

        &--app {
            color: $black-header;
            font-weight: 600;
        }
    }
}

.signin-signup {
    position: relative;
    ul {
        li {


            &:last-child {
                padding-bottom: 0;
                padding-top: 0;

                @media only screen and (max-width: $sm) {
                    margin-bottom: 10px;
                }
            }
        }
    }

    input {
        min-height: 23px;
        margin: 12px 0 12px;
        @include font-size(16);

        color: $dark-purple;
        /* Body/B2 - Bold */
        font-family: $mona-sans-font-family !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 24px; /* 150% */
        letter-spacing: -0.16px;

        @include rtl-styles{
            font-family: $arabic-font-family !important;
        }

        &::placeholder {
            color: #ccc !important;
            @include font-size(16);
            opacity: 1 !important;
        }
    }

    label {
        font-weight: 400;
        color: $black-header !important;
        @include font-size(12);

        @include rtl-styles {
            left: auto;
            right: 0;
            transform-origin: top right;
        }
    }

    a {
        color: $dark-purple;

        &:hover {
            text-decoration: underline;
        }
    }

    h3 {
        color: $dark-charcoal;
    }
}

.close-buttton {
    position: absolute;
    top: 48px;
    right: 30px;
    @include font-size(14);

    @extend %top-button-alignment;

    @include rtl-styles {
        right: auto;
        left: 30px;
    }

    &:hover {
        text-decoration: none !important;
    }
}

.back-button {
    left: 18px;
    @include font-size(18);

    @extend %top-button-alignment;

    @include rtl-styles {
        right: 18px;
        left: auto;
        transform: rotate(180deg);
    }

    &:hover {
        text-decoration: none !important;
    }
}

.content-panel {
    height: 100%;
    position: relative;

    h3 {
        margin-top: 0;
    }
}

.content {
    position: relative;
    width: 607px;
    padding: 32px;
    margin-bottom: -10px;
    border-radius: 24px;
    margin-top: 100px;
    border: 1px solid #FFD7EF;
    background: #FFF;
}

.back-container{
    display: flex;
    align-items: center;
    position: absolute;
    top: -55px;
    left: 0;
    gap: 8px;
    font-family: $mona-sans-font-family;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: 28px; /* 155.556% */
    letter-spacing: -0.18px;

    img{
        width: 32px;
        height: 32px;
        cursor: pointer;
    }

    @include rtl-styles{
        font-family: $arabic-font-family;
        left: auto;
        right: 0
    }
}

.logo-container {
    margin: 40px 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .logo {
        width: 88.6px;
        height: 60px;
        object-fit: contain;
    }
}

.business-login {
    position: absolute;
    top: -41px;
    right: -1px;
    cursor: pointer;


    @include rtl-styles{
        left: -1px;
        right: unset;
        transform: scale(-1,1);
    }

    &__content{
        position: relative;

        span {
                position: absolute;
                right: 50px;
                top: 8px;
                color: #FFF;
        
                /* Other/Text - Button */
                font-family: "Mona Sans";
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                line-height: 24px;
                /* 150% */
                letter-spacing: -0.16px;

                @include rtl-styles{
                    font-family: $arabic-font-family;
                    width: 100%;
                    right: 40px;
                }
            }
    }



    .business-login {
        //width: 166.1px;
        height: 50px;
        border-radius: 5px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: flex-start;
        gap: 10px;
        margin: 0px 0px 0px 442px;
        padding: 8px 16px;
        background-color: #306ff6;
        cursor: pointer;

        @include rtl-styles {
            margin: 0px 424px 0px 0px;
        }

        span {
            width: 105px;
            height: 21px;
            flex-grow: 0;
            font-family: Poppins;
            font-size: 14px;
            font-weight: 600;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: normal;
            text-align: left;
            color: #fff;

            @include rtl-styles {
                text-wrap: nowrap;
                width: 130px;
                font-family: $arabic-font-family !important;
            }
        }

        .arrow {
            width: 12.1px;
            height: 12.1px;
            flex-grow: 0;
            margin-top: 5px;
            //transform: rotate(-315deg);
            //background-color: #fff;
        }
    }

    .business-login:hover {
        background-color: #0B3FAD;
    }
}

.main {
    display: flex;
    justify-content: center;
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.bg-circle-top-left {
    width: calc(100vw * 0.644);
    height: calc(100vw * 0.644);
    border-radius: calc(100vw* 0.644);
    border: 50px solid rgba(255, 255, 255, 0.2);
    position: absolute;
    //top: -395px;
    top: calc(.26 * 100vw * -1);
    // left: -295px;
    left: calc(0.19 * 100vw * -1);
}

.bg-circle-bottom-right {
    width: calc(100vw * 0.644);
    height: calc(100vw * 0.644);
    border-radius: calc(100vw* 0.644);
    border: 50px solid rgba(255, 255, 255, 0.2);
    position: absolute;
    // bottom: -288px;
    bottom: calc(0.19 * 100vw * -1);
    // right: -198px;
    right: calc(0.13 * 100vw * -1);
}



.bg-title-strip {
    width: 200%;
    height: 48px;
    position: absolute;
    top: 755px;
    border: 1.5px solid rgba(255, 255, 255, 0.50);
    background: #0E0F0C;
    display: flex;
    gap: 10px;
    // transform: rotate(-2deg);
    align-items: center;
    justify-content: center;
    animation: marquee 20s linear 0s infinite;
    transform: rotate(-2deg) translateX(0%);

    @include rtl-styles{
        transform: rotate(2deg) translateX(0%);
        animation: marquee-ar 20s linear 0s infinite;
    }

    &__title {
        color: #FFF;
        font-family: "Bricolage Grotesque";
        font-optical-sizing: none;
        font-size: 16px;
        font-style: normal;
        font-weight: 800;
        line-height: 100%;
        /* 16px */
        letter-spacing: -0.5px;
        text-transform: uppercase;
        flex-shrink: 0;

        @include rtl-styles{
            font-family: $arabic-font-family;
        }
    }

    &__title-separator {
        color: #B800C4;
        font-family: "Bricolage Grotesque";
        font-optical-sizing: none;
        font-size: 16px;
        font-style: normal;
        font-weight: 800;
        line-height: 100%;
        /* 16px */
        letter-spacing: -0.5px;
        text-transform: uppercase;
        width: 5px;
        height: 16px;
        line-height: 10px;

        @include rtl-styles{
            font-family: $arabic-font-family;
        }
    }
}

.main-login {
    // @media only screen and (max-height: 800px) {
    //     height: unset;
    // }
}

.logo-arabic {
    cursor: pointer;
    width: 45px;
    height: 30px;
    font-family: $arabic-font-family;
    font-size: 16px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.5;
    letter-spacing: -0.16px;
    text-align: right;
    color: $barney-purple;

    @include rtl-styles {
        font-family: Poppins;
        font-weight: normal;
    }
}



@media only screen and (max-width: $sm) {
    .main {
        display: block;
        // background: rgb(255, 255, 288, 0.8);
    }

    .bg-title-strip {
        display: none !important;
    }

    .container {
        padding: 50px 20px 0;
    }

    .top-container {
        z-index: 10;
        position: absolute;
        top: -30px;
        left: 0px;

        @include rtl-styles {
            right: 0px;
        }

        .logo {
            width: 68px;
            height: 46.1px;
            object-fit: contain;
        }
    }

    .logo-arabic {
        width: unset;
        height: unset;
        font-size: 14px;
        line-height: 1.71;
        letter-spacing: -0.14px;
    }

    .content {
        width: 350px !important;
        margin-top: 0px;
        padding: 32px 24px;
    }

    .footer {
        .-continue-to-agree {
            font-family: "Mona Sans";
            font-size: 10px;
            letter-spacing: 0.1px;

            @include rtl-styles {
                font-family: $arabic-font-family;
                font-weight: normal;
            }
        }
    }
}

.language-switch {
    cursor: pointer;
    position: absolute;
    top: 24px;
    right: 23px;
    display: flex;
    gap: 8px;
    height: 48px;
    padding: 8px 16px;
    align-items: center;
    justify-content: center;
    border-radius: 100px;
    border: 1px solid $dark-purple;
    background: transparent;
    z-index: 10;

    @include rtl-styles {
        left: 23px;
        right: unset;
    }

    &__text {
        color: $dark-charcoal;
        /* Body/B3 - Semi bold */
        font-family: "Mona Sans";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 18px;
        /* 128.571% */
        letter-spacing: -0.14px;

        @media only screen and (max-width: $sm) {
            font-size: 12px;
        }

        @include rtl-styles{
            font-family: $arabic-font-family;
        }
    }

    @media only screen and (max-width: $sm) {
        top: -1px;
        border: 1px solid #FFD7EF;
        padding: 8px 16px;
        right: -1px;
        height: 28px;
        width: 73px;
        border-radius: 0px 24px;

        @include rtl-styles {
            left: 0;
            right: unset;
            border-radius: 24px 0;
        }

        img{
            width: 16px;
            height: 16px;
        }
    }

}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-x: scroll;
    -ms-overflow-style: none;
    scrollbar-width: none;
    min-height: 100vh;
    ::-webkit-scrollbar {
      display: none;
    }
}

@keyframes marquee {
    0% { transform: rotate(-2deg) translateX(0%); }
    100% { transform: rotate(-2deg) translateX(50vw); }
  }

  @keyframes marquee-ar {
    0% { transform: rotate(2deg) translateX(0%); }
    100% { transform: rotate(2deg) translateX(-50vw); }
  }


  .arrow {
    position: absolute;
    top: 13px;
    width: 13px;
    right: 20px;
    height: 14px;

    @include rtl-styles{
        right: 15px;
    }
  }