@import '@styles/variables';
@import '/src/styles/mixins';

.title-wrapper {
    .title-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 32px;
        flex-direction: row-reverse;
        align-items: center;

        h3,
        h2 {
            margin: 0px;
        }

        &__title {
            display: flex;
            flex-direction: column;
            gap: 8px;

            &--welcome {
                color: $dark-charcoal;
                font-family: $default-font-family;
                font-optical-sizing: none;
                font-size: 24px;
                font-style: normal;
                font-weight: 800;
                line-height: 32px;

                @include rtl-styles {
                    font-family: $arabic-font-family;
                }
            }

            &--login {
                /* Heading/H1 */
                font-family: $default-font-family;
                font-optical-sizing: none;
                font-size: 24px;
                font-style: normal;
                font-weight: 800;
                line-height: 32px;
                background: linear-gradient(
                    90deg,
                    #4983f6 0%,
                    #c175f5 50%,
                    #fbacb7 100%
                );
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;

                div {
                    background: linear-gradient(
                        90deg,
                        #4983f6 0%,
                        #c175f5 50%,
                        #fbacb7 100%
                    );
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                }

                @include rtl-styles {
                    font-family: $arabic-font-family;
                }

                @media only screen and (max-width: $sm) {
                    font-size: 24px;
                    line-height: 24px;
                }
            }

            @media only screen and (max-width: $sm) {
                display: none;
            }
        }

        img {
            width: 60.142px;
            height: 40px;
            aspect-ratio: 60.14/40;
            min-width: 60.142px;
        }

        @media only screen and (max-width: $sm) {
            justify-content: center;
            margin-bottom: 32px;

            // img {
            //     height: 48px;
            // }
        }
    }

    &__mweb-title {
        display: flex;
        flex-direction: column;

        &--welcome {
            color: $dark-charcoal;
            font-family: $default-font-family;
            font-optical-sizing: none;
            font-size: 18px;
            font-weight: 800;
            line-height: 24px;
            letter-spacing: -0.09px;
            margin: 0 0 8px !important;

            @include rtl-styles {
                font-family: $arabic-font-family;
            }
        }

        &--login {
            color: $dark-charcoal;
            font-family: $default-font-family;
            font-optical-sizing: none;
            font-size: 24px;
            font-weight: 800;
            line-height: 24px;
            margin: 0 0 32px !important;
            text-align: center;
            background: linear-gradient(
                90deg,
                #4983f6 0%,
                #c175f5 50%,
                #fbacb7 100%
            );
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;

            @include rtl-styles {
                font-family: $arabic-font-family;
            }
        }
    }
}
