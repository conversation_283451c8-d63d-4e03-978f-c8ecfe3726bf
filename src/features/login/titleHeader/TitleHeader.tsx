import { useTranslation } from 'next-i18next';
import styles from './TitleHeader.module.scss';
import getConfig from 'next/config';
import Link from 'next/link';
import { useMediaQuery } from '@mui/material';
import useAppRouter from '@features/common/router.context';
import Marquee from 'react-fast-marquee';
import { PLATFORM_TYPE } from '@constants/common';

const TitleHeader = ({ title, subTitle, logo, platformType }: any) => {
    const isWeb = useMediaQuery('(min-width:768px)');

    const {
        state: { locale }
    } = useAppRouter();

    // taking public image config url
    const {
        publicRuntimeConfig: { defaultRedirectUrl },
    } = getConfig();

    return (
        <div className={styles['title-wrapper']}>
            <div className={styles['title-header']}>
                {isWeb && (
                    <div className={styles['title-header__title']}>
                        {/* <h3 className={styles['title-header__title--welcome']}>
                            {title}
                        </h3> */}
                        <h2 className={styles['title-header__title--login']}>
                            {locale === 'ar' ? (
                                subTitle.length > 25 ? (
                                    <Marquee>{subTitle}</Marquee>
                                ) : (
                                    subTitle
                                )
                            ) : (
                                subTitle
                            )}
                        </h2>
                    </div>
                )}
                <div className={styles['title-header__logo']}>
                    {platformType != PLATFORM_TYPE.APP.toLowerCase() ? (
                        <Link href={defaultRedirectUrl}>
                            <img
                                src={logo}
                                height={40}
                                width={60.142}
                                alt="logo"
                            />
                        </Link>
                    ) : (
                        <img src={logo} height={40} width={60.142} alt="logo" />
                    )}
                </div>
            </div>
            {!isWeb && (
                <div className={styles['title-wrapper__mweb-title']}>
                    {/* <h3 className={styles['title-wrapper__mweb-title--welcome']}>
                        {title}
                    </h3> */}
                    <h2 className={styles['title-wrapper__mweb-title--login']}>
                        {subTitle}
                    </h2>
                </div>
            )}
        </div>
    );
};

export default TitleHeader;
