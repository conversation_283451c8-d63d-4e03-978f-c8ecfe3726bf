import { useMutation } from "@apollo/client";
import { AUTH_LOGIN, AUTH_SET_TOKEN, AUTH_SIGNUP, AUTH_USER_DETAILS, BUSINESS_LOGIN, GENERATE_OTP_QUERY } from "./authAPI.query";
import { UserInfo } from "./loginFlowSlice";
import { DEFAULT_LOCALE } from "@features/common/router.context";
import { AUTH_FLOW, PLATFORM_TYPE } from "@constants/common";
import { AuthSetTokenResponse, GenerateLoginOTPResponse, UserDetails, VerifyOTPResponse } from "./authAPI.interface";
import { initializeApollo } from "@graphql/apolloClient";
import * as Sentry from '@sentry/nextjs';


/**
 * APIs for Sign In Sign Up Flows
 * @returns 
 */
const useAuthAPI = () => {
    const [authLogin] = useMutation(AUTH_LOGIN);
    const [authSignUp] = useMutation(AUTH_SIGNUP);
    const [authSetToken] = useMutation(AUTH_SET_TOKEN);
    const [validateBusinessUserExists] = useMutation(BUSINESS_LOGIN);
    const [sentOtp] = useMutation(GENERATE_OTP_QUERY);

    /**
     * Method to generate Login OTP for both Email and Phone number
     * @param params 
     * @param type 
     * @param captcha 
     * @param language 
     * @param cognitoCustomQuery 
     * @param onSuccessCallback 
     * @param onErrorCallback 
     */
    const generateLoginOTP = (params: UserInfo, type : string, captcha : string, language = DEFAULT_LOCALE, cognitoCustomQuery : any, onSuccessCallback : any, onErrorCallback : any) => {
        
        const context = getContext(language);

        let requestParams : any = {};
        requestParams['captchaReference'] = captcha;
        if(type == 'phone_number'){
            requestParams['phone_number'] = params.phone_number;
        } else if(type == 'email'){
            requestParams['email'] = params.email?.toLowerCase();
        }

        authLogin({
            variables: {
                "input": {
                  ...requestParams
                }
              },
            context,
            onCompleted : (response: GenerateLoginOTPResponse) => {
                onSuccessCallback(response, captcha, cognitoCustomQuery, params);
            },
            onError : (error: any) => {
                onErrorCallback(error);
            }
        })
    }

    /**
     * Method to verify OTP for Login
     * @param sessionId 
     * @param otp 
     * @param language 
     * @param onSuccessCallback 
     * @param onErrorCallback 
     * @param authFlow 
     */
    const verifyOTP = (sessionId : any, otp : string, language = DEFAULT_LOCALE, onSuccessCallback : any, onErrorCallback : any, authFlow: String = AUTH_FLOW.NORMAL) => {
        const context = getContext(language);

        let requestParams : any = {};
        requestParams['SessionId'] = sessionId;
        requestParams['OTP'] = otp;
        requestParams['AuthFlow'] = authFlow;

        authLogin({
            variables: {
                "input": {
                  ...requestParams
                }
              },
            context,
            onCompleted : (response: VerifyOTPResponse) => {
                onSuccessCallback(response);
            },
            onError : (error: any) => {
                onErrorCallback(error);
            }
        })
    }

    /**
     * Method for User Sign Up Flow
     * @param params 
     * @param captcha 
     * @param language 
     * @param userInfo 
     * @param onSuccessCallback 
     * @param onErrorCallback 
     */
    const userSignUp = (params: any, captcha : string, language = DEFAULT_LOCALE, userInfo : UserInfo, onSuccessCallback : any, onErrorCallback : any) => {
        const context = getContext(language);
        
        authSignUp({
            variables: {
                "input": {
                  ...params
                }
              },
            context,
            onCompleted : (response: GenerateLoginOTPResponse) => {
                onSuccessCallback(response, userInfo, params);
            },
            onError : (error: any) => {
                onErrorCallback(error);
            }
        })
    }

    /**
     * 
     * @param sessionId 
     * @param otp 
     * @param language 
     * @param onSuccessCallback 
     * @param onErrorCallback 
     * @param authFlow 
     */
    const userSignUpOtpVerification = (sessionId : any, otp : string, language = DEFAULT_LOCALE, onSuccessCallback : any, onErrorCallback : any, authFlow: String = AUTH_FLOW.NORMAL, platform: PLATFORM_TYPE) => {
        const context = getContext(language);
        const authSession = localStorage.getItem('authSession') || '';
        let requestParams : any = {};
        requestParams['SessionId'] = sessionId;
        requestParams['OTP'] = otp;
        requestParams['AuthFlow'] = authFlow;
        if(platform && platform.toLowerCase() == PLATFORM_TYPE.APP.toLowerCase()){
            requestParams['authSession'] = authSession;
        }

        authSignUp({
            variables: {
                "input": {
                  ...requestParams
                }
              },
            context,
            onCompleted : (response: GenerateLoginOTPResponse) => {
                onSuccessCallback(response);
            },
            onError : (error: any) => {
                onErrorCallback(error);
            }
        })
    }

    /**
     * Method to set refresh token and fetch the access, id token
     * @param username 
     * @param refreshToken 
     * @param language 
     * @param onSuccessCallback 
     * @param onErrorCallback 
     */
    const setRefreshToken = (username: string, refreshToken: string, language = DEFAULT_LOCALE, onSuccessCallback : any, onErrorCallback : any) => {
        const context = getContext(language);
        const platform = localStorage.getItem('platform') || "";
        
        const authSession = localStorage.getItem('authSession') || '';
        const requestParams = {
            "refreshToken": refreshToken,
            ...(platform.toLowerCase() == PLATFORM_TYPE.APP.toLowerCase() && authSession && authSession.length && {'authSession': authSession})

        }
        authSetToken({
            variables: {
                "input": {
                  ...requestParams
                }
              },
            context,
            onCompleted : (response: AuthSetTokenResponse) => {
                onSuccessCallback(response, username);
            },
            onError : (error: any) => {
                onErrorCallback(error);
            }
        })
    }

    /**
     * Method to fetch user details based on Access token
     * @param locale 
     * @param accessToken 
     * @returns 
     */
    const getUserDetails = async (locale : string, accessToken : string) => {
        try{
            const context = getContext(locale);
            const apolloClient = initializeApollo(locale);
            let response = await apolloClient.query({
                query: AUTH_USER_DETAILS,
                context,
                variables: {
                    input : {
                        accessToken : accessToken
                    }
                }
            });
            return response;
        } catch(error){
            Sentry.captureMessage(`Error occured in API call method getUserDetails ${JSON.stringify(error)}`);
            return {
                name : ""
            }
        }
    }

    /**
     * Method to get context 
     * @param language 
     * @returns 
     */
    const getContext = (language : string) => {
        const headers: any = {
            'Content-Type': 'application/json',
            'accept-language': language
        };
        return {
            clientName: "ecom-users",
            headers,
            credentials: "include",
        }
    }

    /**
     * Validates if a user exists in the system
     * @param email - The email address to validate
     */
    const validateUserExists = async (email: string) => {
        try {
            const platform = localStorage.getItem('platform') || '';
            const selectedStore = localStorage.getItem('selectedStore')?.toUpperCase() || 'AE';

            const headers = {
                'app-platform': platform,
                'access-locale': `ST${selectedStore}`,
            };

            const response = await validateBusinessUserExists({
                variables: {
                    input: { email },
                },
                context: {
                    headers,
                    clientName: 'at-work',
                    credentials: 'include',
                },
            });

            return response?.data?.validateBusinessUserExists;
        } catch (error) {
            Sentry.captureException(error);
            console.log('Failed to validate user existence',error);
        }
    };

    const generateAtworkOtp = async (variables: any) => {
        try {
            const platform = localStorage.getItem('platform') || '';
            const selectedStore =
                localStorage.getItem('selectedStore')?.toUpperCase() || 'AE';

            const headers = {
                'access-locale': `ST${selectedStore}`,
                'app-platform': PLATFORM_TYPE.WEB.toLowerCase(),
            };

            console.log(variables, 'variables');

            const response = await sentOtp({
                variables: variables,
                context: {
                    headers,
                    clientName: 'at-work',
                    credentials: 'include',
                },
            });
            console.log(response, 'response');
            return response?.data;
        } catch (error) {
            Sentry.captureException(error);
            console.log('Failed to sent otp', error);
        }
    };

    return {
        generateLoginOTP,
        generateAtworkOtp,
        verifyOTP,
        userSignUp,
        userSignUpOtpVerification,
        setRefreshToken,
        getUserDetails,
        validateUserExists
    }

}   

export default useAuthAPI;