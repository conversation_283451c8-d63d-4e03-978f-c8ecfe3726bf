import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { AppState } from '@redux/store';
import { COGNITO_USER_TYPE, PAGE_FLOW } from '@constants/common';

export interface LoginFlowSlice {
    params: {
        pageFlow: PAGE_FLOW;
        userInfo: UserInfo;
        prefillInfo: PrefillInfo;
        cognitoCustomResponse: any;
        resendOtpReq: any;
        rdir: string;
        email: string;
        customFlow: boolean;
        signInResponse: any;
        verifyEmail: boolean;
        phone_number: string;
        ipAddress: string;
        loggedOut: boolean;
        provider: COGNITO_USER_TYPE;
        mfa: boolean;
        providerId: string;
        socialProviderSignUp: boolean;
        socialEmailExists: boolean;
        socialEmailVerified: boolean;
        legacyAttrs: any;
        verifyMobileInfo: any;
        verifyMobile: boolean;
        mobileToBeVerified: string;
        mobileVerifyStatus: boolean;
        socialLinking: boolean;
        addInfo: any;
        legacyResetPassInfo: any;
        countryCode: string;
        numberMobile: string;
        verifyEmailOTPInfo: any;
        platform: string;
    };
}

export interface UserInfo {
    emailVerified : boolean;
    phoneVerified : boolean;
    SessionId? : string;
    email?: string;
    phone_number?: string;
    UserAttributes? : Attributes;
    toVerify : FieldToVerify,
    isSocial : boolean;
    userType : COGNITO_USER_TYPE
    providerId?: string
    idp_signature?: string
}

export interface PrefillInfo{
    previousEmail: string;
    prefillRequired: boolean;
    type: UserType;
    authCode: string;
}

export enum UserType{
    AppleUser = "APPLE-USER",
    ConventionalUser = "CONVENTIONAL-USER"
}

export interface Attributes{
    name: string;
    birthdate?: string;
    gender: string;
    'custom:date_joined': string;
    picture: string;
}

export enum FieldToVerify{
    Email = 'email',
    Phone = 'phone',
    None = 'none'
}

export const defaultUserInfo: UserInfo = {
    emailVerified: false,
    phoneVerified: false,
    SessionId: "",
    email: "",
    phone_number:"",
    UserAttributes : {
        name : "",
        birthdate : "",
        gender : "",
        "custom:date_joined" : "",
        picture:""
    },
    toVerify : FieldToVerify.None,
    isSocial : false,
    userType : COGNITO_USER_TYPE.COGNITO
    
}

const initialState: LoginFlowSlice = {
    params: {
        pageFlow: PAGE_FLOW.NONE,
        userInfo : defaultUserInfo,
        prefillInfo: {previousEmail : '', prefillRequired : false, type : UserType.ConventionalUser, authCode : ""},
        cognitoCustomResponse : {},
        resendOtpReq: {},
        rdir: '',
        email: '',
        customFlow: false,
        signInResponse: null,
        verifyEmail: false,
        phone_number: '',
        ipAddress: '',
        loggedOut: false,
        provider: COGNITO_USER_TYPE.COGNITO,
        mfa: false,
        providerId: '',
        socialProviderSignUp: false,
        socialEmailExists: false,
        socialEmailVerified: false,
        legacyAttrs: {},
        verifyMobileInfo: {},
        verifyMobile: false,
        mobileToBeVerified: '',
        mobileVerifyStatus: false,
        socialLinking: false,
        addInfo: {},
        legacyResetPassInfo: {},
        countryCode: '',
        numberMobile: '',
        verifyEmailOTPInfo: {},
        platform: '',
    },
};

// #. quickview slice contains the quickview state methods
export const loginFlowSlice = createSlice({
    name: 'loginFlowSlice',
    initialState,
    reducers: {
        setPageFlow: (state, action: PayloadAction<any>) => {
            state.params.pageFlow = action.payload;
        },
        setPrefillInfo: (state, action: PayloadAction<any>) => {
            state.params.prefillInfo = action.payload;
        },
        setRdir: (state, action: PayloadAction<any>) => {
            state.params.rdir = action.payload;
        },
        
        setEmail: (state, action: PayloadAction<any>) => {
            state.params.email = action.payload;
        },
       
        setCustomFlow: (state, action: PayloadAction<any>) => {
            state.params.customFlow = action.payload;
        },
        setSignInResponse: (state, action: PayloadAction<any>) => {
            state.params.signInResponse = action.payload;
        },
        setVerifyEmail: (state, action: PayloadAction<any>) => {
            state.params.verifyEmail = action.payload;
        },
        setPhoneNumber: (state, action: PayloadAction<any>) => {
            state.params.phone_number = action.payload;
        },
        setDefaultState: (state, action: PayloadAction<any>) => {
            state.params = initialState.params;
        },
        setLoggedOut: (state, action: PayloadAction<any>) => {
            state.params.loggedOut = action.payload;
        },
        setProvider: (state, action: PayloadAction<any>) => {
            state.params.provider = action.payload;
        },
        setMFA: (state, action: PayloadAction<any>) => {
            state.params.mfa = action.payload;
        },
        setProviderId: (state, action: PayloadAction<any>) => {
            state.params.providerId = action.payload;
        },
        setSocialProviderSignUp: (state, action: PayloadAction<any>) => {
            state.params.socialProviderSignUp = action.payload;
        },
        setSocialEmailExists: (state, action: PayloadAction<any>) => {
            state.params.socialEmailExists = action.payload;
        },
        setSocialEmailVerified: (state, action: PayloadAction<any>) => {
            state.params.socialEmailVerified = action.payload;
        },
        setLegacyAttrs: (state, action: PayloadAction<any>) => {
            state.params.legacyAttrs = action.payload;
        },
        setVerifyMobileInfo: (state, action: PayloadAction<any>) => {
            state.params.verifyMobileInfo = action.payload;
        },
        setVerifyMobile: (state, action: PayloadAction<any>) => {
            state.params.verifyMobile = action.payload;
        },
        setMobileToVerified: (state, action: PayloadAction<any>) => {
            state.params.mobileToBeVerified = action.payload;
        },
        setMobileVerifyStatus: (state, action: PayloadAction<any>) => {
            state.params.mobileVerifyStatus = action.payload;
        },
        setSocialLinking: (state, action: PayloadAction<any>) => {
            state.params.socialLinking = action.payload;
        },
        setAddInfo: (state, action: PayloadAction<any>) => {
            state.params.addInfo = action.payload;
        },
        setLegacyResetPassInfo: (state, action: PayloadAction<any>) => {
            state.params.legacyResetPassInfo = action.payload;
        },
        setCountryCode: (state, action: PayloadAction<any>) => {
            state.params.countryCode = action.payload;
        },
        setNumberMobile: (state, action: PayloadAction<any>) => {
            state.params.numberMobile = action.payload;
        },
        setVerifyEmailOTPInfo: (state, action: PayloadAction<any>) => {
            state.params.verifyEmailOTPInfo = action.payload;
        },
        setPlatform: (state, action: PayloadAction<any>) => {
            state.params.platform = action.payload;
        },
        setUserInfo: (state, action: PayloadAction<any>) => {
            state.params.userInfo = {...state.params.userInfo, ...action.payload}
        },
        setResendOtpReq: (state, action: PayloadAction<any>) => {
            state.params.resendOtpReq = action.payload;
        },
        setCognitoCustomResponse: (state, action: PayloadAction<any>) => {
            state.params.cognitoCustomResponse = action.payload;
        },
    },
});

export const {
    setPageFlow,
    setRdir,
  
    setEmail,
    
    setSignInResponse,
    setPhoneNumber,
    setCustomFlow,
    setDefaultState,
    setVerifyEmail,
    setLoggedOut,
    setProvider,
    setMFA,
    setProviderId,
    setSocialProviderSignUp,
    setSocialEmailExists,
    setSocialEmailVerified,
    setLegacyAttrs,
    setVerifyMobileInfo,
    setVerifyMobile,
    setMobileToVerified,
    setMobileVerifyStatus,
    setSocialLinking,
    setAddInfo,
    setLegacyResetPassInfo,
    setCountryCode,
    setNumberMobile,
    setVerifyEmailOTPInfo,
    setPlatform,
    setUserInfo,
    setResendOtpReq,
    setCognitoCustomResponse,
    setPrefillInfo
} = loginFlowSlice.actions;

// #. State for loader component
export const getPageFlow = (state: AppState) => state.login?.params?.pageFlow;
export const getRdir = (state: AppState) => state.login?.params?.rdir;

export const getEmail = (state: AppState) => state.login?.params?.email;

export const getSignInResponse = (state: AppState) =>
    state.login?.params?.signInResponse;
export const getCustomFlow = (state: AppState) =>
    state.login?.params?.customFlow;
export const getVerifyEmail = (state: AppState) =>
    state.login?.params?.verifyEmail;
export const getPhoneNumber = (state: AppState) =>
    state.login?.params?.phone_number;
export const getLoggedOut = (state: AppState) => state.login?.params?.loggedOut;
export const getProvider = (state: AppState) => state.login?.params?.provider;
export const getMFA = (state: AppState) => state.login?.params?.mfa;
export const getProviderId = (state: AppState) =>
    state.login?.params?.providerId;
export const getSocialProviderSignUp = (state: AppState) =>
    state.login?.params?.socialProviderSignUp;
export const getSocialEmailExists = (state: AppState) =>
    state.login?.params?.socialEmailExists;
export const getSocialEmailVerified = (state: AppState) =>
    state.login?.params?.socialEmailVerified;
export const getLegacyAttrs = (state: AppState) =>
    state.login?.params?.legacyAttrs;
export const getVerifyMobileInfo = (state: AppState) =>
    state.login?.params?.verifyMobileInfo;
export const getVerifyMobile = (state: AppState) =>
    state.login?.params?.verifyMobile;
export const getMobileToBeVerified = (state: AppState) =>
    state.login?.params?.mobileToBeVerified;
export const getMobileVerifyStatus = (state: AppState) =>
    state.login?.params?.mobileVerifyStatus;
export const getSocialLinking = (state: AppState) =>
    state.login?.params?.socialLinking;
export const getLoginState = (state: AppState) => state.login?.params;
export const getAddInfo = (state: AppState) => state.login?.params.addInfo;
export const getCountryCode = (state: AppState) =>
    state.login?.params.countryCode;
export const getLegacyResetPassInfo = (state: AppState) =>
    state.login?.params.legacyResetPassInfo;
export const getNumberMobile = (state: AppState) =>
    state.login?.params.numberMobile;
export const getVerifyEmailOTPInfo = (state: AppState) =>
    state.login?.params.verifyEmailOTPInfo;
export const getPlatform = (state: AppState) => state.login?.params?.platform;
export const getUserInfo = (state: AppState) => state.login?.params?.userInfo;
export const getResendOtpReq = (state: AppState) => state.login?.params?.resendOtpReq;
export const getCognitoCustomResponse = (state: AppState) => state.login?.params?.cognitoCustomResponse;
export const getPrefillInfo = (state: AppState) => state.login?.params?.prefillInfo;






// #. Export the reducers
export default loginFlowSlice.reducer;
