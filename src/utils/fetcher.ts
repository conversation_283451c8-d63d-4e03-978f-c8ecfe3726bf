/*******  Custom fetch util for handling REST APIs *******/
import * as Sentry from '@sentry/nextjs';

const fetcher: any = async (
    url: RequestInfo,
    options: RequestInit | undefined
) => {
    const res = await fetch(url, options);
    if (res.ok || [429].includes(res.status)) {
        return promiseResolveHandler(res);
    } else {
        if (res.status === 400 || res.status === 401 ||typeof window === 'undefined') {
            return promiseRejectHandler(res);
        } else {
            // custom error page url
            Sentry.captureException(res);
            const fallbackUrl = `/error/${res.status}/`;
            window.location.href = fallbackUrl;
        }
    }
};

const promiseResolveHandler = async (res: {
    json: () => Promise<any>;
    status: any;
    ok: any;
}) => {
    return new Promise((resolve) =>
        res.json().then((data: any) =>
            resolve({
                status: res.status,
                ok: res.ok,
                data,
            })
        )
    );
};

const promiseRejectHandler = (res: {
    json: () => Promise<any>;
    text: () => Promise<any>;
    status: any;
    ok: any;
}) => {
    return new Promise((reject) =>
        (res.status === 400 ? res.json() : res.text()).then((data: any) =>
            reject({
                status: res.status,
                ok: res.ok,
                data,
            })
        )
    );
};

const fetchAPI: any = async (
    url: RequestInfo,
    options: RequestInit | undefined
) => {
    try {
        // custom fetch api call
        return await fetcher(url, options);
    } catch (error:any) {
        if(error && error.message && error.message.includes("Failed to fetch")){
            return Promise.resolve({
                offline : true
            })
        }
    }
};

export default fetchAPI;
