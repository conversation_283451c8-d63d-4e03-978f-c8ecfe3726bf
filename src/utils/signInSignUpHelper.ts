/* ####################################################### */
/* ## Common utility functions used for sign in sign up ## */
/* ####################################################### */

import {
    API_STATUS,
    AUTH_FLOW,
    COGNITO_CONFIG,
    COGNITO_USER_TYPE,
    CustomSchema,
    PLATFORM_TYPE,
} from '@constants/common';
import { UserInfo } from '@features/login/loginFlowSlice';
import * as Sentry from '@sentry/nextjs';
import getConfig from 'next/config';

const {
    publicRuntimeConfig: { androidPackage, redirectURL },
} = getConfig();

/**
 * This method is used to parse the Pre sign up error message
 * ##
 * @param message
 * @returns
 */
export const parsePreSignUpErrorMessage = (message: any) => {
    try {
        let messageArray = message
            ? message.split(COGNITO_CONFIG.RESPONSE_SEPARATOR)
            : '';
        let jsonResponse = messageArray[1].toString().trim();
        if (jsonResponse[jsonResponse.length - 1] === '.')
            jsonResponse = jsonResponse.slice(0, -1);
        if (!jsonResponse || !jsonResponse.length)
            throw Error('Error parsing message');
        return JSON.parse(jsonResponse);
    } catch (error) {
        console.log('Error parsing pre sign up error message ', error);
    }
};

/**
 * Method to get current date time
 * @returns
 */
export const getCurrentDate = () => {
    return new Date();
};

/**
 * Method to get provider name
 * @param provider 
 * @returns 
 */
export const getProviderName = (provider: COGNITO_USER_TYPE) => {
    if (provider == COGNITO_USER_TYPE.FACEBOOK) return 'Facebook';
    if (provider == COGNITO_USER_TYPE.GOOGLE) return 'Google';
    if (provider == COGNITO_USER_TYPE.APPLE) return 'SignInWithApple';
};


/**
 * Method to get custom schema
 * @returns 
 */
const getCustomSchema = () => {
    try {
        const deviceType = localStorage.getItem('deviceType') || '';
        if (deviceType == 'android') {
            return CustomSchema.INTENT;
        }
        return CustomSchema.YOUGOTAGIFT;
    } catch (error) {
        return CustomSchema.YOUGOTAGIFT;
    }
};

/**
 * Method to get custom schema url
 * @param auth_signature 
 * @returns 
 */
export const getCustomSchemaUrl = (auth_signature: string) => {
    try {
        const schema = getCustomSchema();
        const url = `${schema}://${
            window.location.host
        }/auth/callback?auth_signature=${auth_signature}${
            schema == CustomSchema.INTENT
                ? `#Intent;scheme=https;package=${androidPackage};end`
                : ''
        }`;
        return url;
    } catch (error) {
        Sentry.captureMessage(`Error in getCustomSchemaUrl`);
        return '';
    }
};

/**
 * Method to get custom schema error url
 * @param code 
 * @returns 
 */
export const getCustomSchemaErrorUrl = (code: string) => {
    try {
        const schema = getCustomSchema();
        const url = `${schema}://${
            window.location.host
        }/auth/error?code=${code}${
            schema == CustomSchema.INTENT
                ? `#Intent;scheme=https;package=${androidPackage};end`
                : ''
        }`;
        return url;
    } catch (error) {
        Sentry.captureMessage(`Error in getCustomSchemaErrorUrl`);
        return '';
    }
};

/**
 * Method to get custom schema url
 * @param token 
 * @returns 
 */
export const getCustomSchemaLogoutUrl = (token:string) => {
    try {
        const schema = getCustomSchema();
        const siteUrl = new URL(redirectURL);
        const url = `intent://${
            siteUrl.host
        }/auth/error?code=LOGOUT&token=${token}#Intent;scheme=https;package=${androidPackage};end`;
        return url;
    } catch (error) {
        console.log('Error in getCustomSchemaLogoutUrl ', error);
        console.log(error);
        Sentry.captureMessage(`Error in getCustomSchemaLogoutUrl`);
        return '';
    }
};

/**
 * Method to get platform type
 * @param platform 
 * @returns 
 */
export const getPlatformType = (platform: any) => {
    if (platform && platform.length) {
        if (platform == PLATFORM_TYPE.APP) return PLATFORM_TYPE.APP;
        if (platform == PLATFORM_TYPE.MWEB) return PLATFORM_TYPE.MWEB;
        if (platform == PLATFORM_TYPE.WEB) return PLATFORM_TYPE.WEB;
    }
    return PLATFORM_TYPE.WEB;
};

/**
 * Method to trigger captcha
 * @param elementRef 
 * @returns 
 */
export const executeCaptcha = (elementRef : any) => {
    elementRef.current.reset();
    return elementRef.current.execute();
}

/**
 * Method to check if user exists or not
 * @param response 
 * @returns 
 */
export const checkUserExists = (response : any) => {
    if(response.c == API_STATUS.ACCOUNT_ALREADY_EXISTS){
        return true;
    }
    return false;
}

/**
 * Method to check if phone or email address exists based on status code
 * @param statusCode 
 * @returns 
 */
export const phoneEmailAlreadyExists = (statusCode : API_STATUS) => {
    return statusCode == API_STATUS.ALREADY_EXISTS;
}

/**
 * Method to check if otp is generated successfully
 * @param statusCode 
 * @returns 
 */
export const otpGeneratedSuccessfully = (statusCode : API_STATUS) => {
    return statusCode == API_STATUS.OTP_GENERATED;
}

/**
 * Method to check if otp limit execeeded or captcha failed from status code
 * @param statusCode 
 * @returns 
 */
export const otpLimitExceededOrCaptchaFailed = (statusCode : API_STATUS) => {
    return [API_STATUS.LIMIT_EXCEEDED, API_STATUS.CAPTCHA_FAILED].includes(
        statusCode as API_STATUS
    )
}

/**
 * Method to chcek if platform is mobile app with valid auth sesssion
 * @param platformType 
 * @param authSession 
 * @returns 
 */
export const platformIsMobileAppWithValidAuthSession = (platformType : PLATFORM_TYPE, authSession: string) => {
    return platformType == PLATFORM_TYPE.APP.toLowerCase() &&
        authSession &&
        authSession.length &&
        authSession != 'null' &&
        authSession != null;
}

/**
 * Method to check if user creation success based on status code
 */
export const userCreated = (statusCode : API_STATUS) => {
    return statusCode == API_STATUS.USER_CREATED
}

/**
 * Method to check if otp is invalid
 * @param statusCode 
 * @returns 
 */
export const otpIsInvalid = (statusCode : API_STATUS) => {
    return [
        API_STATUS.INVALID_OTP,
        API_STATUS.SMS_ENGINE_INVALID_CODE,
    ].includes(statusCode)
}

/**
 * Method to check if otp is verified
 * @param statusCode 
 * @returns 
 */
export const otpIsVerified = (statusCode : API_STATUS) => {
    return statusCode === API_STATUS.FIELD_VERIFIED;
}

/**
 * Method to check if platform is not app
 * @param platformType 
 * @returns 
 */
export const platformNotApp = (platformType : PLATFORM_TYPE) => {
    return platformType != PLATFORM_TYPE.APP.toLocaleLowerCase()
}

/**
 * Method to check if platform is mobile- android or iOS with valid signature
 * @param platformType 
 * @param AuthSignature 
 * @returns 
 */
export const platformAppWithAuthSignature = (platformType : PLATFORM_TYPE, AuthSignature : string) => {
    return platformType == PLATFORM_TYPE.APP.toLocaleLowerCase() && AuthSignature && AuthSignature.length;
}

/**
 * Method to check otp generated successfully based on statua code
 * @param statusCode 
 * @returns 
 */
export const otpGeneratedSuccesfully = (statusCode : API_STATUS) => {
    return [
        API_STATUS.OTP_GENERATED,
        API_STATUS.OTP_GENERATED_WITH_CONTEXT_SWITCH,
    ].includes(statusCode as API_STATUS)
}

export const prepareSocialSignUpReqParams = (
    userInfo: UserInfo,
    token: string
) => {
    let reqParams = {
        email : userInfo.email,
        captchaReference : token,
        idpSignature: userInfo.idp_signature
    }

    if ([COGNITO_USER_TYPE.GOOGLE, COGNITO_USER_TYPE.FACEBOOK, COGNITO_USER_TYPE.APPLE].includes(userInfo.userType)) {
        const socialInfo = {
            UserAttributes: {
                name: userInfo.UserAttributes?.name,
                picture: userInfo.UserAttributes?.picture,
                birthdate: userInfo?.UserAttributes?.birthdate ?? '',
                gender: userInfo?.UserAttributes?.gender ?? ''
            },
            AuthFlow: AUTH_FLOW.SOCIAL
        };
        reqParams = { ...reqParams, ...socialInfo }
    }
    return reqParams;
};
export const emailIsWorkEmail = (statusCode : API_STATUS) => {
    return statusCode == API_STATUS.IS_WORK_EMAIL;
}
