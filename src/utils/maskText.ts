export default function maskText(text: string, type: string, mask: string = 'x') {
    if (type === 'name') {
        const nameParts = text.split(' ');
        let maskedText = '';
        nameParts.forEach((nameEl: string) => {
            if (nameEl.length >= 2) {
                let nameTxt =
                    nameEl.substring(0, 1) + mask.repeat(nameEl.length - 1);
                maskedText = maskedText ? maskedText + ' ' + nameTxt : nameTxt;
            } else {
                maskedText = maskedText ? maskedText + ' ' + nameEl : nameEl;
            }
        });
        return maskedText;
    } else if (type === 'email') {
        const [name, fullDomain] = text.split('@');
        const [domain, ...extension] = fullDomain.split('.');
        let maskedText = getMask(name, '*') + '@' + getMask(domain,"*") + '.' + getExtension(extension);
        return maskedText;
    } else {
        if (text.length >= 5) {
            let maskedText =
                text.substring(0, 2) +
                mask.repeat(text.length - 4) +
                text.slice(-2);
            return maskedText;
        } else if (text.length < 5 && text.length > 2) {
            let maskedText =
                text.substring(0, 1) +
                mask.repeat(text.length - 2) +
                text.slice(-1);
            return maskedText;
        } else if (text.length === 2) {
            let maskedText =
                text.substring(0, 1) + mask.repeat(text.length - 1);
            return maskedText;
        } else {
            return text;
        }
    }
}

const getExtension = (extensionArr: any) => {
    if(extensionArr && extensionArr.length){
        return extensionArr[extensionArr.length - 1]
    }
    return "";
}

const getMask = (name: string, mask: string) => {
    if (name.length >= 10) {
        return name.substring(0, 1) + mask.repeat(5);
    }  else if(name.length < 10 && name.length > 1){
        return name.substring(0, 1) + mask.repeat(name.length - 1);
    } else if(name.length === 1){
        return name;
    }
}