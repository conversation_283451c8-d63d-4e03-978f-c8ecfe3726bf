/**
 * Method to get IP Address from request
 * @param req
 * @returns
 */
const getIPFromRequest = (req: any) => {
    let ipAddress;
    try {
        if (req && req.headers && req.headers['x-forwarded-for']) {
            ipAddress = req.headers['x-forwarded-for'].split(',')[0];
        } else if (req && req.headers && req.headers['x-real-ip']) {
            ipAddress = req.connection.remoteAddress;
        } else {
            ipAddress = req?.connection?.remoteAddress;
        }
        return ipAddress || '';
    } catch (error) {
        return '';
    }
};

export default getIPFromRequest;
