/**
 * Util to validate Date Month 
 * Expected input format 
 * ## dd/MM
 * @param dateMonth 
 * @returns 
 */
const dateMonthIsValid = (dateMonth: string) => {
    let isDateMonthRegExp = /^\d{2}\/\d{2}$/;
    if(!dateMonth || !dateMonth.length || !(dateMonth.length == 5) || !isDateMonthRegExp.test(dateMonth)) return false;
    const dateMonthSplit = dateMonth.split("/");
    const datePart = dateMonthSplit[0];
    const monthPart = dateMonthSplit[1];
    const listOfDays = [31,29,31,30,31,30,31,31,30,31,30,31];
    const noDaysInMonth = listOfDays[parseInt(monthPart)-1];
    if(parseInt(datePart)>0 && parseInt(datePart)<=noDaysInMonth) return true;
    return false;
};

export default dateMonthIsValid;
