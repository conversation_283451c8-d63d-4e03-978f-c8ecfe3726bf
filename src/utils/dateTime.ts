/**
 * @name DateTime
 * @description To handle the date and time related util methods
 * @dependencies Need to check the relevence of external libraries
 */

const DateTime = {
    /**
     * @method today
     * @description To get today's date
     * @format YYYY-MM-DD
     */
    today: () => {
        return new Date().toISOString().substring(0, 10);
    },

    /**
     * @metho format
     * @description convert a date in to specific fomat
     * @param format
     * @param value
     * @returns
     */
    format: (format: string, value: string | Date) => {
        if (format == 'dd Mmm yyyy') {
            return new Date(value).toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
            });
        }
    },
};

export default DateTime;
