/**
 * @method isValidEmailAddress
 * @description To verify the provided address is meet the basic requirements
 * @param emailAddress
 * @returns
 */
const emailAddressIsValid = (emailAddress: string): boolean => {
    var emailReg = /^[^@ ]+@[^@ ]+\.[^@ ]+$/;
    return emailReg.test(emailAddress);
};

export { emailAddressIsValid };


/**
 * @method isGuestEmailValid
 * @description To verify the provided address is meet the basic requirements
 * @param emailAddress
 * @returns
 */
export const isGuestEmailValid = (emailAddress: string): boolean => {
    if(!emailAddress || emailAddress.length == 0) return false;
    if(emailAddress.startsWith('.') || emailAddress.endsWith('.')) return false;
    const emailReg = /^(?!.*\.\.)[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/;
    if(!emailReg.test(emailAddress)) return false;
    const [emailUsername, domainPlusTopDomain]  = emailAddress.split('@');
    const [domain, topDomain] = domainPlusTopDomain.split(".");
    if(topDomain.length < 2) return false;
    if(emailUsername.trim().endsWith('.')) return false;
    return true;
    
};