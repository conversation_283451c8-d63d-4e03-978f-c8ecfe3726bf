// encrypt content
import getConfig from 'next/config';
import fernet from "fernet";
import * as Sen<PERSON> from '@sentry/nextjs';

const {
    serverRuntimeConfig: { guestEncryptionKey },
} = getConfig();

const encrypt = (name: string, email: string) => {
    try{
        const secret = new fernet.Secret(guestEncryptionKey);
        const token = new fernet.Token({
            secret:  secret
        });
        return token.encode(JSON.stringify({name : name, email : email}));
    } catch(error){
        console.log(error);
        Sentry.captureMessage('Error in encrypt ');
        Sentry.captureException({ error });
        console.log('Error occured in encrypt method ', error);
        return "";
    }
    
};

export default encrypt;
