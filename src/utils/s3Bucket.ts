import { Storage } from 'aws-amplify';
import { v4 as uuidv4 } from 'uuid';

const S3Bucket = {
    /**
     * @method put
     * @param fileName
     * @param cotent
     * @param options
     * @returns
     */
    put: async (fileName: string, cotent: any, options: any = {}) => {
        // #. Get new uuid
        const uuid = uuidv4();

        // #. Rename the file with uuid
        return await Storage.put(
            `${uuid}-${fileName.replace(/ /g, '-')}`,
            cotent,
            options
        );
    },

    /**
     * @method get
     * @param fileName
     * @param options
     * @returns
     */
    get: async (fileName: string, options: any = {}) => {
        return await Storage.get(fileName, options);
    },
};

export default S3Bucket;
