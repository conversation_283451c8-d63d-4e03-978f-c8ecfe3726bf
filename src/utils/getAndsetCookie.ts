// create cookie

export const setCookie = (
    cookieName: string,
    cookieValue: string,
    exDays: number,
    domain?: string
) => {
    const d = new Date();
    d.setTime(d.getTime() + exDays * 24 * 60 * 60 * 1000);
    let expires = 'expires=' + d.toUTCString();
    if (domain) {
        document.cookie = `${cookieName}=${cookieValue};${expires};path=/;domain=${domain}`;
    } else {
        document.cookie =
            cookieName + '=' + cookieValue + ';' + expires + ';path=/';
    }
};

// get cookie

export const getCookie = (cookieName: string) => {
    let name = cookieName + '=';
    let decodedCookie = decodeURIComponent(document.cookie);
    let ca = decodedCookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) == ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) == 0) {
            return c.substring(name.length, c.length);
        }
    }
    return '';
};
