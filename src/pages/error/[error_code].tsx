import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import type { NextPage } from 'next';
import Image from 'next/image';
import getConfig from 'next/config';
import { useTranslation } from 'next-i18next';
import CommonLayout from '@features/layouts/common';
import getIPFromRequest from '@utils/getIPFromRequest';

const {
    publicRuntimeConfig: { imageBaseUrl },
} = getConfig();

const Error: NextPage<any> = ({ statusCode }: { statusCode: any }) => {
    const { t } = useTranslation('common');

    return (
        <div data-testid="error-page" className="error-page-wrapper">
            <CommonLayout>
                <div className="error-page">
                    <h2 data-testid="errorTitle">{t('errorTitle')}</h2>
                    <h5 data-testid="errorSubTitle">{t('errorSubTitle')}</h5>
                    <Image
                        src={`${imageBaseUrl}/images/error-page.png`}
                        alt="error-page-icon"
                        height={164}
                        width={135}
                        data-testid="error-image"
                    />
                    {/* <Button
              className="error-goto-home"
              onClick={() => {
                router.push('/');
              }}
            >
              {t('goToHome')}
            </Button> */}
                    <span className="error-message" data-testid="errorMessage">
                        {statusCode === 404
                            ? t('pageNotFound')
                            : statusCode === 403
                            ? t('haveNoPermission')
                            : statusCode === 500
                            ? t('weApologizeTryAgain', {
                                  email: '<EMAIL>',
                              })
                            : ''}
                    </span>
                </div>
            </CommonLayout>
        </div>
    );
};

export async function getServerSideProps({
    locale,
    query,
    res,
    err,
    req,
}: {
    locale: any;
    query: any;
    res: any;
    req: any;
    err: any;
}) {
    res.setHeader(
        'Cache-Control',
        'public, s-maxage=300, stale-while-revalidate=59'
    );

    const { error_code: statusCode } = query;

    return {
        props: {
            ...(await serverSideTranslations(locale, ['common'])),

            statusCode,
        },
    };
}

export default Error;
