import { NextPage } from 'next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useAppDispatch, useAppSelector } from '@redux/hooks';
import {
    getRdir,
    getUserInfo,
    setDefaultState,
    setPageFlow,
    setPrefillInfo,
    setUserInfo,
    UserInfo,
    UserType,
} from '@features/login/loginFlowSlice';
import { useEffect, useState } from 'react';
import useAppRouter from '@features/common/router.context';
import getConfig from 'next/config';
import dynamic from 'next/dynamic';
import { setIpAddress ,setIsCaptchaRequired } from '@features/common/commonSlice';
import { cloneDeep } from 'lodash';
import {
    API_STATUS,
    AUTH_FLOW,
    COGNITO_USER_TYPE,
    GRAPHQL_STATUS_CODE,
    PAGEURLS,
    PAGE_FLOW,
    PLATFORM_TYPE,
    SIGN_IN_SLUGS,
    SNOW_ENABLED,
    VOLUME_ENABLED,
} from '@constants/common';
import {
    getUserProfileDetails,
    refreshTokenValid,
    sendOTP,
} from '@features/common/signInSignUpAPI';
import * as Sentry from '@sentry/nextjs';
import { Fernet } from 'fernet-nodejs';
import { useTranslation } from 'next-i18next';
import { loginGA } from 'lib/gtm';
import fetchAPI from '@utils/fetcher';
import { cleverTapService } from '@features/common/clevertap/clevertap.services';
import isYouGotaGiftDomain from '@utils/isYouGotaGiftDomain';
import {
    getCustomSchemaLogoutUrl,
    getCustomSchemaUrl,
    prepareSocialSignUpReqParams,
} from '@utils/signInSignUpHelper';
import {
    getLoginMetaData, logoDataQuery,
    guestLoginEnabled,
    getCaptchaConfig,
    getBlacklistedCountries,
} from '@features/common/commonAPI';
import Meta from '@features/common/meta/meta';
import { useMutation } from '@apollo/client';
import { GUEST_LOGOUT } from '@features/common/common.query';
import Confirm from '@features/common/confirm/Confirm';
import useAuthAPI from '@features/login/authAPI';

const LoginLayout = dynamic(
    () => import('@features/login/loginLayout/loginLayout'),
    {
        ssr: false,
    }
);
const Loader = dynamic(() => import('@features/common/loader/Loader'), {
    ssr: false,
});

const {
    publicRuntimeConfig: {
        defaultRedirectUrl,
        imageBaseUrl
    },
    serverRuntimeConfig: { fernetKey }
} = getConfig();

const SigninSignup: NextPage<any> = ({
    headerData,
    slug,
    info,
    ip,
    prefillInfo,
    authSession,
    guestSessionExists,
    redirect = false,
    redirectUrl = '',
    userAgent,
    metaData,
    isGuestEnabled,
    ipCountry,
    isCaptchaRequired,
    blacklistedCountries
}): JSX.Element => {
    const {
        state: { locale },
        router,
    } = useAppRouter();

    const dispatch = useAppDispatch();
    const { t } = useTranslation('common');
    const [redirectPage, setRedirectPage] = useState(false);
    const [activeSlug, setActiveSlug] = useState('');
    const [browserBack, setBrowserBack] = useState(false);
    const rdir = useAppSelector(getRdir);
    const userInfo = useAppSelector(getUserInfo);
    const [platformType, setPlatformType] = useState('');
    const [selectedStore, setSelectedStore] = useState('AE');
    const {userSignUp} = useAuthAPI();



    useEffect(() => {
        const checkBfcache = (e:any) => {
          console.log("This page is restored from bfcache?", e.persisted);
          if (e.persisted) {
            window.location.reload();
          }
        };
        window.addEventListener("pageshow", checkBfcache);
      }, []);

    useEffect(() => {
        setPlatformType(localStorage.getItem('platform') || '');
        setSelectedStore(
            localStorage.getItem('selectedStore')?.toUpperCase() || 'AE'
        );
    }, []);
    const [appRedirectUrl, setAppRedirectUrl] = useState('');
    const [showRedirectPrompt, setShowRedirectPrompt] =
        useState<boolean>(false);

    useEffect(() => {
        if (redirect) {
            window.location.href = redirectUrl;
        }
    }, [redirect]);

    dispatch(setPrefillInfo(prefillInfo));

    dispatch(setIpAddress(ip));

    dispatch(setIsCaptchaRequired(isCaptchaRequired));

    useEffect(() => {
        window.onpopstate = () => {
            setBrowserBack(true);
            router.reload();
        };
    }, [router]);

    useEffect(() => {
        let slug = router?.query?.slug ?? '';
        if (slug == '') {
            dispatch(setDefaultState({}));
        }
        setActiveSlug(slug);
    }, [router.query]);

    useEffect(() => {
        if (activeSlug == 'redirect-signin') {
            setRedirectPage(true);
        } else {
            setRedirectPage(false);
        }
    }, [activeSlug]);

    useEffect(() => {
        if (redirectPage) {
            handleRedirectSignIn();
        }
    }, [redirectPage]);

    const [handleGuestLogout] = useMutation(GUEST_LOGOUT, {
        context: {
            clientName: 'guestLogin',
            headers: {
                'app-platform': platformType && platformType.length ? platformType : PLATFORM_TYPE.WEB.toLowerCase(),
                'access-locale': `ST${selectedStore}`,
                'yg-ip-address': ip,
            },
            credentials: 'include',
        },
        onError : (error:any) => {
            console.log('Error occured in Guest Logout ', error)
            Sentry.captureMessage('Error occured in handleGuestLogout ', error);
        },
        onCompleted : (response: any) => {
            if(response?.guestLogout?.logout?.status == GRAPHQL_STATUS_CODE.SERVER_ERROR){
                console.log('Guest logout failed ', response);
                Sentry.captureMessage('Guest Logout Failed ');
                Sentry.captureException(response);
            }
        }
    });

    useEffect(() => {
        userAgent && setAppPlatform(userAgent);
    }, [userAgent]);

    if (redirect) {
        return <Loader />;
    }

    const prepareSocialSignUpReqParams1 = (userInfo: UserInfo) => {
        let reqParams: any = cloneDeep(userInfo);
        delete reqParams.phone_number;
        delete reqParams.email;
        delete reqParams.UserAttributes?.birthdate;
        delete reqParams.UserAttributes?.name;
        delete reqParams.UserAttributes?.gender;
        delete reqParams.UserAttributes['custom:date_joined'];

        reqParams['AuthFlow'] = AUTH_FLOW.SOCIAL;
        reqParams.UserAttributes.email = userInfo.email;

        switch (userInfo.userType) {
            case COGNITO_USER_TYPE.GOOGLE: {
                reqParams.UserAttributes['google_email'] = userInfo.email;
                reqParams.UserAttributes['google_id'] = userInfo.providerId;
                reqParams.UserAttributes['provider_name'] = 'Google';
                reqParams.UserAttributes['provider_id'] = userInfo.providerId;
                break;
            }
            case COGNITO_USER_TYPE.FACEBOOK: {
                reqParams.UserAttributes['facebook_id'] = userInfo.providerId;
                reqParams.UserAttributes['provider_name'] = 'Facebook';
                reqParams.UserAttributes['provider_id'] = userInfo.providerId;
                break;
            }
            case COGNITO_USER_TYPE.APPLE: {
                reqParams.UserAttributes['apple_id'] = userInfo.providerId;
                reqParams.UserAttributes['provider_name'] = 'SignInWithApple';
                reqParams.UserAttributes['provider_id'] = userInfo.providerId;
                break;
            }
        }
        return reqParams;
    };

    const userCancelledSocialSignIn = ({ error_description, error }: any) => {
        return [
            'user_cancelled_authorize',
            'user_denied',
            'access_denied',
        ].includes(error);
    };

    const socialSignInCallback = (query: any) => {
        return query && query.code && query.state;
    };

    const signUpCallback = (query: any) => {
        return query && query.error && query.error_description;
    };

    const socialSignUpRequest = (errorResponse: any) => {
        return (
            errorResponse &&
            errorResponse['status'] == 200 &&
            errorResponse['message'] == 'social_signup'
        );
    };

    const setAppPlatform = (userAgent: string) => {
        try {
            if (userAgent.includes('Android'))
                localStorage.setItem('AppPlatform', 'Android');
            else if (/(iPhone|iPad|iPod)/i.test(userAgent))
                localStorage.setItem('AppPlatform', 'iOS');
        } catch (error: any) {
            console.log('error while setting AppPlatform:', error);
        }
    };

    const getUnusualAcitvityUrl = () => {
        return `${defaultRedirectUrl}/${locale}-${selectedStore?.toLowerCase()}${PAGEURLS.SUSPICIOUS_ACTIVITY}`
    }

    const onSocialSignUpSuccess = async (response : any) => {
        try{
            const { authSignup: { data, errors } } = response;

            if(data == null){
                if(errors && errors.length > 0){
                    if(errors[0]['code'] == API_STATUS.REQUEST_BLOCKED){
                        window.location.href = getUnusualAcitvityUrl();
                        return;
                    }
                }
            }

            const { authenticationResult } = data;
            const { accessToken } = authenticationResult || {accessToken : ''}; 
            let platformType = localStorage.getItem('platform') || '';
            if (platformType != PLATFORM_TYPE.APP.toLocaleLowerCase()) {
                const {data: guestSessionExists = {data : true}} = await fetchAPI('/api/gexists');
                if(guestSessionExists){
                    await handleGuestLogout({
                        variables: {
                            user: accessToken,
                        },
                    });
                }
            }

            afterSignInRedirect();
        } catch(error){
            console.log('Error occured in onSocialSignUpSuccess ', error);
        }
    }

    const onSocialSignUpError = (error: any) => {
        console.log('Error occured in onSocialSignUpError ', error);
    }

    const handleRedirectSignIn = async () => {
        let query = router.query;
        const { parsePreSignUpErrorMessage } = await import(
            '@utils/signInSignUpHelper'
        );
        const { SIGN_IN_SLUGS } = await import('@constants/common');

        if (signUpCallback(query)) {
            let errorResponse: any = parsePreSignUpErrorMessage(
                query.error_description.toString()
            );
            if (socialSignUpRequest(errorResponse)) {
                let info = cloneDeep(userInfo);
                info.isSocial = true;
                info.userType = errorResponse['provider'].toLowerCase();
                info.providerId = errorResponse['providerId'];
                info.email = errorResponse['email'];
                info.idp_signature = errorResponse['idpResponse'] || "";
                if (
                    errorResponse['picture'] &&
                    errorResponse['picture'].length &&
                    info.UserAttributes
                ) {
                    info.UserAttributes.picture = errorResponse['picture'];
                }
                if (errorResponse['exists'] == true) {
                    // ## Email exists in Cognito, hence let's link the social account and  Auto Sign in
                    let reqParams = prepareSocialSignUpReqParams(info, "");
                    userSignUp(reqParams, "", locale, userInfo, onSocialSignUpSuccess, onSocialSignUpError);                    
                } else {
                    // ## Email doesn't exist in Cognito
                    if (errorResponse['email']) {
                        dispatch(
                            setPageFlow(PAGE_FLOW.SOCIAL_SIGN_UP_WITH_EMAIL)
                        );
                        info.emailVerified = true;

                        // ## if name is received then let's collect mobile and verify
                        // ## else let's collect data using Add Info form
                        if (
                            errorResponse['name'] &&
                            errorResponse['name'].length
                        ) {
                            if (info.UserAttributes) {
                                info.UserAttributes.name =
                                    errorResponse['name'];
                                    info.UserAttributes.gender = 'male';
                            }
                            dispatch(setUserInfo(info));
                            router.push(
                                `?slug=${SIGN_IN_SLUGS.ADD_MOBILE}`,
                                `${SIGN_IN_SLUGS.ADD_MOBILE}`,
                                { locale, shallow: true }
                            );
                        } else {
                            if (info.UserAttributes) {
                                info.UserAttributes.birthdate = errorResponse[
                                    'birthdate'
                                ]
                                    ? `0000/${errorResponse['birthdate']}`
                                    : '';
                                info.UserAttributes.gender = 'male';
                            }
                            dispatch(setUserInfo(info));
                            router.push(
                                `?slug=${SIGN_IN_SLUGS.ADD_INFO}`,
                                `${SIGN_IN_SLUGS.ADD_INFO}`,
                                { locale, shallow: true }
                            );
                        }
                    } else {
                        // ## haven't received the email from social hence we need to collect and verify email
                        dispatch(setUserInfo(info));
                        dispatch(
                            setPageFlow(PAGE_FLOW.SOCIAL_SIGN_UP_WITHOUT_EMAIL)
                        );
                        router.push(
                            `?slug=${SIGN_IN_SLUGS.ADD_INFO}`,
                            `${SIGN_IN_SLUGS.ADD_INFO}`,
                            { locale, shallow: true }
                        );
                    }
                }
            } else if (userCancelledSocialSignIn(query)) {
                const localStoragePlatform = localStorage.getItem('platform');
                const authSession = localStorage.getItem('authSession') || '';
                router.push(
                    `/login/?platform=${localStoragePlatform}${
                        authSession && authSession.length
                            ? `&auth_session=${authSession}`
                            : ''
                    }`,
                    undefined,
                    { locale }
                );
            } else {
                router.push('/login/', undefined, { locale });
            }
        } else if (socialSignInCallback(query)) {
            setRefreshToken();
        } else {
            // ## Bug fix : EV-3514. Stuck on loading page during back button click
            router.push('/login/', undefined, { locale });
        }
    };

    const afterSignInRedirect = async () => {
        let lrDir = localStorage.getItem('rdir');
        // ## to clear cognito cookies

        if (rdir && rdir.length && isYouGotaGiftDomain(rdir)) {
            router.push(rdir, undefined, { locale });
        } else if (lrDir && lrDir.length && isYouGotaGiftDomain(lrDir)) {
            router.push(lrDir, undefined, { locale });
        } else {
            router.push({ pathname: defaultRedirectUrl });
        }
    };

    const setRefreshToken = async (token?: any) => {
        const platformType = localStorage.getItem('platform') || '';
        const CognitoAPI = await import('@features/common/signInSignUpAPI');
        let user;
        try{
            if(!token){
                user = await CognitoAPI.currentAuthenticatedUser();
            }
        } catch(error){
            console.log('error ', error);
        }
        
        await fetchAPI('/api/clearCookie', {
            method: 'GET',
        });
        let setTokenResponse = await CognitoAPI.setRefreshTokenViaAPI(
            user?.signInUserSession?.refreshToken?.token || token
        );

        if (platformType != PLATFORM_TYPE.APP.toLocaleLowerCase()) {
            const {data: guestSessionExists = {data : true}} = await fetchAPI('/api/gexists');
            if(guestSessionExists){
                await handleGuestLogout({
                    variables: {
                        user: user?.signInUserSession?.accessToken?.token,
                    },
                });
            }
        }
        const profileResponse = await getUserProfileDetails(
            setTokenResponse?.data?.AccessToken
        );

        // ## Google Analitic for login
        loginGA();
        // ## Clevertap
        cleverTapService.pushClevertapProfile(
            profileResponse?.email,
            profileResponse?.name,
            locale
        );
        cleverTapService.pushLoginSignup();

        if (
            platformType == PLATFORM_TYPE.APP.toLocaleLowerCase() &&
            setTokenResponse &&
            setTokenResponse['data'] &&
            setTokenResponse['data']['AuthSignature'] &&
            setTokenResponse['data']['AuthSignature'].length
        ) {
            const url = getCustomSchemaUrl(
                setTokenResponse['data']['AuthSignature']
            );
            const deviceType = localStorage.getItem('deviceType') || '';
            if (deviceType == 'android') {
                setAppRedirectUrl(url);
                setShowRedirectPrompt(true);
            } else {
                window.location.href = url;
                return false;
            }
        } else {
            afterSignInRedirect();
        }
    };

    const handleOnConfirm = () => {
        window.location.href = appRedirectUrl;
        setShowRedirectPrompt(false);
        return false;
    };

    if ((browserBack || redirectPage || !prefillInfo) && !showRedirectPrompt) {
        return <Loader />;
    }

    return (
        <>
            <Meta
                loginPageSiteMeta={metaData?.loginPageSiteMeta}
                activeSlug={activeSlug}
            />
            <LoginLayout
                headerData={headerData}
                slug={activeSlug}
                info={info}
                redirectUrl={redirectUrl}
                prefillInfo={prefillInfo}
                guestSessionExists={guestSessionExists}
                isGuestEnabled={isGuestEnabled}
                ipCountry={ipCountry}
                blacklistedCountries={blacklistedCountries}
            />
            <Confirm
                icon={`${imageBaseUrl}/icons/redirect-arrow.png`}
                open={showRedirectPrompt}
                onClose={handleOnConfirm}
                message={t('youWillBeRedirectedToYougotagift')}
                title={t('redirectingToYougotagift') || ''}
                showCancel={false}
                fullsizeButton={true}
            ></Confirm>
        </>
    );
};

/**
 * Method to check for invalid slug
 * Valid Slugs
 * ** redirect-signin
 * ** change-password
 * @param slug
 * @returns
 */
const isInvalidSlug = (slug: any) => {
    return !['redirect-signin', 'change-password', 'logout'].includes(slug[0]);
};

/**
 * Method to delete cognito cookie on logout
 * @param cookies
 * @param res
 * @returns
 */
const deleteAllCookies = (cookies: any, res: any) => {
    try {
        let arr = [];
        for (const key in cookies) {
            if (key !== SNOW_ENABLED && key !== VOLUME_ENABLED) {
                arr.push(`${key}=;Path=/;MAX-AGE=0;`);
            }
        }
        res.setHeader('set-cookie', arr);
        return res;
    } catch (error) {
        return res;
    }
};

/**
 * Method to redirect to login page
 * @returns
 */
const redirectToLogin = () => {
    return {
        redirect: {
            destination: '/login',
            permanent: false,
        },
    };
};

/**
 * Method to redirect to error page
 * @returns
 */
const redirectTo500Error = () => {
    return {
        redirect: {
            destination: '/error/500',
            permanent: false,
        },
    };
};

const checkIfEmailPrefillRequired = (info: any) => {
    return (
        info['error'] == false &&
        [UserType.AppleUser, UserType.ConventionalUser].includes(info['type'])
    );
};

const userAlreadySignedIn = async (cookies: any) => {
    return (
        cookies &&
        cookies['REFRESH_TOKEN'] &&
        cookies['REFRESH_TOKEN'].length > 0 &&
        (await refreshTokenValid(cookies['REFRESH_TOKEN']))
    );
};

const guestSessionExists = (cookies: any): boolean => {
    return (
        cookies &&
        !!cookies['session_ygag'] &&
        cookies['session_ygag'].length > 0
    );
};

const getCaptchaConfigData = (captchaConfigData : any) =>{
    if(captchaConfigData && captchaConfigData?.captchaConfig?.length){
        return captchaConfigData?.captchaConfig?.find((item: any) => {
            return item?.captchaVersion === "V2"
        });
    }else{
        console.error("Captcha config data is undefined");
    }
}

export async function getServerSideProps({
    locale,
    query,
    res,
    req,
}: {
    locale: any;
    query: any;
    res: any;
    req: any;
}) {
    try {
        const {
            slug,
            app,
            rdir,
            platform,
            email_prefill,
            auth_session,
            store,
            ...otherInfo
        } = query;

        // #. Fetch logo data
        const headerData = await logoDataQuery(locale, platform);

        //# fetch captcha config
        const captchaConfigData = await getCaptchaConfig(locale);

        const captchaConfig = getCaptchaConfigData(captchaConfigData)
       
        const getIPFromRequest = (await import('@utils/getIPFromRequest'))
            .default;

        const ipAddress = await getIPFromRequest(req);
        const { metaData } = await getLoginMetaData(
            store,
            locale,
            platform,
            ipAddress
        );
        // #. Fetch the list of black listed countries
        const {blacklistedCountries} = await getBlacklistedCountries(locale);

        console.log('blacklistedCountries ', blacklistedCountries);

        let isGuestEnabled = false;
        if (platform != PLATFORM_TYPE.APP.toLowerCase()) {
            // ## If the platform isn't web or mobile web, we use 'web' as the default
            const platformType = platform && ['web', 'mweb'].includes(platform.toLowerCase()) ? platform : PLATFORM_TYPE.WEB.toLowerCase()
            isGuestEnabled = await guestLoginEnabled(platformType, locale, store);
        }
        
        const guestSession = guestSessionExists(req.cookies);
        const fernet = new Fernet(fernetKey);
        const userAgent = req.headers['user-agent'];

        if (await userAlreadySignedIn(req.cookies)) {
            let redirectUrl = defaultRedirectUrl;
            if (
                platform == PLATFORM_TYPE.APP.toLowerCase() &&
                auth_session &&
                auth_session.length
            ) {
                // ## this case only occurs for Android
                const url = getCustomSchemaLogoutUrl(
                    req.cookies['REFRESH_TOKEN']
                );
                redirectUrl = url;
            } else if (rdir && rdir.length && isYouGotaGiftDomain(rdir)) {
                redirectUrl = rdir;
            }

            return {
                props: {
                    redirect: true,
                    redirectUrl: redirectUrl,
                },
            };
        }

        let prefillInfo = {
            previousEmail: '',
            prefillRequired: false,
            prefillUserType: UserType.ConventionalUser,
            authCode: '',
        };

        if (email_prefill) {
            let decrptedInfo = '{}';
            try {
                decrptedInfo = fernet.decrypt(email_prefill);
            } catch (error) {
                console.log('Error in fernet.decrypt ', error);
                Sentry.captureMessage("Error in fernet.decrypt");
                console.log(error);
                Sentry.captureException(error);
            }
            const parsedInfo = JSON.parse(decrptedInfo);
            prefillInfo.previousEmail = parsedInfo.email || '';
            prefillInfo.prefillRequired =
                checkIfEmailPrefillRequired(parsedInfo);
            prefillInfo.authCode = parsedInfo.auth_code || '';
            if (parsedInfo.type) {
                prefillInfo.prefillUserType = parsedInfo.type || '';
            }
        }

        if (slug && isInvalidSlug(slug)) {
            return redirectToLogin();
        }

        res = deleteAllCookies(req.cookies, res);

        res.setHeader(
            'Cache-Control',
            'public, s-maxage=300, stale-while-revalidate=59'
        );
        const ipCountry = metaData?.ipCountry || 'AE';

        return {
            props: {
                ...(await serverSideTranslations(locale, ['common'])),
                headerData: headerData || {},
                slug: slug == undefined ? '' : slug,
                info: otherInfo,
                redirectUrl: rdir || '',
                ip: ipAddress,
                prefillInfo,
                authSession: auth_session || '',
                metaData,
                guestSessionExists: guestSession,
                isGuestEnabled,
                ipCountry,
                userAgent,
                isCaptchaRequired:captchaConfig?.hasCaptchaEnabled || false,
                blacklistedCountries
            },
        };
    } catch (error) {
        console.log('Error occured in getServerSideProps ', error);
        Sentry.captureException("Error occured in getServerSideProps ");
        console.log(error);
        Sentry.captureException(error);
        return redirectTo500Error();
    }
}

export default SigninSignup;
