import dynamic from 'next/dynamic';
const LoginLayout = dynamic(
    () => import('@features/login/loginLayout/loginLayout'),
    {
        ssr: false,
    }
);

interface EmailVerificationProps {
    info: any;
    accessToken?: string;
    referenceId?: any;
    userInfo: any;
}

const EmailVerification = ({
    info,
    accessToken,
    referenceId,
    userInfo,
}: EmailVerificationProps) => {
    return (
        <>
            <LoginLayout
                slug={'email-verification'}
                redirectUrl=""
                info={info}
                accessToken={accessToken}
                referenceId={referenceId}
                userInfo={userInfo}
                guestSessionExists={false}
                isGuestEnabled={false}
            />
        </>
    );
};

export async function getServerSideProps({
    locale,
    query,
    res,
    req,
}: {
    locale: any;
    query: any;
    res: any;
    req: any;
}) {
    const { emailVerification, getAuthInfo, getUserProfileDetails } =
        await import('@features/common/signInSignUpAPI');

    const { serverSideTranslations } = await import(
        'next-i18next/serverSideTranslations'
    );

    const refreshToken = req?.cookies?.REFRESH_TOKEN ?? '';
    const token = await getAuthInfo(refreshToken);
    const userInfo = await getUserProfileDetails(token?.accessToken);
    const { slug, ...otherInfo } = query;

    if (userInfo?.secure_access) {
        return {
            redirect: {
                destination: otherInfo?.rdir,
                permanent: false,
            },
        };
    }

    const { reference_id } = await emailVerification(
        userInfo?.email,
        token?.accessToken
    );

    return {
        props: {
            ...(await serverSideTranslations(locale, ['common'])),
            info: otherInfo,
            referenceId: reference_id,
            accessToken: token?.accessToken,
            userInfo: userInfo,
        },
    };
}

export default EmailVerification;
