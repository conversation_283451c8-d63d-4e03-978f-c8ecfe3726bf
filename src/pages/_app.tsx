import '../styles/globals.scss';
import type { AppProps } from 'next/app';
import { appWithTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import getDirection from '@utils/getDirection';
import getConfig from 'next/config';
import { useAppSelector } from '@redux/hooks';
import { selectIsLoading } from '@features/common/commonSlice';
import { wrapper } from '@redux/store';
import { AppRouterContextProvider } from '@features/common/router.context';
import { Amplify } from 'aws-amplify';
import { COGNITO_CONFIG, COGNITO_OAUTH_SCOPES } from '@constants/common';
import Analytics from '../features/common/Analytics/Analytics';
import Hotjar from '@features/common/Analytics/Hotjar';
import Clevertap from '@features/common/clevertap/Clevertap';
import { useApollo } from '@graphql/apolloClient';
import { ApolloProvider } from '@apollo/client';
import React from 'react';
import CaptchaContextProvider from '@features/common/captcha.context';

import { ErrorBoundary } from '@features/common/error';
import Loader from '@features/common/loader/Loader';
import CookieDisabled from '@features/common/cookieDisabled/cookieDisabled';
import cookieIsEnabled from '@utils/cookieEnabled';


function MyApp({ Component, pageProps }: AppProps) {
    const router = useRouter();
    const [locale] = ((router.locale as any) || "en-ae")?.split('-');


    const [appLoaded, setAppLoaded] = useState(false);
    const [cookieEnabled, setCookieEnabled] = useState<any>(undefined);

    const clevertapAccountId =
        pageProps?.commonData?.data?.siteConfigs?.edges[0]?.node
            ?.clevertapAccountId;

    const apolloClient = useApollo(pageProps, locale);

    // #. Get the default loading state
    const isLoading = useAppSelector(selectIsLoading);

    const {
        publicRuntimeConfig: {
            identityPoolId,
            region,
            userPoolId,
            userPoolWebClientId,
            domain,
            s3BucketName,
            redirectURL,
            GTM_ID,
            HOTJAR_ID,
            CLEVERTAP_ACCOUNT_ID,
        },
    } = getConfig();
    // #. changes for making redirectURL dynamic
    const redirectSignOutURL = redirectURL;

    useEffect(() => {
        setAppLoaded(true);
        if(cookieIsEnabled()){
            setCookieEnabled(true);
        } else {
            //window.location.href = `${window.origin}/error/404`
            setCookieEnabled(false);
        }
    },[]);

    useEffect(()=>{
        console.log('Cookie enabled value chabge ', cookieEnabled);
    }, [cookieEnabled]);

    useEffect(() => {
        /* Configuration for AWS Cognito */
        if(cookieEnabled && locale){
            let redirectSignInURL = `${redirectURL}/${locale}/login/redirect-signin/`;
            const awsconfig = {
                Auth: {
                    identityPoolId: identityPoolId,
                    region: region,
                    userPoolId: userPoolId,
                    userPoolWebClientId: userPoolWebClientId,
                    authenticationFlowType: COGNITO_CONFIG.USER_PASSWORD_AUTH_TYPE,
                },
                oauth: {
                    domain: domain,
                    scope: COGNITO_OAUTH_SCOPES,
                    redirectSignIn: `${redirectSignInURL}`,
                    redirectSignOut: redirectSignOutURL,
                    responseType: COGNITO_CONFIG.RESPONSE_TYPE,
                },
                Storage: {
                    AWSS3: {
                        bucket: s3BucketName,
                        region: region,
                    },
                },
                ssr: true,
            };
            Amplify.configure(awsconfig);
        }
        
    }, [locale, cookieEnabled]);

    useEffect(() => {
        document.body.setAttribute('dir', getDirection(locale));
    }, [locale]);

    // Lazyload fix in Safari
    useEffect(() => {
        const scroll = setTimeout(() => {
            window.scrollTo(0, 2);
        }, 1000);

        return () => {
            clearTimeout(scroll);
        };
    }, [locale, region]);

    

    // taking public image config url
    const {
        publicRuntimeConfig: { imageBaseUrl },
    } = getConfig();

    if(cookieEnabled == undefined){
        return(
            <Loader />
        )   
    }

    return (
        <>
            {
                cookieEnabled === true ? <ApolloProvider client={apolloClient}>
                <Analytics id={GTM_ID} />
                <Hotjar id={HOTJAR_ID} />
                <Clevertap clevertapAccountId={CLEVERTAP_ACCOUNT_ID} />
                <AppRouterContextProvider>
                    <CaptchaContextProvider>
                        <ErrorBoundary>
                            <Component {...pageProps} />
                        </ErrorBoundary>
                    </CaptchaContextProvider>
                </AppRouterContextProvider>
                <style jsx global>{`
                    @font-face {
                        font-family: icomoon;
                        src: url('${imageBaseUrl}/fonts/icomoon.eot?pfvxia');
                        src: url('${imageBaseUrl}/fonts/icomoon.eot?pfvxia#iefix')
                                format('embedded-opentype'),
                            url('${imageBaseUrl}/fonts/icomoon.ttf?pfvxia')
                                format('truetype'),
                            url('${imageBaseUrl}/fonts/icomoon.woff?pfvxia')
                                format('woff'),
                            url('${imageBaseUrl}/fonts/icomoon.svg?pfvxia#icomoon')
                                format('svg');
                        font-weight: normal;
                        font-style: normal;
                        font-display: block;
                    }
                    @font-face {
                        font-family: 'Twemoji Country Flags';
                        unicode-range: U+1F1E6-1F1FF, U+1F3F4, U+E0062-E0063,
                            U+E0065, U+E0067, U+E006C, U+E006E, U+E0073-E0074,
                            U+E0077, U+E007F;
                        src: url('${imageBaseUrl}/fonts/TwemojiCountryFlags.woff2')
                            format('woff2');
                    }
                    @font-face {
                        font-family: 'Bricolage Grotesque';
                        src: url('${imageBaseUrl}/fonts/BricolageGrotesque-VariableFont_opsz,wdth,wght .ttf')
                            format('woff2');
                    }
                `}</style>
            </ApolloProvider> : (<CookieDisabled />)
            }
        </>
    );
}
export default appWithTranslation(wrapper.withRedux(MyApp));
