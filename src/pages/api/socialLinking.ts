// pages/api/date.ts
import { linkSocialAccount } from '@features/common/signInSignUpAPI';
import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try{
    const {reqParams, locale} = JSON.parse(req.body);
    let response = await linkSocialAccount(reqParams, locale);
    res.status(200).json(response);
  } catch(error){
    console.log('errr ', error);
    res.status(400).json({error: error});
  } 
}