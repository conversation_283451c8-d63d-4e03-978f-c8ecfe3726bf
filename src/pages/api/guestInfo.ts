// pages/api/date.ts
import { isGuestEmailValid } from '@utils/emailValidation';
import encrypt from '@utils/encrypt';
import { NextApiRequest, NextApiResponse } from 'next'

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try{
    const bodyParams = JSON.parse(req.body);
    const {name , email} = bodyParams;
    if(isGuestEmailValid(email.trim()) && name && name.length <= 26){
      const data = encrypt(name, email);
      res.status(200).json(data);
    } else {
      res.status(400).json('Invalid guest email');
    }
  } catch(error){
    console.log('errr ', error);
    res.status(400).json("Invalid guest info");
  } 
}