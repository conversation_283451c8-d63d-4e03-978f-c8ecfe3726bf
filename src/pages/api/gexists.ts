// pages/api/gexists.ts
import { NextApiRequest, NextApiResponse } from 'next'
import * as Sentry from '@sentry/nextjs';

/**
 * API endpoint to check if Guest session exists or not
 * @param req 
 * @param res 
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        if (req && req.cookies && req.cookies['session_ygag']) {
            res.status(200).send(true);
        } else {
            res.status(200).send(false);
        }
    } catch (error) {
        Sentry.captureException(error);
        res.status(200).send(true);
    }
}