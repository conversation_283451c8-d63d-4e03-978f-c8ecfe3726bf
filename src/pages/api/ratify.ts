// pages/api/date.ts
import { API_STATUS } from '@constants/common';
import { userWithEmailExists } from '@features/common/signInSignUpAPI';
import encrypt from '@utils/encrypt';
import { Fernet } from 'fernet-nodejs';
import { NextApiRequest, NextApiResponse } from 'next'
import getConfig from 'next/config';
const {
    
    serverRuntimeConfig: { fernetKey }
} = getConfig();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try{
    const fernet = new Fernet(fernetKey);
    const bodyParams = JSON.parse(req.body);
    const {email} = bodyParams;
    const response:any = await userWithEmailExists(email.trim());
    
    if(response?.status == 200){
        const info:any = fernet.decrypt(response?.data?.details);
        const parsedInfo = JSON.parse(info);
        res.status(200).json({c : parsedInfo.status});
    } else {
        res.status(response.status).json({c : 0});
    }
  } catch(error){
    console.log('errr ', error);
    res.status(400).json({c : 0});
  } 
}