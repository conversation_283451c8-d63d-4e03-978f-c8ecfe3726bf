import { useMemo } from 'react';
import {
  ApolloClient,
  HttpLink,
  InMemoryCache,
  NormalizedCacheObject,
  ApolloLink,
} from "@apollo/client";
import {
  defaultAPIBaseUrl, guestLoginAPIUrl, ecomUsersAPIUrl,
  atWorkAPIUrl
} from "@constants/apiEndPoints"
import { onError } from "@apollo/client/link/error";
import * as Sentry from '@sentry/nextjs';


export const APOLLO_STATE_PROP_NAME = '__APOLLO_STATE__';

let apolloClient: ApolloClient<NormalizedCacheObject>;

function createApolloClient(locale: string) {
  const defaultGrahpqlEndpoint = new HttpLink({uri : !!locale
      ? `${defaultAPIBaseUrl}/${locale}/api/`
      : `${defaultAPIBaseUrl}/en/api/`});

  const guestSignInGraphQlEndpoint = new HttpLink({uri : !!locale
    ? `${guestLoginAPIUrl}/${locale}/api/`
    : `${guestLoginAPIUrl}/en/api/`, credentials : 'include'}); 
    
  const ecomUsersGraphQlEndpoint = new HttpLink({
      uri: `${ecomUsersAPIUrl}/graphql/`
  });
  
  const atWorkGraphQlEndpoint = new HttpLink({uri : !!locale
    ? `${atWorkAPIUrl}/${locale}/api/`
    : `${atWorkAPIUrl}/en/api/`
  });   

  const defaultAndotherGrahpqlEndpoint = ApolloLink.split(
    (operation) => operation.getContext().clientName === "guestLogin",
    guestSignInGraphQlEndpoint, // <= apollo will send to this if clientName is "guestLogin".
    defaultGrahpqlEndpoint // <= else otherGrahpqlEndpoints will run
  );

  const atWorkGraphqlEndpoint = ApolloLink.split(
    (operation) => operation.getContext().clientName === "at-work",
    atWorkGraphQlEndpoint,
    defaultAndotherGrahpqlEndpoint // <= else otherGrahpqlEndpoints will run
  );

  const currentHttpLink = () => {
    const link = ApolloLink.split(
      (operation) => operation.getContext().clientName === "ecom-users",
      ecomUsersGraphQlEndpoint, // <= apollo will send to this if clientName is "captchaConfig".
      atWorkGraphqlEndpoint // <= else defaultAndotherGrahpqlEndpoint will run
    );

    return link;
  };
      
  return new ApolloClient({
    ssrMode: typeof window === "undefined",
    cache: new InMemoryCache({
      addTypename: false,
    }),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: "network-only",
      },
    },
    headers : {
      'Access-Control-Allow-Origin': '*'
    },
    link: ApolloLink.from([
      onError(({ graphQLErrors, networkError }) => {
        if (networkError) {
          console.log(`[Network error]: ${networkError}`); 
          Sentry.captureException(`[Network error]: ${networkError}`);
        }

        if (graphQLErrors) {
          console.log(`[graphQLErrors error]: ${graphQLErrors}`); 
          Sentry.captureException(`[graphQLErrors error]: ${graphQLErrors}`);
        }
      }),
      currentHttpLink()
    ]),
  });
}

export function initializeApollo(locale: string, initialState = null) {
    const _apolloClient = createApolloClient(locale);
    // For SSG and SSR always create a new Apollo Client
    if (typeof window === 'undefined') return _apolloClient;
    // Create the Apollo Client once in the client
    if (!apolloClient) apolloClient = _apolloClient;

    return _apolloClient;
}

export function useApollo(pageProps: { [x: string]: any }, locale: string) {
  const state = pageProps[APOLLO_STATE_PROP_NAME];
  const store = useMemo(() => initializeApollo(locale, state), [state, locale]);
  return store;
}
