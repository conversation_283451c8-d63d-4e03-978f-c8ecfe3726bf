<svg data-name="Group 45877" xmlns="http://www.w3.org/2000/svg" width="135.283" height="164" viewBox="0 0 135.283 164">
    <defs>
        <clipPath id="m53umvmtya">
            <path data-name="Rectangle 15166" style="fill:none" d="M0 0h135.283v164H0z"/>
        </clipPath>
        <clipPath id="kymp9j1gnb">
            <path data-name="Path 80970" d="M79.156 152.581s16.055 4.11 21.524 4.82c6.455.838 26.017.987 26.017.987-2.967-22.81-8.613-46.11-19.359-51.679a10.909 10.909 0 0 0-5.044-1.344c-13.5 0-20.391 30.607-23.137 47.216" transform="translate(-79.156 -105.365)" style="fill:#ffe8fd"/>
        </clipPath>
        <clipPath id="ten5m9wzpc">
            <path data-name="Rectangle 15168" style="fill:#b408a4" d="M0 0h48.173v86.709H0z"/>
        </clipPath>
        <linearGradient id="6hv3j0tlod" x1="4.803" y1="-105.557" x2="5.804" y2="-105.557" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="red"/>
            <stop offset="1" stop-color="#f94045"/>
        </linearGradient>
    </defs>
    <g data-name="Group 45878">
        <g data-name="Group 45877" style="clip-path:url(#m53umvmtya)">
            <path data-name="Path 80833" d="m136.862 404.34-42.4-16.146 1.788-.85c5.675-3.105 9.008-6.875 9.008-10.937 0-10.584-22.612-19.195-50.405-19.195s-50.404 8.612-50.404 19.196S27.061 395.6 54.854 395.6c12.473 0 23.9-1.736 32.709-4.6l42.167 16.058a11.589 11.589 0 0 0 7.132 0c1.969-.75 1.969-1.966 0-2.716m-82.008-12.886c-21.789 0-39.516-6.751-39.516-15.048s17.727-15.048 39.516-15.048 39.516 6.75 39.516 15.048-17.727 15.048-39.516 15.048" transform="translate(-3.056 -245.389)" style="fill:#ffe8fd"/>
            <path data-name="Path 80834" d="m133.862 396.34-42.4-16.146 1.788-.849c5.675-3.105 9.008-6.876 9.008-10.937 0-10.584-22.612-19.195-50.405-19.195s-50.404 8.611-50.404 19.195S24.061 387.6 51.854 387.6c12.473 0 23.9-1.736 32.709-4.6l42.167 16.058a11.583 11.583 0 0 0 7.132 0c1.969-.75 1.969-1.966 0-2.716M12.339 368.408c0-8.3 17.727-15.048 39.516-15.048s39.515 6.75 39.515 15.048-17.727 15.048-39.516 15.048-39.516-6.751-39.516-15.048" transform="translate(-.995 -239.893)" style="fill:#fff"/>
            <path data-name="Path 80835" d="m133.143 395.121-37.138-14.142-5.261-2-.054.6c4.2-1.993 9.028-4.879 10.612-9.541 1.113-3.276-.109-6.5-2.341-8.972-2.811-3.11-6.7-5.166-10.519-6.767a77.529 77.529 0 0 0-17.181-4.712 123.237 123.237 0 0 0-42.2.278 73.738 73.738 0 0 0-16.6 4.855c-3.685 1.631-7.481 3.747-10.079 6.906a8.881 8.881 0 0 0-2.046 8.195c.972 3.256 3.748 5.709 6.489 7.52a49.625 49.625 0 0 0 14.123 6 103.219 103.219 0 0 0 19.553 3.314c13.913 1.1 28.457.27 41.867-3.816 1.019-.311.857-.454 1.773-.1l2.521.96 8.817 3.358 21.884 8.334c4.724 1.8 10.865 5.27 15.944 2.96 2.017-.917 1.734-2.386-.172-3.216-.4-.174-1.051.4-.518.633.862.376 1.323.463.831 1.34-.376.671-.587.735-1.336.946a10.225 10.225 0 0 1-2.766.33 10.378 10.378 0 0 1-3.4-.553c-.408-.141-.809-.308-1.212-.461l-6.39-2.433-20.17-7.681c-4.633-1.764-9.238-3.721-13.937-5.307-.769-.259-1.193-.015-2.048.245a85.379 85.379 0 0 1-8.494 2.093 117.445 117.445 0 0 1-20.393 2.06c-12.589.226-25.752-1.034-37.612-5.5-5.69-2.166-15.823-6.782-14.677-14.475.489-3.283 3.1-5.822 5.676-7.663A44.736 44.736 0 0 1 20 352.694c12.526-3.724 26-4.6 39-3.782 11.181.707 23.28 2.535 33.2 8.074 3.1 1.729 6.389 4.051 7.9 7.383 1.989 4.378-.69 8.542-4.137 11.255a29.447 29.447 0 0 1-5.685 3.387c-.254.121-.378.475-.054.6l37.137 14.142 5.262 2c.4.154 1.059-.427.518-.633" transform="translate(-.017 -238.991)" style="fill:#b408a4"/>
            <path data-name="Path 80836" d="M35.684 376.681c.081-4.269 4.365-7.059 7.768-8.768 5.707-2.866 12.166-4.272 18.459-5.094a99.215 99.215 0 0 1 23.346-.255 63.861 63.861 0 0 1 19.356 4.74c3.509 1.575 8.1 4.106 9.038 8.21.915 4.011-2.7 7.136-5.761 8.977-5.329 3.2-11.668 4.754-17.748 5.738a97.809 97.809 0 0 1-23.2.882c-7.365-.578-14.912-1.861-21.72-4.834-3.87-1.69-9.442-4.707-9.535-9.6-.009-.49-.929-.366-.921.061.079 4.15 3.865 7 7.179 8.817 5.742 3.148 12.38 4.673 18.813 5.592a99.6 99.6 0 0 0 25.155.339c6.761-.763 13.676-2.156 19.859-5.09 3.51-1.666 7.876-4.287 8.8-8.4.879-3.925-2.272-7.116-5.274-9.088-5.18-3.4-11.5-5.062-17.529-6.151a97.911 97.911 0 0 0-24.94-1.139c-7.833.612-15.959 2.006-23.111 5.4-3.832 1.818-8.856 4.917-8.948 9.719-.009.489.913.368.921-.061" transform="translate(-23.88 -248.197)" style="fill:#b408a4"/>
            <path data-name="Path 80837" d="m280.94 475.867 36.575 13.933a12.374 12.374 0 0 0 3.927.878c1.118.048 7.412-.317 6.175-2.707-.113-.219-.979.1-.907.243 1.209 2.336-5.334 2.164-6.7 1.831a10.086 10.086 0 0 1-1.1-.346c-.69-.253-1.374-.523-2.061-.784l-8.439-3.214-21.424-8.158-5.186-1.983c-.249-.095-.935.277-.855.307" transform="translate(-192.988 -326.677)" style="fill:#b408a4"/>
            <path data-name="Path 80838" d="M99.388 377.054a95.438 95.438 0 0 1 31.469-1.2c7.805.97 16.537 2.734 22.97 7.564.288.216 1.1-.2.707-.492-6.541-4.911-15.412-6.69-23.349-7.677a96.877 96.877 0 0 0-31.947 1.251c-.564.12-.408.677.15.558" transform="translate(-67.929 -257.251)" style="fill:#b408a4"/>
            <path data-name="Path 80839" d="M0 420.887c.052 3.486 2.417 6.3 5.033 8.358 3.939 3.1 8.767 5.041 13.509 6.523 13.562 4.239 28.331 5.111 42.432 4.032a100.846 100.846 0 0 0 23.07-4.262c.521-.167.439-.537-.1-.365-13.821 4.427-28.892 5.316-43.288 4.137a101.546 101.546 0 0 1-19.684-3.428A46.911 46.911 0 0 1 7 429.741c-3.048-2.125-6.036-5.148-6.095-9.1 0-.216-.912-.062-.907.243" transform="translate(0 -288.894)" style="fill:#b408a4"/>
            <path data-name="Path 80840" d="M120.392 262a440.878 440.878 0 0 1 3.284 49.248s-14.353-32.2-28.063-33.893-25.634 26.6-25.634 26.6.737-28.014 1.4-37.329a104.573 104.573 0 0 1 1.47-10.429s16.053 4.153 21.524 4.863c6.454.839 26.017.944 26.017.944" transform="translate(-48.072 -175.992)" style="fill:#facff6"/>
            <path data-name="Path 80841" d="M79.156 152.581c3.069-18.564 11.316-54.613 28.181-45.872 10.746 5.569 16.393 28.869 19.359 51.679 0 0-19.562-.149-26.017-.987-5.468-.71-21.524-4.821-21.524-4.821" transform="translate(-54.376 -72.381)" style="fill:#fff"/>
        </g>
    </g>
    <g data-name="Group 45880">
        <g data-name="Group 45879" style="clip-path:url(#kymp9j1gnb)" transform="translate(24.78 32.984)">
            <path data-name="Path 80842" d="M83.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-81.575 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80843" d="M93.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-88.445 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80844" d="M103.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-95.315 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80845" d="M113.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-102.184 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80846" d="M123.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-109.054 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80847" d="M133.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-115.923 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80848" d="M143.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-122.793 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80849" d="M153.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-129.662 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80850" d="M163.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-136.532 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80851" d="M173.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-143.401 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80852" d="M183.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-150.271 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80853" d="M193.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-157.14 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80854" d="M203.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-164.01 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80855" d="M213.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-170.879 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80856" d="M223.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-177.749 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80857" d="M233.3 135.058a.626.626 0 0 1-.626-.626V131.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-184.618 -122.752)" style="fill:#ffe8fd"/>
            <path data-name="Path 80858" d="M78.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-78.141 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80859" d="M88.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-85.01 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80860" d="M98.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-91.88 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80861" d="M108.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-98.749 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80862" d="M118.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-105.619 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80863" d="M128.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-112.488 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80864" d="M138.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-119.358 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80865" d="M148.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-126.227 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80866" d="M158.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-133.097 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80867" d="M168.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-139.967 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80868" d="M178.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-146.836 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80869" d="M188.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-153.706 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80870" d="M198.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-160.575 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80871" d="M208.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-167.445 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80872" d="M218.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-174.314 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80873" d="M228.3 155.058a.626.626 0 0 1-.626-.626V151.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-181.184 -136.491)" style="fill:#ffe8fd"/>
            <path data-name="Path 80874" d="M83.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-81.575 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80875" d="M93.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-88.445 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80876" d="M103.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-95.315 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80877" d="M113.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-102.184 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80878" d="M123.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-109.054 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80879" d="M133.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-115.923 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80880" d="M143.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-122.793 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80881" d="M153.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-129.662 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80882" d="M163.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-136.532 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80883" d="M173.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-143.401 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80884" d="M183.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-150.271 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80885" d="M193.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-157.14 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80886" d="M203.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-164.01 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80887" d="M213.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-170.879 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80888" d="M223.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-177.749 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80889" d="M233.3 175.058a.626.626 0 0 1-.626-.626V171.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-184.618 -150.23)" style="fill:#ffe8fd"/>
            <path data-name="Path 80890" d="M78.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-78.141 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80891" d="M88.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-85.01 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80892" d="M98.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-91.88 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80893" d="M108.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-98.749 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80894" d="M118.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-105.619 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80895" d="M128.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-112.488 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80896" d="M138.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-119.358 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80897" d="M148.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-126.227 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80898" d="M158.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-133.097 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80899" d="M168.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-139.967 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80900" d="M178.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-146.836 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80901" d="M188.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-153.706 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80902" d="M198.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-160.575 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80903" d="M208.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-167.445 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80904" d="M218.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-174.314 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80905" d="M228.3 195.058a.626.626 0 0 1-.626-.626V191.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-181.184 -163.969)" style="fill:#ffe8fd"/>
            <path data-name="Path 80906" d="M83.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-81.575 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80907" d="M93.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-88.445 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80908" d="M103.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-95.315 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80909" d="M113.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-102.184 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80910" d="M123.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-109.054 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80911" d="M133.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-115.923 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80912" d="M143.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-122.793 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80913" d="M153.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-129.662 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80914" d="M163.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-136.532 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80915" d="M173.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-143.401 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80916" d="M183.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-150.271 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80917" d="M193.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-157.14 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80918" d="M203.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-164.01 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80919" d="M213.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-170.879 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80920" d="M223.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-177.749 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80921" d="M233.3 215.058a.626.626 0 0 1-.626-.626V211.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-184.618 -177.708)" style="fill:#ffe8fd"/>
            <path data-name="Path 80922" d="M78.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-78.141 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80923" d="M88.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-85.01 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80924" d="M98.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-91.88 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80925" d="M108.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-98.749 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80926" d="M118.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-105.619 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80927" d="M128.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-112.488 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80928" d="M138.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-119.358 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80929" d="M148.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-126.227 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80930" d="M158.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-133.097 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80931" d="M168.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-139.967 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80932" d="M178.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-146.836 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80933" d="M188.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-153.706 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80934" d="M198.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-160.575 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80935" d="M208.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-167.445 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80936" d="M218.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-174.314 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80937" d="M228.3 235.058a.626.626 0 0 1-.626-.626V231.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-181.184 -191.447)" style="fill:#ffe8fd"/>
            <path data-name="Path 80938" d="M83.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-81.575 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80939" d="M93.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-88.445 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80940" d="M103.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-95.315 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80941" d="M113.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-102.184 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80942" d="M123.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-109.054 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80943" d="M133.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-115.923 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80944" d="M143.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-122.793 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80945" d="M153.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-129.662 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80946" d="M163.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-136.532 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80947" d="M173.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-143.401 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80948" d="M183.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-150.271 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80949" d="M193.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-157.14 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80950" d="M203.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-164.01 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80951" d="M213.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-170.879 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80952" d="M223.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 1 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-177.749 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80953" d="M233.3 255.058a.626.626 0 0 1-.626-.626V251.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-184.618 -205.186)" style="fill:#ffe8fd"/>
            <path data-name="Path 80954" d="M78.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-78.141 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80955" d="M88.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-85.01 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80956" d="M98.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-91.88 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80957" d="M108.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-98.749 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80958" d="M118.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-105.619 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80959" d="M128.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-112.488 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80960" d="M138.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-119.358 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80961" d="M148.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-126.227 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80962" d="M158.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-133.097 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80963" d="M168.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-139.967 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80964" d="M178.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-146.836 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80965" d="M188.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-153.706 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80966" d="M198.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-160.575 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80967" d="M208.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-167.445 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80968" d="M218.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-174.314 -218.925)" style="fill:#ffe8fd"/>
            <path data-name="Path 80969" d="M228.3 275.058a.626.626 0 0 1-.626-.626V271.3a.626.626 0 0 1 1.252 0v3.13a.626.626 0 0 1-.626.626" transform="translate(-181.184 -218.925)" style="fill:#ffe8fd"/>
        </g>
    </g>
    <g data-name="Group 45882">
        <g data-name="Group 45881" style="clip-path:url(#m53umvmtya)">
            <path data-name="Path 80971" d="m126.836 120.511-4.245-2.924a30.207 30.207 0 0 1-4.25-2.926c-1.076-1.078.063-2 .893-3.1a21.255 21.255 0 0 1 2.424-2.753c3.506-3.3 7.962-4.146 12.3-1.9a17.833 17.833 0 0 1 6.21 5.815c1.074 1.553-.228 2.544-1.388 3.769a19.12 19.12 0 0 1-3.487 2.9 5.526 5.526 0 0 1-3.676 1.141c-1.3-.191-2.482-1.525-2.062-2.768l-2.714 2.746" transform="translate(-80.994 -72.576)" style="fill:#fae8f8"/>
            <path data-name="Path 80972" d="M119.1 262.753a394.493 394.493 0 0 1 3.1 47.8l.913-.154c-1.585-3.551-3.411-7.009-5.323-10.393-3.594-6.361-7.645-12.729-12.9-17.858-4.084-3.984-9.341-7.3-15.187-5.265-6.188 2.152-10.715 8.094-14.176 13.359a81.5 81.5 0 0 0-7 12.924l.895.023c.317-12.031.571-24.086 1.321-36.1a115.766 115.766 0 0 1 1.557-11.661c.076-.463-.851-.352-.92.065-.59 3.569-1.242 7.135-1.492 10.747-.219 3.173-.35 6.354-.481 9.532-.306 7.417-.539 14.837-.754 22.258q-.076 2.61-.146 5.221c-.012.464.76.343.895.023a81.326 81.326 0 0 1 5.091-9.875c3.3-5.547 7.425-11.63 13.187-14.866a10.706 10.706 0 0 1 8.367-1.236 19.649 19.649 0 0 1 7.5 4.757c5.113 4.767 9.051 10.823 12.548 16.828a124.05 124.05 0 0 1 6.118 11.756c.153.343.914.258.913-.154a395.973 395.973 0 0 0-3.116-47.974c-.053-.426-.963-.2-.907.243" transform="translate(-47.062 -175.26)" style="fill:#b408a4"/>
            <path data-name="Path 80973" d="M184.7 123.907c4.585 5.889 6.9 13.172 8.9 20.262a187.512 187.512 0 0 1 5.053 26.318c.051.383.961.165.907-.243a189.477 189.477 0 0 0-5.132-26.6c-1.987-7.043-4.346-14.278-8.9-20.125-.234-.3-1.1.042-.834.387" transform="translate(-126.845 -84.773)" style="fill:#b408a4"/>
            <path data-name="Path 80974" d="M80.143 160.062a172.9 172.9 0 0 1 4.874-20.709c2-6.479 4.472-13.276 8.827-18.584.347-.423-.523-.445-.737-.184-4.233 5.16-6.7 11.658-8.711 17.949a168.851 168.851 0 0 0-5.163 21.629c-.068.38.85.238.911-.1" transform="translate(-54.426 -82.719)" style="fill:#b408a4"/>
            <path data-name="Path 80975" d="M143.967 32.871c0-10.028-4.415-18.157-9.861-18.157s-9.861 8.129-9.861 18.157v1.8c0 5.56.473 9.434 1.3 12.13 1.7 5.542 8.356 7.856 8.356 7.856s6.814-2.27 8.559-7.224c.955-2.712 1.507-6.752 1.507-12.762z" transform="translate(-85.351 -10.108)" style="fill:#fff"/>
            <path data-name="Path 80976" d="M133.946 13.146c2-2.648 1.6-6.639-.462-9.234a11.389 11.389 0 0 0-8.8-3.91 21.939 21.939 0 0 0-9.552 2.469c-1.905.908-3.865 2.095-4.689 4.038a5.989 5.989 0 0 0 1.317 6.189 9.817 9.817 0 0 0 5.913 2.828 28.84 28.84 0 0 0 6.67-.091 11.851 11.851 0 0 0 4.173-.9 4.046 4.046 0 0 0 2.42-3.291z" transform="translate(-75.607)" style="fill:#f5caf1"/>
            <path data-name="Path 80977" d="M143.415 32.071c-.047-5.913-1.433-14.8-7.439-17.78-5.247-2.6-9.539 2.674-11.274 7.04a29.95 29.95 0 0 0-1.928 10.995 62.689 62.689 0 0 0 .667 11.109 12.678 12.678 0 0 0 5.046 8.506c1.023.752 2.785 2.1 4.12 2.23 1.351.135 3.079-1.131 4.169-1.8a12.1 12.1 0 0 0 5.807-8.2 58.44 58.44 0 0 0 .832-12.094c0-.474-.919-.339-.919.069a80.3 80.3 0 0 1-.346 8.989c-.3 2.626-.706 5.544-2.356 7.7a13.771 13.771 0 0 1-4.38 3.558 5.738 5.738 0 0 1-2.389.987 5.668 5.668 0 0 1-2.632-1.153 12.077 12.077 0 0 1-5.827-7.934 47.176 47.176 0 0 1-.87-9.81 41.27 41.27 0 0 1 .693-9.118c.943-4.342 4.226-12.743 10.186-10.824 2.751.886 4.56 3.668 5.687 6.165A28.4 28.4 0 0 1 142.5 32.14c0 .475.922.339.919-.069" transform="translate(-84.339 -9.343)" style="fill:#b408a4"/>
            <path data-name="Path 80978" d="M138.679 136.987a14.778 14.778 0 0 1-4.564 4.045 20.922 20.922 0 0 1-2.3 1.171l-2-3.289c-.127-.208-.6-.442-.82-.25-.183.157-1.658 2.258-2.152 2.761-.294.3-.438.594-.876.419a3.361 3.361 0 0 1-.74-.594q-2.982-2.365-5.971-4.723c-.642-.508-2.167.425-1.352 1.069l7.52 5.948a1.113 1.113 0 0 0 1.417-.251l2-2.53 1.032 1.7c.521.856.737 1.4 1.825 1.122a16.685 16.685 0 0 0 8.468-6.026c.7-.9-.964-1.237-1.483-.566" transform="translate(-80.833 -93.687)" style="fill:#f2f2f2"/>
            <path data-name="Path 80979" d="M48.171 44.416 45.195 48.3l.729-.129-7.3-5.948c-.306-.25-1.1.191-.7.518l7.3 5.948a.6.6 0 0 0 .729-.129l2.977-3.887c.348-.455-.509-.585-.757-.26"/>
            <path data-name="Path 80980" d="m47.982 44.926 2.182 3.69c.064.107.265.1.364.091a9.481 9.481 0 0 0 3.337-1.452 15.9 15.9 0 0 0 5.074-4.54c.26-.345-.657-.3-.851-.045a14.983 14.983 0 0 1-4.146 3.9 18.2 18.2 0 0 1-1.734.9 14.3 14.3 0 0 0-1.31.686c.178.039-.159-.357-.267-.539l-.719-1.216-1.091-1.845c-.167-.283-1.047.017-.839.368"/>
            <path data-name="Path 80981" d="M146.71 96.331c-.962-3.233-5.5-3.049-6.777-.167-.046.1.794.05.892-.171a2.527 2.527 0 0 1 3.444-1.391 3.229 3.229 0 0 1 1.535 1.973c.053.176.957-.076.907-.243" transform="translate(-96.126 -64.542)" style="fill:#b408a4"/>
            <path data-name="Path 80982" d="M127.67 264.031c-7.413.055-14.934.274-22.325-.39a98.511 98.511 0 0 1-13.294-2.341c-2.149-.5-4.287-1.051-6.444-1.516-.572-.123-2.615-.619-2.136.688.337.918 2.547 1.553 3.367 1.843a44.9 44.9 0 0 0 5.4 1.272 88.031 88.031 0 0 0 10.82 1.769c7.656.688 15.442.479 23.12.422.676-.005 2.049-.262 2.412-.927.376-.689-.322-.828-.918-.824" transform="translate(-57.291 -178.329)" style="fill:#ffe8fd"/>
            <path data-name="Path 80983" d="M193.92 288.491a11.475 11.475 0 0 0 4.57 5.581 1.756 1.756 0 0 0 1.955-.151c.437-.345.723-1.085.132-1.458a9.758 9.758 0 0 1-3.936-4.7c-.5-1.261-3.2-.5-2.721.729" transform="translate(-133.175 -197.234)" style="fill:#ffe8fd"/>
            <path data-name="Path 80984" d="M128.375 262.173c-7.173-.1-14.392-.139-21.542-.767-6.947-.61-13.856-2.659-20.6-4.339-.439-.11-2.034.594-1.483.731 6.755 1.682 13.663 3.715 20.613 4.36 7.246.672 14.577.7 21.849.795.413.006 2.065-.768 1.166-.78" transform="translate(-58.138 -176.586)" style="fill:#b408a4"/>
            <path data-name="Path 80985" d="M200.255 287.482a10.692 10.692 0 0 0 4.28 5.178c.225.142 1-.225.853-.316a10.533 10.533 0 0 1-4.226-5.1c-.091-.232-.983.05-.907.243" transform="translate(-137.563 -197.26)" style="fill:#b408a4"/>
            <path data-name="Path 80986" d="M86.32 147.782c-5.527 2.886-6.977 10.7-7.99 16.258a56.824 56.824 0 0 0 1.377 25.276c.1.379 1.012.136.907-.243a55.741 55.741 0 0 1 .549-32.867c1.039-3.01 2.581-6.406 5.541-7.951.551-.287.052-.7-.385-.473" transform="translate(-53.235 -101.474)" style="fill:#b408a4"/>
            <path data-name="Path 80987" d="M87.138 273.781a7.15 7.15 0 0 1-3.156 4.32c-.481.3-.683.933-.174 1.3a1.9 1.9 0 0 0 1.944-.192 9.435 9.435 0 0 0 4.133-5.678c.312-1.212-2.46-.864-2.746.248" transform="translate(-57.368 -187.381)" style="fill:#ffe8fd"/>
            <path data-name="Path 80988" d="m88.1 298.091.074 1.939a3.623 3.623 0 0 0 .037.972c.089.231.393.213.634.139a.714.714 0 0 0 .458-.4 1.21 1.21 0 0 0-.032-.739 8.3 8.3 0 0 1-.264-2.153c-.011-.295-.921-.113-.907.243" transform="translate(-60.522 -204.506)" style="fill:#ffe8fd"/>
            <path data-name="Path 80989" d="m94.373 293.316.939.783a.418.418 0 0 0 .685-.283c.035-.358-.533-.649-.783-.857a.611.611 0 0 0-.6-.011c-.092.039-.4.234-.241.368" transform="translate(-64.798 -201.191)" style="fill:#ffe8fd"/>
            <path data-name="Path 80990" d="M81.8 300.319c-.091.208-.4.663-.386.883.025.339.508.411.71.208a4.339 4.339 0 0 0 .573-1.242c.07-.161-.308-.118-.368-.108-.154.025-.456.092-.529.259" transform="translate(-55.928 -206.119)" style="fill:#ffe8fd"/>
            <path data-name="Path 80991" d="M88.185 273.877a7.922 7.922 0 0 1-3.477 4.788c-.519.327.254.528.578.324a8.658 8.658 0 0 0 3.814-5.209c.1-.371-.823-.246-.912.1" transform="translate(-58.07 -187.919)" style="fill:#b408a4"/>
            <path data-name="Path 80992" d="m86.58 297.239.12 3.132c.011.295.921.113.907-.243l-.12-3.132c-.011-.3-.921-.113-.907.243" transform="translate(-59.476 -203.921)" style="fill:#b408a4"/>
            <path data-name="Path 80993" d="m91.873 293.316.939.783a.611.611 0 0 0 .6.011c.092-.039.4-.234.241-.368l-.939-.783a.611.611 0 0 0-.6-.011c-.092.039-.4.234-.241.368" transform="translate(-63.081 -201.191)" style="fill:#b408a4"/>
            <path data-name="Path 80994" d="m80.061 299.467-.477 1.093c-.07.161.308.118.368.108.154-.026.456-.092.529-.259l.477-1.093c.07-.16-.308-.118-.368-.108-.154.025-.456.092-.529.259" transform="translate(-54.665 -205.534)" style="fill:#b408a4"/>
            <path data-name="Path 80995" d="M206.482 155.746a104.348 104.348 0 0 1 11.084 18.024 103.049 103.049 0 0 1 4.244 10.167c.586 1.646 1.174 3.308 1.635 4.994a11.48 11.48 0 0 1 0 5.178l.882-.273c-.436-.22-.883-.383-1.318-.6-.229-.116-.941.26-.857.3.435.22.883.383 1.318.6.181.091.841-.036.882-.273a11.429 11.429 0 0 0 .073-4.9c-.43-1.689-1.032-3.355-1.612-5a103.324 103.324 0 0 0-15.424-28.463c-.16-.209-.963.17-.907.243" transform="translate(-141.842 -106.78)" style="fill:#b408a4"/>
            <path data-name="Path 80996" d="m262.426 280.38-.763-1.777c-.163-.38-1.061-.116-.907.243l.762 1.777c.163.38 1.062.117.907-.243" transform="translate(-179.115 -191.259)" style="fill:#b408a4"/>
            <path data-name="Path 80997" d="m264.775 280.98-1.043-.73c-.155-.109-.43-.028-.589.033-.059.023-.408.188-.262.29l1.044.73c.155.108.43.028.589-.033.059-.023.408-.187.262-.29" transform="translate(-180.562 -192.482)" style="fill:#b408a4"/>
            <path data-name="Path 80998" d="M167.418 173.729c.168.329.756.3 1.057.326.48.049.962.079 1.445.086.923.013 1.875-.317 2.782-.254a2.011 2.011 0 0 0 .979-.279c.246-.155.156-.425-.141-.418a5.83 5.83 0 0 0-.734.078 10.8 10.8 0 0 1-1.42.182 14.983 14.983 0 0 1-2.739-.243 2.038 2.038 0 0 0-1.03.142c-.165.063-.268.076-.238.259a.405.405 0 0 0 .039.121" transform="translate(-114.978 -118.972)" style="fill:#f2f2f2"/>
            <path data-name="Path 80999" d="M166.531 172.775a21.588 21.588 0 0 0 2.968.265c1.015.012 2.044-.318 3.046-.254.354.023.961-.485.351-.524-.911-.059-1.833.24-2.743.255a18.549 18.549 0 0 1-3.068-.243c-.295-.045-1.067.423-.553.5" transform="translate(-114.281 -118.331)" style="fill:#202124"/>
            <path data-name="Path 81000" d="M176.3 179.641a.783.783 0 1 1-.783-.783.783.783 0 0 1 .783.783" transform="translate(-120.035 -122.867)" style="fill:#202124"/>
        </g>
    </g>
    <g data-name="Group 45883" style="clip-path:url(#ten5m9wzpc)" transform="rotate(8 31.492 560.306)">
        <path data-name="Path 81002" d="M118.719 153.068a.3.3 0 0 0 .181-.058c.093-.107-.112-.32-.166-.428l-.238-.477-.525-1.055-.632-1.27c-.1-.21-.239-.764-.537-.573-.2.126-.373.53-.525.717a8.283 8.283 0 0 1-1.309 1.289c-.172.135-.354.187-.234.415a1.2 1.2 0 0 0 .466.464 6.545 6.545 0 0 0 2.952.96 4.392 4.392 0 0 0 .566.015" transform="translate(-93.521 -121.629)" style="fill:#b408a4"/>
        <path data-name="Path 81003" d="M80.013 220.751a9.568 9.568 0 0 1-2.643-4.319 8.088 8.088 0 0 1-.284-5.129c.716-1.969 2.762-3.209 4.009-4.82A10.691 10.691 0 0 0 83.23 201a18.851 18.851 0 0 0-.786-6.318 15.454 15.454 0 0 0-3.267-5.844c-2.691-3.466-4.628-8.692-1.637-12.583 1.407-1.83 3.143-3.4 4.569-5.223a14.042 14.042 0 0 0 3.083-6.372c.008-.051-.51-.016-.535.143-.738 4.619-4.427 7.714-7.281 11.1a7.471 7.471 0 0 0-1.861 5.284 12.414 12.414 0 0 0 2.2 6.53c1.273 1.876 2.968 3.476 3.762 5.643a21.333 21.333 0 0 1 .823 3.273 18.463 18.463 0 0 1 .454 3.2 10.82 10.82 0 0 1-1.838 6.272c-1.21 1.769-3.256 2.978-4.2 4.929a7.492 7.492 0 0 0 .05 5.3 9.784 9.784 0 0 0 2.749 4.59c.106.09.578-.117.5-.18" transform="translate(-61.57 -134.244)" style="fill:#b408a4"/>
        <path data-name="Path 81004" d="M119.641 153.525c.053.03.1.043.129.025.085-.052.027-.142 0-.208a12.321 12.321 0 0 0-.628-1.262l-.786-1.578c-.086-.173-.2-.628-.442-.471-.161.1-.307.436-.431.59a6.8 6.8 0 0 1-1.077 1.06q-.209.164-.43.312c-.155.1-.05.264.079.292a3.562 3.562 0 0 0 .575-.023 4.5 4.5 0 0 1 1.162.187 4.548 4.548 0 0 1 1.395.693 4.378 4.378 0 0 0 .452.382" transform="translate(-94.504 -122.306)" style="fill:#b408a4"/>
        <path data-name="Path 81005" d="M24.086 0C10.784 0 0 6.386 0 14.262s10.784 14.261 24.086 14.261 24.086-6.385 24.086-14.261S37.389 0 24.086 0m.554 28.034c-12.8 0-23.178-5.961-23.178-13.314S11.839 1.407 24.64 1.407s23.178 5.961 23.178 13.314S37.441 28.035 24.64 28.035" transform="translate(0 -.001)" style="fill:#b408a4"/>
        <path d="M5 1039.968a1 1 0 0 0-.7 1.717l2.676 2.676-2.676 2.676a1 1 0 1 0 1.414 1.414l2.676-2.676 2.678 2.676a1 1 0 1 0 1.414-1.414l-2.676-2.676 2.676-2.676a1 1 0 1 0-1.414-1.414l-2.668 2.675-2.676-2.676a1 1 0 0 0-.724-.302z" style="fill-rule:evenodd;fill:url(#6hv3j0tlod)" transform="rotate(1 59034.696 1428.154)"/>
    </g>
</svg>
