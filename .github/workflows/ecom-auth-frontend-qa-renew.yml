name: Renew QA Deployment ecom-auth-frontend
run-name: Renewing ecom-auth-frontend ${{ inputs.branch }}

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch'
        required: true
        type: string

jobs:
  deploy:
    name: QA
    uses: YouGotaGift/devops-organization-actions/.github/workflows/qa-renew-deployed-app.yml@main
    with:
      application_name: ecom-auth-frontend
      branch: ${{ inputs.branch }}
    secrets: inherit
